import authInstance from '@app/api/authInstance';
import {LOGIN_SERVICE} from '@app/api/instance';

export const getAllPermissionByRoleId = (roleId: number): Promise<ProductAdminPrivilegeResponse> =>
  authInstance
    .get<ProductAdminPrivilegeResponse>(LOGIN_SERVICE + `api/v1/role-permission/role/${roleId}`)
    .then(({data}) => data);

export const productAdminPermissionUpdateByRoleId = (
  payload: ProductAdminPrivilegeByRolePayload,
): Promise<ProductAdminPrivilegeResponse> =>
  authInstance
    .put<ProductAdminPrivilegeResponse>(LOGIN_SERVICE + `api/v1/role-permission`, payload)
    .then(({data}) => data);

export interface ProductAdminPrivilegeResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface ProductAdminPrivilegeByRolePayload {
  id: any;
  status: string;
  roleId: number;
  permissionId: number;
}
