/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {FC, useEffect, useState} from 'react';
import * as S from './PriceCalculator.style';
import {isEmpty} from 'lodash';
import {BsCashStack} from 'react-icons/bs';
import {BASE_COLORS} from '@app/styles/themes/constants';
import {convertNumberFormatWithDecimal} from '@app/utils/utils';
import {Col, Divider, Input, Row, Tag} from 'antd';
import {BaseForm} from '../forms/BaseForm/BaseForm';
import {Checkbox} from '../Checkbox/Checkbox';
import {Option, Select} from '../selects/Select/Select';
import {Button} from '../buttons/Button/Button';
import {Popconfirm} from '../Popconfirm/Popconfirm';
import RefundAmount from './RefundAmount';

interface Props {
  currency: string;
  totalAmount: number;
  isVatApplicable: boolean;
  taxArray: any[];
  totalTaxAmount: number;
  activityDetailsList: any[];
  paymentStatus?: boolean;
  isRetiveAction?: boolean;
  handlePayNow?: (data: any) => void;
  totalDiscountAmount?: number;
  isServiceBooking?: boolean;
  isReadyToPay?: boolean;
  setisReadyToPay?: (checked: boolean) => void;
  paidAmount?: any;
  rowData?: any;

  existingActivities?: any;
  setOutstanding?: (outstanding: number) => void;
  reloadData?: any;
}

const PriceCalculator: FC<Props> = ({
  currency,
  totalAmount,
  taxArray,
  totalTaxAmount,
  activityDetailsList,
  paymentStatus,
  isRetiveAction = false,
  handlePayNow,
  totalDiscountAmount,
  isServiceBooking = false,
  isReadyToPay,
  setisReadyToPay,
  paidAmount,
  rowData,
  existingActivities,
  setOutstanding,
  reloadData,
}) => {
  const [form] = BaseForm.useForm();
  const [paymentMethod, setpaymentMethod] = useState(undefined);
  const [isPayNowEnable, setIsPayNowEnable] = useState(false);

  useEffect(() => {
    const outstandingAmount = parseFloat((Number(totalAmount) - Number(paidAmount)).toFixed(2));
    if (!isNaN(outstandingAmount)) {
      setOutstanding && setOutstanding(outstandingAmount);
    }
  }, [totalAmount, paidAmount, setOutstanding]);

  useEffect(() => {
    if (!isReadyToPay) {
      form.setFieldValue('paymentMethod', undefined);
      setpaymentMethod(undefined);
      setIsPayNowEnable(false);
    }
  }, [isPayNowEnable, paymentMethod, isReadyToPay]);

  useEffect(() => {
    form.resetFields();
    setIsPayNowEnable(false);
  }, [rowData]);

  return (
    <BaseForm form={form}>
      <S.BlurCardWrapper>
        <S.BlueCard>
          <S.Padding>
            <S.PriceWrapper>
              <S.TotalPriceTitle>Total Price</S.TotalPriceTitle>
              <S.TotalPriceTitle>{`${currency} ${
                convertNumberFormatWithDecimal(totalAmount, 0) + '.00'
              }`}</S.TotalPriceTitle>
            </S.PriceWrapper>

            {!isEmpty(activityDetailsList) ? (
              <S.ListTaxWrapper style={{marginTop: '1rem'}}>
                {activityDetailsList.map((tax, idx) => {
                  return (
                    <S.ListTaxRow key={`tax-list${idx}`}>
                      <S.TaxInfoText>{` ${tax.activityName}`}</S.TaxInfoText>
                      {isServiceBooking ? (
                        <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                          tax.totalAmountWithDiscountAndWithoutTax,
                          2,
                        )}`}</S.TaxInfoText>
                      ) : (
                        <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                          tax.totalAmountWithoutTax,
                          2,
                        )}`}</S.TaxInfoText>
                      )}
                    </S.ListTaxRow>
                  );
                })}

                {totalDiscountAmount && totalDiscountAmount > 0 ? (
                  <S.ListTaxRow key={`discount`}>
                    <S.TaxInfoText>{`Total discount`}</S.TaxInfoText>
                    <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                      totalDiscountAmount,
                      2,
                    )}`}</S.TaxInfoText>
                  </S.ListTaxRow>
                ) : null}
              </S.ListTaxWrapper>
            ) : null}

            {!isEmpty(taxArray) ? (
              <S.TaxInformationWapper>
                <S.TaxRightWrapper>
                  <S.TaxInfoText>{`Excludes ${currency} ${convertNumberFormatWithDecimal(
                    totalTaxAmount,
                    2,
                  )} in taxes and charges`}</S.TaxInfoText>
                  <S.ListTaxWrapper>
                    {taxArray.map((tax, idx) => {
                      return (
                        <S.ListTaxRow key={`tax-list${idx}`}>
                          <S.TaxInfoText>{` ${tax.name}`}</S.TaxInfoText>
                          <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                            tax.taxAmount,
                            2,
                          )}`}</S.TaxInfoText>
                        </S.ListTaxRow>
                      );
                    })}
                  </S.ListTaxWrapper>
                </S.TaxRightWrapper>
              </S.TaxInformationWapper>
            ) : null}
            {isRetiveAction && (
              <>
                <Divider />
                <S.ListTaxRow style={{marginTop: '1rem'}}>
                  <S.PaidAmountText>Paid Amount</S.PaidAmountText>
                  {/* <S.PaidAmountText>{`${currency} ${convertNumberFormatWithDecimal(totalAmount, 2)}`}</S.PaidAmountText> */}
                  <S.PaidAmountText>{`${currency} ${convertNumberFormatWithDecimal(paidAmount, 2)}`}</S.PaidAmountText>
                </S.ListTaxRow>
                <S.ListTaxRow style={{marginTop: '1rem', marginBottom: '1rem'}}>
                  <S.DuePaymentText>Due Amount</S.DuePaymentText>
                  <S.DuePaymentText>{`${currency} ${convertNumberFormatWithDecimal(
                    existingActivities?.outStanding,
                    2,
                  )}`}</S.DuePaymentText>
                </S.ListTaxRow>
                <S.ListTaxRow style={{marginTop: '1rem'}}>
                  {/* <S.DuePaymentText>Due Amount</S.DuePaymentText>
                  <S.DuePaymentText>{`${currency} ${convertNumberFormatWithDecimal(
                    // serviceData.outStanding,
                    totalAmount,
                    2,
                  )}`}</S.DuePaymentText> */}
                </S.ListTaxRow>

                {!paymentStatus ? (
                  <>
                    <Row gutter={{xs: 10, md: 15, xl: 30}}>
                      <Col xs={24} sm={24} md={6} xl={12}>
                        <BaseForm.Item
                          valuePropName="checked"
                          name="paidStatus"
                          label="Payment Status"
                          rules={[{required: false, message: 'payment status is required'}]}>
                          <Checkbox
                            onChange={e => {
                              setisReadyToPay && setisReadyToPay(e.target.checked);
                            }}
                          />
                        </BaseForm.Item>
                      </Col>

                      {/* <Col>
                        <BaseForm.Item name="amount" label="Amount">
                          <Input readOnly placeholder="Enter the Amount" prefix={currency} />
                        </BaseForm.Item>
                      </Col> */}

                      <Col xs={24} sm={24} md={12} xl={12}>
                        <BaseForm.Item
                          name="paymentMethod"
                          label="Payment Method"
                          rules={[{required: true, message: 'Payment method is required'}]}>
                          <Select
                            onSelect={(value: any) => {
                              setpaymentMethod(value);
                              setIsPayNowEnable(true);
                            }}
                            size="small"
                            disabled={!isReadyToPay}
                            placeholder="Select payment method">
                            <Option value="CASH">Cash</Option>
                            <Option value="CREDITCARD">Credit card</Option>
                          </Select>
                        </BaseForm.Item>
                      </Col>
                    </Row>
                    <Row justify="end" gutter={{xs: 10, md: 15, xl: 30}}>
                      <Col>
                        <Popconfirm
                          placement="topRight"
                          title="Are you sure to pay now?"
                          onConfirm={() => handlePayNow && handlePayNow(paymentMethod)}
                          okText="Yes"
                          cancelText="No">
                          <Button disabled={!isPayNowEnable} type="primary">
                            Pay Now
                          </Button>
                        </Popconfirm>
                      </Col>
                    </Row>
                    {/* <Row justify="end" gutter={{xs: 10, md: 15, xl: 30}}>
                    <Col>
                      <BaseForm.Item
                        name="amount"
                        label="Amount"
                        rules={[{required: false, message: 'Amount is required'}]}>
                        <Input placeholder="Enter the Amount" />
                      </BaseForm.Item>
                    </Col>
                  </Row> */}
                  </>
                ) : (
                  <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                    <Tag color="green">Amount Paid</Tag>
                  </div>
                )}

                {existingActivities.hasRefund && (
                  <S.PayButtonWrapper>
                    <RefundAmount
                      refundDataDetails={{
                        id: existingActivities?.id,
                        refundAmount: existingActivities?.refund,
                        prefix: currency,
                        // prefix: rowData?.reservationCurrencyId ? 'LKR' : 'USD',
                      }}
                      reloadData={reloadData}
                      existingActivities={existingActivities}
                      registryType="ADDITIONAL_ACTIVITY"
                    />
                  </S.PayButtonWrapper>
                )}
              </>
            )}
          </S.Padding>
        </S.BlueCard>
      </S.BlurCardWrapper>
    </BaseForm>
  );
};

export default PriceCalculator;
