.product-registration {
  width: 100%;
  min-height: 100vh;
  background-color: #f1f1f2;
  font-family: 'Poppins', sans-serif;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

@media (min-width: 768px) {
  .container {
    padding: 3rem 1rem;
  }
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media (min-width: 768px) {
  .content-wrapper {
    flex-direction: row;
  }
}

/* Marketing Content Column */
.marketing-content {
  width: 100%;
}

@media (min-width: 768px) {
  .marketing-content {
    width: 50%;
  }
}

.brand-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.logo {
  background-color: black;
  border-radius: 9999px;
  padding: 0.5rem;
  margin-right: 0.5rem;
}

.brand-name {
  font-weight: 700;
  font-size: 1.125rem;
}

.hero-title {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .hero-title {
    font-size: 3rem;
  }
}

.hero-subtitle {
  color: #4b5563;
  margin-bottom: 1.5rem;
}

/* Rating section */
.rating {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.star {
  width: 1.25rem;
  height: 1.25rem;
  color: #facc15;
}

.rating-text {
  margin-left: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

/* Features grid */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.feature {
  background-color: #e3e4e9;
  padding: 1.5rem;
  border-radius: 0.5rem;
}

.feature-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.feature-icon {
  padding: 0.25rem;
  background-color: #e5e7eb;
  border-radius: 0.375rem;
  margin-right: 0.5rem;
}

.leader-icon {
  padding: 0.25rem;
  background-color: #fee2e2;
  border-radius: 9999px;
  color: #ef4444;
  margin-right: 0.5rem;
}

.rated-icon {
  padding: 0.25rem;
  background-color: #e5e7eb;
  border-radius: 0.375rem;
  color: #f97316;
  margin-right: 0.5rem;
}

.feature-title {
  font-weight: 700;
}

.feature-description {
  color: #4b5563;
  font-size: 0.875rem;
}

/* Testimonial */
.testimonial {
  background-color: #e3e4e9;
  padding: 1rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
}

.testimonial-image {
  border-radius: 9999px;
  width: 4rem;
  height: 4rem;
  margin-right: 1rem;
}

.testimonial-author {
  font-weight: 500;
}

.testimonial-text {
  color: #4b5563;
  font-style: italic;
  font-size: 0.875rem;
}

/* Form Column */
.form-container {
  width: 96%;
}

@media (min-width: 768px) {
  .form-container {
    width: 50%;
  }
}

.form-wrapper {
  margin: 60px 20px;
  background-color: white;
  padding: 2rem;
  border-radius: 2rem;
  box-shadow: 0 4px 6px -2px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 1.25rem;
  font-weight: 700;
}

/* Form elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.form-input,
.form-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background-color: white;
  font-size: 0.875rem;
}

.form-select {
  appearance: none;
  background-color: white;
}

.form-select:focus,
.form-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.select-wrapper {
  position: relative;
}

.button-group {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 1rem;
}

.back-button {
  background-color: #ccc;
}

.info-header {
  background-color: #80f1a6;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.info-header-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(19, 2, 2, 0.05);
}

.info-header-button {
  background-color: #687e6f;
  color: #000;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 50%;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-arrow {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding: 0 0.5rem;
  pointer-events: none;
}

.name-fields {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.error-text {
  color: #ef4444;
  font-size: 0.75rem;
}

/* Phone input styling */
.phone-input {
  display: flex;
}

.country-code {
  width: 4rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem 0 0 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flag {
  display: flex;
  align-items: center;
}

.flag-inner {
  width: 1.5rem;
  height: 1rem;
  background-color: #2563eb;
  border-radius: 0.125rem;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flag-text {
  color: white;
  font-size: 0.75rem;
}

.flag-arrow {
  margin-left: 0.25rem;
}

.phone-input-field {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-left: 0;
  border-radius: 0 0.5rem 0.5rem 0;
  background-color: white;
}

.form-help-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.submit-button {
  width: 100%;
  background-color: #009432;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
}

.submit-button:hover {
  background-color: rgb(3, 87, 31);
}

.button-arrow {
  margin-left: 0.25rem;
}

/* Terms text */
.terms-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 1rem;
  text-align: center;
}

.terms-link {
  color: #2563eb;
  text-decoration: none;
}

.terms-link:hover {
  text-decoration: underline;
}
