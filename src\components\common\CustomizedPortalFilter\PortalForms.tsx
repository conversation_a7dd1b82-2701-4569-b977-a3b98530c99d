/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {useState} from 'react';
import * as S from './PortalFilter.style';
import {DatePicker, Radio, RadioChangeEvent, Select} from 'antd';
import moment from 'moment';
import {DatePickerProps, RangePickerProps} from 'antd/lib/date-picker';
import {Input} from '../inputs/Input/Input';
import {Button} from '../buttons/Button/Button';

const {RangePicker} = DatePicker;

interface IDateRange {
  startDate: string;
  endDate: string;
}

interface IPropsFilter {
  showVatNumberInput: boolean;
  onApply: (data: {
    range: IDateRange;
    vatNumber: string;
    selectDateType: 'SINGLE' | 'RANGE' | undefined;
    dateType: string;
  }) => void;
  loadingButton: boolean;
}

function PortalForms(props: IPropsFilter) {
  const {onApply, loadingButton} = props;
  const [dateType, setDateType] = useState<any>('year');
  const [singleDatePicker, setSingleDatePicker] = useState<moment.Moment | null>(moment());
  const [rangeDatePicker, setRangeDatePicker] = useState<[moment.Moment, moment.Moment] | [null, null]>([null, null]);
  const [selectedDateRange, setSelectedDateRange] = useState<IDateRange>({
    startDate: '',
    endDate: '',
  });
  const [selectDateType, setSelectDateType] = useState<'SINGLE' | 'RANGE' | undefined>('SINGLE');
  const [vatInputValue, setvatInputValue] = useState('');
  const [isAllDate, setIsAllDate] = useState(false);

  const onChange = (e: RadioChangeEvent) => {
    setDateType(e.target.value);
    setSingleDatePicker(null);
    setRangeDatePicker([null, null]);
    setSelectedDateRange({
      startDate: '',
      endDate: '',
    });
    setSelectDateType('SINGLE');
    if (e.target.value === 'all') {
      setIsAllDate(true);
    } else {
      setIsAllDate(false);
    }
  };

  const handleChangeDateSelectType = (value: 'SINGLE' | 'RANGE' | undefined) => {
    setSelectDateType(value);
  };

  const onChangeSingleDate: DatePickerProps['onChange'] = (date: any, dateString) => {
    setSingleDatePicker(date);
    if (date !== null) {
      if (dateType === 'month') {
        const startDate: any = date?.startOf('month').format('YYYY-MM-DD');
        const endDate: any = date?.endOf('month').format('YYYY-MM-DD');
        setSelectedDateRange({
          startDate: startDate,
          endDate: endDate,
        });
      } else if (dateType === 'year') {
        const startDate: any = date?.startOf('year').format('YYYY-MM-DD');
        const endDate: any = date?.endOf('year').format('YYYY-MM-DD');

        const financeYearStartDate = moment(startDate).startOf('year').month(3);
        const financeYearEndDate = moment(endDate).endOf('year').add(1, 'year').month(2);
        const finaceStartDate = financeYearStartDate.format('YYYY-MM-DD');
        const finaceEndDate = financeYearEndDate.format('YYYY-MM-DD');

        setSelectedDateRange({
          startDate: finaceStartDate,
          endDate: finaceEndDate,
        });
      } else if (dateType === 'date') {
        setSelectedDateRange({
          startDate: dateString,
          endDate: dateString,
        });
      } else {
      }
    } else {
      //   setShowReport(false);
      setSelectedDateRange({
        startDate: '',
        endDate: '',
      });
    }
  };

  const onChangeRangeDate: RangePickerProps['onChange'] = (date, dateString) => {
    if (date !== null) {
      setRangeDatePicker(date);
      if (dateType === 'month') {
        const startMonth = date && date[0];
        const endMonth = date && date[1];
        const startDate: any = startMonth?.startOf('month').format('YYYY-MM-DD');
        const endDate: any = endMonth?.endOf('month').format('YYYY-MM-DD');
        setSelectedDateRange({
          startDate: startDate,
          endDate: endDate,
        });
      } else if (dateType === 'year') {
        const startYear = date && date[0];
        const endYear = date && date[1];
        // const startDate: any = startYear?.startOf('year').format('YYYY-MM-DD');
        // const endDate: any = endYear?.endOf('year').format('YYYY-MM-DD');
        const startDate: any = moment(date[0]).startOf('year').month(3).date(1).format('YYYY-MM-DD');
        const endDate: any = moment(date[1]).endOf('year').add(1, 'year').month(2).format('YYYY-MM-DD');

        setSelectedDateRange({
          startDate: startDate,
          endDate: endDate,
        });
      } else if (dateType === 'date') {
        setSelectedDateRange({
          startDate: dateString[0],
          endDate: dateString[1],
        });
      } else {
      }
    } else {
      setRangeDatePicker([null, null]);
      setSelectedDateRange({
        startDate: '',
        endDate: '',
      });
    }
  };

  return (
    <S.SearchWrapper>
      <S.FormFilter style={{justifyContent: 'center'}}>
        <Radio.Group onChange={onChange} value={dateType}>
          <Radio value={'date'}>Date</Radio>
          <Radio value={'month'}>Month</Radio>
          <Radio value={'year'}>Year</Radio>
        </Radio.Group>
      </S.FormFilter>
      <S.FormFilter>
        {dateType !== 'all' && (
          <Select
            placeholder="Type"
            value={selectDateType}
            style={{width: 150}}
            onChange={handleChangeDateSelectType}
            options={[
              {
                value: 'SINGLE',
                label: `Single ${dateType}`,
              },
              {
                value: 'RANGE',
                label: `Range ${dateType}`,
              },
            ]}
          />
        )}
        {selectDateType === 'SINGLE' && dateType !== 'all' ? (
          <DatePicker value={singleDatePicker} showNow onChange={onChangeSingleDate} picker={dateType} />
        ) : (
          dateType !== 'all' && <RangePicker onChange={onChangeRangeDate} picker={dateType} value={rangeDatePicker} />
        )}

        <Input
          value={vatInputValue}
          onChange={e => setvatInputValue(e.target.value)}
          style={{width: 250}}
          placeholder="VAT Number"
        />
        <Button
          size="small"
          disabled={vatInputValue === ''}
          type="primary"
          style={{fontSize: '14px'}}
          onClick={() => {
            onApply({
              range: selectedDateRange,
              vatNumber: vatInputValue,
              selectDateType: selectDateType,
              dateType: dateType,
            });
          }}
          loading={loadingButton}>
          Generate Report
        </Button>
        <Button
          size="small"
          type="ghost"
          style={{fontSize: '14px'}}
          onClick={() => {
            setSelectDateType('SINGLE');
            setSingleDatePicker(moment());
            setDateType('year');
            setSelectedDateRange({
              startDate: '',
              endDate: '',
            });
            setvatInputValue('');
          }}>
          Reset
        </Button>
      </S.FormFilter>
    </S.SearchWrapper>
  );
}

export default PortalForms;
