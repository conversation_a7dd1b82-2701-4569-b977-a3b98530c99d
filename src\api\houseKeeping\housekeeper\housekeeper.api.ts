import authInstance from '@app/api/authInstance';
import houseKeepingInstance from '@app/api/houseKeepingInstance';
import {LOGIN_SERVICE} from '@app/api/instance';
import {ICreatePayload} from '@app/pages/houseKeeping/masterPage/trolley/interface';

export const getAllHouseKeeper = (hotelId: number, serviceId: number): Promise<Response> =>
  authInstance.get<Response>(LOGIN_SERVICE + `api/v1/employee/service?hotelId=${hotelId}&serviceId=${serviceId}`).then(({data}) => data);



export interface Response {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}
export interface FilterProps {
  roomNumber: string;
  unitCode: string;
  viewType: string;
  roomType: string;
  phoneExtention: string;
  roomName: string;
}
