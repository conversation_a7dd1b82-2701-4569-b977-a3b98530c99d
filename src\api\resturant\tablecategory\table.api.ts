// import instance, {RESTAURANT_SERVICE} from '@app/api/instance';

import restInstance, {RESTAURANT_SERVICE} from '@app/api/resturantInstance';

export interface TableCategoryRequest {
  id?: number;
  name: string;
  description?: string;
}

export interface TableCategoryResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export const CreateTable = (tableCategoryPayload: TableCategoryRequest): Promise<TableCategoryResponse> =>
  restInstance
    .post<TableCategoryResponse>(RESTAURANT_SERVICE + 'restaurant-table', {...tableCategoryPayload})
    .then(({data}) => data);

export const getAllTable = (): Promise<TableCategoryResponse> =>
  restInstance.get<TableCategoryResponse>(RESTAURANT_SERVICE + 'restaurant-tables').then(({data}) => data);

export const getAllTableSearch = (
  hotelId?: number,
  hotelServiceId?: number,
  {name}: FilterPropsTable,
  pageSize: number | undefined,
  current: number,
): Promise<TableCategoryResponse> =>
  restInstance
    .get<TableCategoryResponse>(
      RESTAURANT_SERVICE +
        `restaurant-table/search?page=${current}&size=${200}&sortField=id&direction=DESC&name=${
          name ? name : ''
        }&hotelServiceId=${hotelServiceId}`,
    )
    .then(({data}) => data);

export const UpdateTable = (tableCategoryPayload: TableCategoryRequest): Promise<TableCategoryResponse> =>
  restInstance
    .put<TableCategoryResponse>(RESTAURANT_SERVICE + 'restaurant-table', {...tableCategoryPayload})
    .then(({data}) => data);

export const DeleteTable = (id: number): Promise<TableCategoryResponse> =>
  restInstance.delete<TableCategoryResponse>(RESTAURANT_SERVICE + `restaurant-table/${id}`).then(({data}) => data);

export interface FilterPropsTable {
  name: string;
}
