import inventoryInstance, {INVENTORY_SERVICE} from '@app/api/inventoryInstance';

export const CreateSubCategory = (payload: CreateSubCategoryProps): Promise<SubCategoryResponse> => {
  return inventoryInstance.post<SubCategoryResponse>(INVENTORY_SERVICE + 'subCategory', payload).then(({data}) => data);
};

export const UpdateSubCategory = (payload: UpdateSubCategoryProps): Promise<SubCategoryResponse> => {
  return inventoryInstance.put<SubCategoryResponse>(INVENTORY_SERVICE + 'subCategory', payload).then(({data}) => data);
};

export const getAllSubCategories = (
  hotelId: number,
  groupId: number,
  groupInventoryServiceId: number | undefined,
  {name, mainCategoryName}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<SubCategoryResponse> =>
  inventoryInstance
    .get<SubCategoryResponse>(
      INVENTORY_SERVICE +
        `subCategory/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${
          name ? name : ''
        }&mainCategoryName=${
          mainCategoryName ? mainCategoryName : ''
        }&groupId=${groupId}&groupInventoryServiceId=${groupInventoryServiceId}&hotelId=${hotelId}`,
    )
    .then(({data}) => data);

export const DeleteSubCategory = (id: number): Promise<SubCategoryResponse> =>
  inventoryInstance.delete<SubCategoryResponse>(INVENTORY_SERVICE + `subCategory/${id}`).then(({data}) => data);

export interface CreateSubCategoryProps {
  name: string;
  description: string;
  mainCategoryId: number;
}

export interface UpdateSubCategoryProps {
  id: number;
  name: string;
  description: string;
  mainCategoryId: number;
}

export interface SubCategoryResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  name: string;
  mainCategoryName: string;
}
