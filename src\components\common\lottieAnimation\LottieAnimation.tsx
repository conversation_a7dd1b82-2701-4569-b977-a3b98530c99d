import React from 'react';
import <PERSON><PERSON> from 'react-lottie';
import animationData from '../../../assets/json/Animation - 1724132471634.json';

const LottieAnimation = ({loading}: any) => {
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData: animationData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };

  return loading ? <Lottie options={defaultOptions} height={400} width={400} /> : <> </>;
};

export default LottieAnimation;
