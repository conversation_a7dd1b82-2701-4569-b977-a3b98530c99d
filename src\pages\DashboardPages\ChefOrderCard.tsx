import React, {FC, useEffect, useMemo, useRef, useState} from 'react';
import {
  Avatar,
  Card,
  Col,
  Descriptions,
  List,
  Row,
  Space,
  Table,
  Typography,
  Checkbox,
  Divider,
  Segmented,
  Tag,
  Tooltip,
} from 'antd';

import {useStopwatch, useTimer} from 'react-timer-hook';
import {EditOutlined, EllipsisOutlined, SettingOutlined, SyncOutlined} from '@ant-design/icons';
import {FcApproval} from 'react-icons/fc';
import {BiCheckCircle} from 'react-icons/bi';
import ChefOrderTable from './ChefOrderTable';
import {IChefOrderItem} from '@app/api/getOrders.api';
import {ColumnsType} from 'antd/lib/table';
import {Button} from '@app/components/common/buttons/Button/Button';
import {IChefOrder, IItems} from './chefMockData';
import {Option, Select} from '@app/components/common/selects/Select/Select';
import {SegmentedArea} from './DashboardPage.styles';
import {filter} from 'lodash';
import {
  GetResponse,
  PrepareTimeCard,
  PrepareTimeTable,
  ReadyToServe,
  ReadyToServeKot,
  changeStatus,
  readyToServeBot,
  updatePrepareAllOrReadyAllStatus,
} from './chef.api';
import {notificationController} from '@app/controllers/notificationController';
import {ShadowedButton, ShadowedButtonAllocateTime} from '../Restaurant/WaiterDashboard/RightArea';
import {
  OrderCard,
  StatusStamp,
  ItemBill,
  ItemDetailsContainer,
  ItemName,
  BillsContainer,
  ButtonContainer,
  ReadyToServeButton,
  ItemQuantity,
  ActionButton,
  ItemRemarks,
  CardHeader,
  SpinnerContainer,
  StatusWrap,
  ImageContainer,
  ItemRow,
  ActionButtonContainer,
  ReadyToServeAllButton,
  PrepareAllButton,
} from './ChefOrderCard.style';
import AllocateTime from './AllocateTime';

interface IChefOrderCard {
  acceptedItems: IChefOrder[];
  getAllChefOrders: (item: any) => void;
  GetDashboardCounts: () => void;
}

interface IOrderedItemResponse {
  orderedItemStatus: 'READY_TO_SERVE' | 'SERVED' | string;
  tableId?: number;
  [key: string]: any;
}

const {Paragraph, Text} = Typography;
const ChefOrderCard: FC<IChefOrderCard> = ({acceptedItems, getAllChefOrders, GetDashboardCounts}) => {
  const [selectedTableId, setselectedTableId] = useState<number | undefined>(0);
  const [tableFoodTimeAllocation, setTableFoodTimeAllocation] = useState(null);

  const columns: ColumnsType<any> = [
    {
      title: 'Item',
      dataIndex: 'itemName',
      width: 150,
      showSorterTooltip: false,
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      align: 'center',
      showSorterTooltip: false,
    },
    {
      title: 'Comments',
      dataIndex: 'remarks',
      align: 'center',
      width: 150,
      render(value, record, index) {
        return <div style={{width: '150px', textAlign: 'left'}}>{value}</div>;
      },
    },

    {
      title: 'Status',
      key: 'orderedItemStatus',
      align: 'center',
      width: 150,

      render: (_, record: IItems) => (
        <Space size="middle">
          <Tag
            // icon={}
            // pos
            color={
              record.orderedItemStatus === 'ACCEPTED'
                ? 'orange'
                : record.orderedItemStatus === 'BEING_PREPARED'
                ? 'blue'
                : 'green'
            }>
            {record.orderedItemStatus === 'BEING_PREPARED'
              ? 'BEING PREPARED'
              : record.orderedItemStatus === 'READY_TO_SERVE'
              ? 'READY TO SERVE'
              : record.orderedItemStatus}
            {record.orderedItemStatus === 'BEING_PREPARED' ? (
              <SyncOutlined
                style={{
                  marginLeft: '0.5rem',
                }}
                spin
              />
            ) : (
              <></>
            )}
          </Tag>
        </Space>
      ),
    },
    {
      title: 'Actions',
      key: 'action',
      align: 'center',
      width: 150,

      render: (_, record: IItems) => (
        <Space size="middle">
          {record.orderedItemStatus === 'ACCEPTED' ? (
            <ShadowedButton
              size="small"
              onClick={() => {
                handleStatusChange(record.id, 'BEING_PREPARED', record?.kotId);
              }}
              style={{width: '60px', fontSize: '0.8rem', height: '30px', textAlign: 'center'}}
              type="primary">
              Prepare
            </ShadowedButton>
          ) : record.orderedItemStatus === 'BEING_PREPARED' ? (
            <ShadowedButton
              size="small"
              onClick={() => {
                handleStatusChange(record.id, 'READY_TO_SERVE', record?.kotId);
              }}
              style={{
                width: '60px',
                fontSize: '0.8rem',
                height: '30px',
                border: 'none',
                background: '#52c41a',
                textAlign: 'center',
              }}
              type="primary">
              Ready
            </ShadowedButton>
          ) : (
            <BiCheckCircle color="Green" fontSize={24} />
          )}
        </Space>
      ),
    },
  ];

  const handleStatusChange = async (itemId: number, status: string, kotNo?: number) => {
    const result: GetResponse = await changeStatus(itemId, status);

    setselectedTableId(kotNo);
    // tableRef?.current.scrollIntoView({behavior: 'smooth'});
    if (result.statusCode === '20000') {
      // notificationController.success({message: result.message});
      await getAllChefOrders({});
      await GetDashboardCounts();
    } else {
      notificationController.error({message: result.message});
    }
  };

  const readyToserve = async (orderId: number, orderStatus: string, kotId: number | undefined) => {
    const data: any = {
      id: orderId,
      orderStatus: orderStatus,
      kotId: kotId,
    };

    try {
      const result: GetResponse = await ReadyToServeKot(kotId);
      if (result.statusCode === '20000') {
        notificationController.success({message: 'Order Ready to Serve'});
        await getAllChefOrders({});
        await GetDashboardCounts();
      } else {
        notificationController.error({message: result.message});
      }
    } catch (error) {}
  };

  const readyToserveBot = async (orderId: number, orderStatus: string, kotId: number | undefined) => {
    const data: any = {
      id: orderId,
      orderStatus: orderStatus,
      kotId: kotId,
    };

    try {
      const result: GetResponse = await readyToServeBot(kotId);
      if (result.statusCode === '20000') {
        notificationController.success({message: 'Order Ready to Serve'});
        await getAllChefOrders({});
        await GetDashboardCounts();
      } else {
        notificationController.error({message: result.message});
      }
    } catch (error) {}
  };

  const prepareAllOrReadyAll = async (id: number, ticketType: string, orderStatus: string) => {
    try {
      const result: GetResponse = await updatePrepareAllOrReadyAllStatus(id, ticketType, orderStatus);
      if (result.statusCode === '20000') {
        notificationController.success({message: 'Order Ready to Serve'});
        await getAllChefOrders({});
        await GetDashboardCounts();
      } else {
        notificationController.error({message: result.message});
      }
    } catch (error) {}
  };

  function isOdd(num: number) {
    return num % 2 === 1 ? true : false;
  }

  const calculateDisableReadyToServe = (order: IChefOrder): boolean => {
    const totalLength = order.orderedItemResponseList.length;
    const notServeLength = order.orderedItemResponseList.filter(
      (res: IOrderedItemResponse) => res.orderedItemStatus === 'READY_TO_SERVE' || res.orderedItemStatus === 'SERVED',
    ).length;
    return totalLength > notServeLength ? true : order.kotStatus !== 'READY_TO_SERVE' ? false : true;
  };

  const prepareTableData = (order: IChefOrder): IOrderedItemResponse[] => {
    return order.orderedItemResponseList.map((post: IOrderedItemResponse) => ({
      ...post,
      tableId: order.tableId,
    }));
  };

  const renderOrderCard = (
    order: IChefOrder,
    index: number,
    selectedTableId: number | undefined,
    columns: any[],
    readyToserve: (id: number, status: string, kotId: number) => void,
    readyToserveBot: (id: number, status: string, kotId: number) => void,
  ) => {
    const disableReadyToServe = calculateDisableReadyToServe(order);
    const tableData = prepareTableData(order);

    return (
      <Card
        key={index}
        style={{
          width: '100%',
          backgroundColor: !isOdd(index) ? '#e8e6e6' : '#fafafa',
          boxShadow: '0px 0px 8px #ccc',
          marginTop: index === 0 ? 0 : 20,
        }}>
        <div style={{padding: '0px 0px'}}>
          <Row>
            <Col span={3}>Table No : </Col>
            <Col span={3}>{order.tableNumber}</Col>
            <Col span={4}>{order?.kotNumber && order?.kotNumber}</Col>
            <Col span={4}>{order?.botNumber && order?.botNumber}</Col>
            <Col span={2}></Col>
            {order?.orderTime && <Col span={5}>Table Time :</Col>}
            <Col span={3}>{order?.orderTime}</Col>
          </Row>
        </div>
        <div
          ref={ref =>
            selectedTableId === (order.kotId !== undefined ? order.kotId : 0) &&
            ref?.scrollIntoView({behavior: 'smooth'})
          }
          style={{height: 20}}></div>
        <div>
          <Table
            style={{borderRadius: '10px', fontSize: '14px', borderColor: 'black'}}
            pagination={false}
            size="small"
            columns={columns}
            dataSource={tableData}
          />
        </div>
        <div style={{height: 10}}></div>
        <Row>
          <Col span={6}>
            <div style={{width: '100%', justifyContent: 'end'}}>
              <ShadowedButton
                size="small"
                onClick={() => {
                  order.ticketType === 'BOT'
                    ? readyToserveBot(order.id, 'READY_TO_SERVE', order.kotId !== undefined ? order.kotId : 0)
                    : readyToserve(order.id, 'READY_TO_SERVE', order.kotId !== undefined ? order.kotId : 0);
                }}
                disabled={disableReadyToServe}
                style={{
                  width: '75%',
                  backgroundColor: disableReadyToServe ? 'gray' : 'green',
                  fontSize: '1rem',
                  height: '30px',
                  border: 'none',
                  margin: '0.5rem',
                }}
                type="primary">
                Ready to serve
              </ShadowedButton>
            </div>
          </Col>
        </Row>
      </Card>
    );
  };

  const handlePrepareTimeTable = async (formattedTime: string, context?: number) => {
    const payload = {
      id: context,
      prepareTime: formattedTime,
    };
    try {
      const res = await PrepareTimeTable(payload);
      setTableFoodTimeAllocation(res?.result);
      if (res.statusCode === '20000') {
        notificationController.success({message: res.message});
      } else {
        notificationController.error({message: res.message});
      }
    } catch (error) {
      console.error('Failed to update prepare time:', error);
      notificationController.error({message: 'Something went wrong'});
    }
  };

  const handlePrepareTimeCard = async (formattedTime: string, context?: number) => {
    const payload = {
      id: context,
      prepareTime: formattedTime,
    };
    try {
      const res = await PrepareTimeCard(payload);
      if (res.statusCode === '20000') {
        notificationController.success({message: res.message});
      } else {
        notificationController.error({message: res.message});
      }
    } catch (error) {
      console.error('Failed to update prepare time:', error);
      notificationController.error({message: 'Something went wrong'});
    }
  };

  const renderOrderCard1 = (
    order: IChefOrder,
    index: number,
    selectedTableId: number | undefined,
    readyToserve: (id: number, status: string, kotId: number) => void,
    readyToserveBot: (id: number, status: string, kotId: number) => void,
    prepareAllOrReadyAll: (id: number, ticketType: string, orderStatus: string) => void,
  ) => {
    const disableReadyToServe = calculateDisableReadyToServe(order);
    const tableData = prepareTableData(order);
    const isOdd = index % 2 !== 0;

    const orderId = order.id;
    const PrepareTimeForTable = order?.prepareTime;

    return (
      <OrderCard index={index} isOdd={isOdd}>
        <CardHeader>
          <Row style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
            <Col span={6}>Table No : {order.tableNumber}</Col>
            <Col span={6}>{order?.kotNumber && order?.kotNumber}</Col>
            <Col span={6}>{order?.botNumber && order?.botNumber}</Col>
            {order?.orderTime && <Col span={5}>Table Time :</Col>}
            <Col span={3}>{order?.orderTime}</Col>
            {/* <Col span={3}>{tableFoodTimeAllocation?.order?.prepareTime}</Col> */}
          </Row>
        </CardHeader>
        <div
          ref={ref =>
            selectedTableId === (order.kotId !== undefined ? order.kotId : 0) &&
            ref?.scrollIntoView({behavior: 'smooth'})
          }
          style={{height: 20}}></div>

        <BillsContainer>
          {tableData.map((item, itemIndex) => (
            <ItemBill key={itemIndex} status={item.orderedItemStatus}>
              <StatusWrap
                color={
                  item.orderedItemStatus === 'ACCEPTED'
                    ? '#096dd9'
                    : item.orderedItemStatus === 'BEING_PREPARED'
                    ? '#0e7490'
                    : '#16a34a'
                }>
                <StatusStamp
                  color={
                    item.orderedItemStatus === 'ACCEPTED'
                      ? '#096dd9'
                      : item.orderedItemStatus === 'BEING_PREPARED'
                      ? '#0e7490'
                      : '#16a34a'
                  }>
                  {item.orderedItemStatus === 'BEING_PREPARED'
                    ? 'PREPARING'
                    : item.orderedItemStatus === 'READY_TO_SERVE'
                    ? 'READY'
                    : item.orderedItemStatus}
                </StatusStamp>
              </StatusWrap>
              <ImageContainer
                color={
                  item.orderedItemStatus === 'ACCEPTED'
                    ? '#096dd9'
                    : item.orderedItemStatus === 'BEING_PREPARED'
                    ? '#0e7490'
                    : '#16a34a'
                }>
                <img src={item.image} alt={item.itemName} />
              </ImageContainer>
              <ItemDetailsContainer>
                <ItemRow>
                  <ItemName>{item.itemName}</ItemName>
                  {/* Right Side Card Timer */}
                  <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
                    <ItemQuantity
                      statusColor={
                        item.orderedItemStatus === 'ACCEPTED'
                          ? '#096dd9'
                          : item.orderedItemStatus === 'BEING_PREPARED'
                          ? '#0e7490'
                          : '#16a34a'
                      }>
                      x{item.quantity}
                    </ItemQuantity>
                    <AllocateTime
                      mode="Card"
                      context={item.id}
                      onPrepareTime={handlePrepareTimeCard}
                      preparingTime={item.preparingTime}
                    />
                  </div>
                </ItemRow>
                <ItemRemarks>{item.remarks}</ItemRemarks>
              </ItemDetailsContainer>

              {item.orderedItemStatus === 'BEING_PREPARED' && (
                <SpinnerContainer>
                  <SyncOutlined style={{fontSize: '16px'}} spin />
                </SpinnerContainer>
              )}
              <ButtonContainer>
                {item.orderedItemStatus === 'ACCEPTED' ? (
                  <ActionButton
                    size="small"
                    onClick={() => {
                      handleStatusChange(item.id, 'BEING_PREPARED', item?.kotId);
                    }}
                    type="primary">
                    Prepare
                  </ActionButton>
                ) : item.orderedItemStatus === 'BEING_PREPARED' ? (
                  <ActionButton
                    size="small"
                    onClick={() => {
                      handleStatusChange(item.id, 'READY_TO_SERVE', item?.kotId);
                    }}
                    status="READY_TO_SERVE"
                    type="primary">
                    Ready
                  </ActionButton>
                ) : (
                  <div style={{textAlign: 'center'}}>
                    <BiCheckCircle color="Green" fontSize={24} />
                  </div>
                )}
              </ButtonContainer>
            </ItemBill>
          ))}
        </BillsContainer>

        <ActionButtonContainer>
          <div style={{display: 'flex', alignItems: 'center', justifyContent: 'center', top: '-40px'}}>
            {/* Right Side Table Timer */}
            <AllocateTime
              mode="Table"
              context={orderId}
              onPrepareTime={handlePrepareTimeTable}
              preparingTime={PrepareTimeForTable}
            />
          </div>
          <PrepareAllButton
            size="small"
            // disabled={disableReadyToServe}
            onClick={() => {
              order.ticketType === 'BOT'
                ? prepareAllOrReadyAll(order.kotId !== undefined ? order.kotId : 0, 'BOT', 'BEING_PREPARED')
                : prepareAllOrReadyAll(order.kotId !== undefined ? order.kotId : 0, 'KOT', 'BEING_PREPARED');
            }}
            type="primary">
            Prepare All
          </PrepareAllButton>
          <ReadyToServeAllButton
            size="small"
            // disabled={disableReadyToServe}
            // onClick={() => prepareAllOrReadyAll(order.kotId !== undefined ? order.kotId : 0,'KOT', 'READY_TO_SERVE')}
            onClick={() => {
              order.ticketType === 'BOT'
                ? prepareAllOrReadyAll(order.kotId !== undefined ? order.kotId : 0, 'BOT', 'READY_TO_SERVE')
                : prepareAllOrReadyAll(order.kotId !== undefined ? order.kotId : 0, 'KOT', 'READY_TO_SERVE');
            }}
            type="primary">
            Ready All
          </ReadyToServeAllButton>
          <ReadyToServeButton
            size="small"
            onClick={() => {
              order.ticketType === 'BOT'
                ? readyToserveBot(order.id, 'READY_TO_SERVE', order.kotId !== undefined ? order.kotId : 0)
                : readyToserve(order.id, 'READY_TO_SERVE', order.kotId !== undefined ? order.kotId : 0);
            }}
            disabled={disableReadyToServe}
            type="primary">
            Ready to serve
          </ReadyToServeButton>
        </ActionButtonContainer>
      </OrderCard>
    );
  };

  return (
    <div style={{display: 'flex', flexDirection: 'column', justifyContent: 'flex-start'}}>
      {acceptedItems.map((order: IChefOrder, index: number) =>
        // renderOrderCard(order, index, selectedTableId, columns, readyToserve, readyToserveBot),
        renderOrderCard1(order, index, selectedTableId, readyToserve, readyToserveBot, prepareAllOrReadyAll),
      )}
    </div>
  );
};

export default React.memo(ChefOrderCard);
