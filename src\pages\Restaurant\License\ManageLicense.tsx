import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Tables} from '@app/components/tables/Tables/Tables';
import {PageTitle} from '@app/components/common/PageTitle/PageTitle';
import * as S from './License.style';
import {Space} from 'antd';
import {DeleteOutlined, EditOutlined} from '@ant-design/icons';
// import {BASE_COLORS} from '@app/styles/themes/constants';
import {ColumnsType} from 'antd/lib/table';
// import {AddTableCategory} from './AddCategory';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {Button} from '@app/components/common/buttons/Button/Button.styles';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
// import {DeleteTableCategory, getAllTableCategory} from '@app/api/resturant/tablecategory/tableCategory.api';
import {setIsEditAction, setLoading, setModalVisible} from '@app/store/slices/commonSlice';
import {Popconfirm} from '@app/components/common/Popconfirm/Popconfirm';
import {notificationController} from '@app/controllers/notificationController';
// import {AddTableCategory} from '../tableCategory/AddTableCategory';
// import {getAllFoodItems} from '@app/api/resturant/tablecategory/foodItems.api';
import {DeleteLicense, FetchLicenses} from '@app/api/resturant/tablecategory/license.api';
// import {Popover} from '@app/components/common/Popover/Popover';
// import {Avatar} from '@app/components/common/Avatar/Avatar';
// import {Card} from '@app/pages/uiComponentsPages/UIComponentsPage.styles';
// import Meta from 'antd/lib/card/Meta';
// import Paragraph from 'antd/lib/typography/Paragraph';
// import {DeleteCategory} from '@app/api/inventory/category.api';
import {AddCategory} from './AddLicense';
// import {getObjectSignedUrl} from '@app/utils/s3Client';
import {RESTAURANT_ADMIN_MODULE_NAME, modulePermission} from '@app/utils/permissions';
import _ from 'lodash';

export interface ILicenseData {
  id?: number;
  name: string;
  features: string[];
  freeTrail: boolean;
  maximumRoom?: number;
  maximumHotel?: number;
  active: boolean;
  pricePrefix?: string;
  yearPrice?: number;
  type: 'YEAR' | 'MONTH';
  monthPrice?: number;
  perRoomPrice?: number;
  perPropertyPrice?: number;
  description?: string;
  hasTrail?: boolean;
  numberOfDays?: number;
}

const CategoryPage: React.FC = () => {
  const userPermission = useAppSelector(state => state.user.permissions);
  const permissions = modulePermission(userPermission, RESTAURANT_ADMIN_MODULE_NAME.CATEGORY);
  const {t} = useTranslation();
  const [form] = BaseForm.useForm();
  const loading = useAppSelector(state => state.commonSlice.loading);
  const isEditAction = useAppSelector(state => state.commonSlice.isEditAction);
  const isTouch = useAppSelector(state => state.commonSlice.isTouch);
  // const hotelConfig = useAppSelector(state => state.hotelSlice.hotelConfig);
  // const hotelServiceConfig = useAppSelector(state => state.hotelSlice.hotelServiceConfig);
  // const [pagination, setPagination] = React.useState<TablePaginationConfig>({current: 0, pageSize: 10, total: 0});
  const dispatch = useAppDispatch();
  const [tableLicenses, setTableLicenses] = useState([]);
  const [rowData, setRowData] = useState<ILicenseData>({
    id: 0,
    name: '',
    features: [],
    freeTrail: false,
    maximumRoom: 0,
    maximumHotel: 0,
    active: false,
    pricePrefix: '',
    yearPrice: 0,
    type: 'YEAR',
    monthPrice: 0,
    perRoomPrice: 0,
    perPropertyPrice: 0,
    description: '',
    hasTrail: false,
    numberOfDays: 0,
  });
  let [searchObj]: any = useState({});

  // const content = (record: ILicenseData) => {
  //   return (
  //     <div>
  //       <Card
  //         style={{width: 300}}
  //         bodyStyle={{padding: '0px'}}
  //         cover={<img style={{width: 300, height: 300, objectFit: 'cover'}} alt="" src={record.image} />}>
  //         <Meta title={<Paragraph ellipsis={{tooltip: true}} style={{fontSize: '14px', margin: 0}}></Paragraph>} />
  //       </Card>
  //     </div>
  //   );
  // };

  // const urlToFile = async (url: string) => {
  //   const response = await fetch(url);
  //   const blob = await response.blob();
  //   const file = new File([blob], `image.png`, {type: blob.type});
  //   return file;
  // };

  const columns: ColumnsType<ILicenseData> = [
    {
      title: <span style={{fontFamily: 'Poppins, sans-serif'}}>Plan</span>,
      dataIndex: 'name',
      render: (name: string) => <span style={{fontFamily: 'Lora, sans-serif'}}>{name}</span>,
    },
    {
      title: <span style={{fontFamily: 'Poppins, sans-serif'}}>Price (Monthly)</span>,
      dataIndex: 'monthPrice',
      align: 'center',
      render: (_, record) => (
        <span style={{fontFamily: 'Lora, sans-serif'}}>
          {record.pricePrefix}.{record.monthPrice}
        </span>
      ),
    },
    {
      title: <span style={{fontFamily: 'Poppins, sans-serif'}}>Price (Yearly)</span>,
      dataIndex: 'yearPrice',
      align: 'center',
      render: (_, record) => (
        <span style={{fontFamily: 'Lora, sans-serif'}}>
          {record.pricePrefix}.{record.yearPrice}
        </span>
      ),
    },
    {
      title: <span style={{fontFamily: 'Poppins, sans-serif'}}>Features</span>,
      dataIndex: 'features',
      align: 'center',
      render: (features: string[]) => {
        console.log('Features:', features);
        return <span style={{fontFamily: 'Lora, sans-serif'}}>{features.join(', ')}</span>;
      },
    },
    {
      title: <span style={{fontFamily: 'Poppins, sans-serif'}}>Active</span>,
      dataIndex: 'active',
      align: 'center',
      render: (active: boolean) => <span style={{fontFamily: 'Lora, sans-serif'}}>{active ? '✅' : 'None'}</span>,
    },
    {
      title: <span style={{fontFamily: 'Poppins, sans-serif'}}>{t('tables.actions')}</span>,
      dataIndex: 'actions',
      width: '15%',
      render: (text: string, record: ILicenseData) => {
        return (
          <Space>
            <div hidden={!permissions.EDIT}>
              <EditOutlined
                // style={{color: BASE_COLORS.blue}}
                style={{
                  backgroundColor: 'blue',
                  color: 'white',
                  padding: '6px 8px',
                  borderRadius: '4px',
                  boxShadow: 'none',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  marginTop: '10px',
                  marginBottom: '10px',
                }}
                onClick={async () => {
                  dispatch(setModalVisible(true));
                  dispatch(setIsEditAction(true));

                  try {
                    const editData: ILicenseData = {
                      id: record.id,
                      name: record.name,
                      features: record.features,
                      freeTrail: record.freeTrail,
                      maximumRoom: record.maximumRoom,
                      maximumHotel: record.maximumHotel,
                      active: record.active,
                      pricePrefix: record.pricePrefix,
                      yearPrice: record.yearPrice,
                      type: record.type,
                      monthPrice: record.monthPrice,
                      perRoomPrice: record.perRoomPrice,
                      perPropertyPrice: record.perPropertyPrice,
                      description: record.description,
                      hasTrail: record.hasTrail,
                      numberOfDays: record.numberOfDays,
                    };
                    form.setFieldsValue(editData);
                    setRowData(editData);
                  } catch (error) {}
                }}
              />
            </div>
            <div hidden={!permissions.DELETE}>
              <Popconfirm
                title="Are you sure to delete this data?"
                onConfirm={() => {
                  if (record.id !== undefined) {
                    deleteLicense(record.id);
                  }
                }}
                onCancel={() => console.log('onCancel')}
                okText="Yes"
                cancelText="No">
                <DeleteOutlined
                  size={20}
                  style={{
                    backgroundColor: 'red',
                    color: 'white',
                    padding: '6px 8px',
                    borderRadius: '4px',
                    marginTop: '10px',
                    marginBottom: '10px',
                    boxShadow: 'none',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                  }}
                />
              </Popconfirm>
            </div>
          </Space>
        );
      },
      align: 'center',
    },
  ];
  const deleteLicense = (id: number) => {
    DeleteLicense(id).then(res => {
      if (res.statusCode === '20000') {
        notificationController.success({message: res.message});
        listLicenses();
      } else {
        notificationController.error({message: res.message});
      }
    });
  };

  useEffect(() => {
    // getTableCategory();
    listLicenses();
  }, []);

  const listLicenses = async () => {
    try {
      const result: any = await FetchLicenses();
      setTableLicenses(result?.result.license);
      // setPagination({
      //   pageSize: pageSize,
      //   current: result.pagination.pageNumber + 1,
      //   total: result.pagination.totalRecords,
      // });
    } catch (error) {}
  };

  const resetForm = () => {
    form.resetFields();
    dispatch(setLoading(false));
  };

  const onChangeTable = () => {
    // setPagination({
    //   pageSize: pagination.pageSize,
    //   current: pagination.current ? pagination.current - 1 : 0,
    //   total: pagination.total,
    // });
    listLicenses();
  };

  const onChangeTableSearch = (values: any) => {
    const obj: any = {...searchObj, ...values};

    searchObj = obj;
    listLicenses();
  };

  !permissions.DELETE && !permissions.EDIT && _.remove(columns, (col: any) => col.dataIndex === 'actions');

  console.log('====================================');
  console.log('tableLicenses', tableLicenses);
  console.log('====================================');

  return (
    <>
      <PageTitle>{t('common.dataTables')}</PageTitle>
      <S.CurrencyWrapper>
        <S.TableWrapper>
          <Tables
            title="License"
            size="small"
            tableData={tableLicenses}
            isCreate={permissions.ADD}
            onChangeFilter={onChangeTable}
            onChangeSearch={onChangeTableSearch}
            columns={columns}
            showPagination={true}
            // pagination={{
            //   defaultPageSize: 10,
            //   defaultCurrent: 0,
            //   current: pagination.current,
            //   total: pagination.total,
            //   showSizeChanger: true,
            //   pageSizeOptions: ['10', '20'],
            // }}
            modalChildren={<AddCategory reloadData={() => listLicenses()} form={form} rowData={rowData} />}
            onCancelModal={() => resetForm()}
            modalTitle={isEditAction ? 'Update License' : 'Create License'}
            modalFooter={
              <Space>
                <div style={{display: 'inline-flex', gap: 8}}>
                  <Button
                    danger
                    title="Clear"
                    type="ghost"
                    style={{minWidth: 80}}
                    onClick={() => {
                      resetForm();
                    }}>
                    Clear
                  </Button>
                  <Button
                    title=""
                    disabled={isEditAction ? !isTouch : false}
                    type="primary"
                    loading={loading}
                    style={{minWidth: 80}}
                    onClick={() => {
                      form.submit();
                    }}>
                    {isEditAction ? 'Update' : 'Save'}
                  </Button>
                </div>
              </Space>
            }
          />
        </S.TableWrapper>
      </S.CurrencyWrapper>
    </>
  );
};

export default CategoryPage;
