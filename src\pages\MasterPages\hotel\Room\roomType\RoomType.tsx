/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-05-03 16:45:20
 * @modify date 2023-05-03 16:45:20
 * @desc [room type]
 */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Tables} from '@app/components/tables/Tables/Tables';
import {PageTitle} from '@app/components/common/PageTitle/PageTitle';
import * as S from '../Room.style';
import {Card, Col, Image, Row, Space, UploadFile, Popover, Avatar} from 'antd';
import {DeleteOutlined, EditOutlined, FileImageOutlined} from '@ant-design/icons';
import {BASE_COLORS} from '@app/styles/themes/constants';
import {ColumnsType} from 'antd/lib/table';
import {Amenities, RoomAreas, RoomTypeContent} from './RoomTypeContent';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {Popconfirm} from '@app/components/common/Popconfirm/Popconfirm';
import {setIsClear, setIsEditAction, setLoading, setModalVisible} from '@app/store/slices/commonSlice';
import {TablePaginationConfig} from 'antd/es/table';
import {Button} from '@app/components/common/buttons/Button/Button';
import {DeleteRoomType, FilterProps, getAllRoomTypes} from '@app/api/hotel/roomType.api';
import {notificationController} from '@app/controllers/notificationController';
import {IRoomTypeData, IRoomTypeDataRes} from './interface';
import {Tag} from '@app/components/common/Tag/Tag';
import {IconPickerItem} from 'react-fa-icon-picker';

import Meta from 'antd/lib/card/Meta';
import Paragraph from 'antd/lib/typography/Paragraph';
import {getObjectSignedUrl} from '@app/utils/s3Client';
import {getAllAmenities} from '@app/api/hotel/Amenities/amenities.api';
import {HOTEL_SERVICE_MODULE_NAME, modulePermission} from '@app/utils/permissions';
import _ from 'lodash';
import {getAllRoomAreas} from '@app/api/hotel/roomArea.api';

const RoomTypePage: React.FC = () => {
  const {t} = useTranslation();
  const [form] = BaseForm.useForm();
  const [form2] = BaseForm.useForm();
  const loading = useAppSelector(state => state.commonSlice.loading);
  const isEditAction = useAppSelector(state => state.commonSlice.isEditAction);
  const isTouch = useAppSelector(state => state.commonSlice.isTouch);
  const hotelConfig = useAppSelector(state => state.hotelSlice.hotelConfig);
  const [selectedItems, setSelectedItems] = React.useState<any[]>([]);

  const [selectedAmenities, setselectedAmenities] = React.useState<number[]>([]);
  const [selectedAmenitiesEdit, setselectedAmenitiesEdit] = React.useState<Amenities[]>([]);
  const [roomTypes, setRoomTypes] = React.useState([]);
  const [fileList, setFileList] = React.useState<UploadFile[]>([]);
  const [openModel, setOpenModel] = React.useState<boolean>(true);
  const [allAmenities, setAllAmenities] = React.useState<Amenities[]>([]);
  const [allRoomAreas, setAllRoomAreas] = React.useState<RoomAreas[]>([]);
  const [selectedRoomAreas, setselectedRoomAreas] = React.useState<number[]>([]);

  //get permission
  const userPermission = useAppSelector(state => state.user.permissions);
  const permissions = modulePermission(userPermission, HOTEL_SERVICE_MODULE_NAME.ROOM_TYPE);

  const [rowData, setRowData] = React.useState<IRoomTypeDataRes>({
    id: 0,
    roomTypeName: '',
    roomArea: 0,
    numberOfRooms: 0,
    amenities: [],
    apiTypeCode: 0,
    hotelId: 0,
    defaultFileList: [],
    areas: [],
  });
  const dispatch = useAppDispatch();
  const [searchPayload, setsearchPayload] = useState({roomTypeName: ''});

  const content = (record: IRoomTypeData) => {
    return (
      <div>
        <Card
          style={{width: 300}}
          bodyStyle={{padding: '0px'}}
          cover={<img style={{width: 300, height: 300, objectFit: 'cover'}} alt="" src={record.imageUrl} />}>
          <Meta title={<Paragraph ellipsis={{tooltip: true}} style={{fontSize: '14px', margin: 0}}></Paragraph>} />
        </Card>
      </div>
    );
  };

  const [pagination, setPagination] = React.useState<TablePaginationConfig>({current: 0, pageSize: 10, total: 0});
  const columns: ColumnsType<IRoomTypeData> = [
    {
      title: 'Room Type',
      dataIndex: 'roomTypeName',
      width: 200,

      render: (text: string) => <span>{text}</span>,
    },
    {
      title: 'Image',
      dataIndex: 'image',
      align: 'center',
      width: 70,
      render: (_text: string, record: IRoomTypeData) => {
        return (
          <Space>
            <Popover content={() => content(record)} title={record.roomTypeName} trigger="hover" placement="left">
              <Avatar icon={<FileImageOutlined />} src={record.imageUrl} />
            </Popover>
          </Space>
        );
      },
    },

    {
      title: 'Amenities',
      dataIndex: 'amenities',
      width: 250,
      ellipsis: true,
      align: 'center',
      render: (_text: string, record: IRoomTypeData) => (
        <>
          <div
            style={{
              display: 'flex',
              flexWrap: 'wrap',
              justifyContent: 'center',
            }}>
            {record.amenities.map((post: Amenities, i: number) => {
              if (i < 4) {
                return (
                  <div
                    key={i}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-around',
                      alignItems: 'center',
                      // border: '1px solid gray',
                      // borderRadius: 5,
                      // padding: '0px 6px',
                      marginBottom: 2,
                      marginRight: 4,
                    }}>
                    <div
                      style={{
                        paddingTop: 3,
                      }}>
                      <IconPickerItem size={10} icon={post.amenityIcon} />
                    </div>

                    <div
                      style={{
                        fontSize: 10,
                        textOverflow: 'ellipsis',
                      }}>
                      {post.name}
                    </div>
                  </div>
                );
              }
            })}
          </div>
          {record.amenities.length > 5 && (
            <Popover
              placement="right"
              content={
                <Row
                  style={{
                    width: 370,
                  }}
                  gutter={5}>
                  {record.amenities.map((post: Amenities, i: number) => {
                    return (
                      <Col key={i} md={8}>
                        <div
                          style={{
                            display: 'flex',

                            alignItems: 'center',
                            // border: '1px solid gray',
                            // borderRadius: 5,
                            // padding: '0px 6px',
                            width: '100%',
                            marginBottom: 2,
                            marginRight: 4,
                          }}>
                          <div
                            style={{
                              paddingTop: 5,
                            }}>
                            <IconPickerItem size={12} icon={post.amenityIcon} />
                          </div>

                          <div
                            style={{
                              fontSize: 12,
                              textOverflow: 'ellipsis',
                            }}>
                            {post.name}
                          </div>
                        </div>
                      </Col>
                    );
                  })}
                </Row>
              }
              trigger="click">
              <div
                style={{
                  textDecoration: 'underline',
                  fontSize: 11,
                  cursor: 'pointer',
                }}>
                {' '}
                View more
              </div>
            </Popover>
          )}
        </>
      ),
    },
    {
      title: 'Number of Rooms',
      dataIndex: 'numberOfRooms',
      align: 'center',
      width: 150,
    },
    {
      title: 'Beds Code',
      dataIndex: 'apiTypeCode',
      align: 'center',
      width: 100,
    },
    {
      title: 'Average Room Area',
      dataIndex: 'roomArea',
      align: 'center',
      width: 150,
    },
    {
      title: 'Room Areas',
      align: 'center',
      width: 150,
      render: (_text: string, record: IRoomTypeData) => {
        return (
          <>
            <div
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                justifyContent: 'center',
              }}>
              {record.areas &&
                record.areas.map((post: RoomAreas, i: number) => {
                  return (
                    <div
                      key={i}
                      style={{
                        display: 'flex',
                        justifyContent: 'space-around',
                        alignItems: 'center',
                        border: '1px solid gray',
                        borderRadius: 5,
                        padding: '0px 6px',
                        marginBottom: 2,
                        marginRight: 4,
                      }}>
                      <div
                        style={{
                          fontSize: 11,
                          textOverflow: 'ellipsis',
                        }}>
                        {`${post.name ? post.name : ''}
                        `}
                      </div>
                    </div>
                  );
                })}
            </div>
          </>
        );
      },
    },
    {
      title: t('tables.actions'),
      dataIndex: 'actions',
      width: 80,
      align: 'center',

      render: (_text: string, record: IRoomTypeData) => {
        return (
          <Space>
            <div hidden={!permissions.EDIT}>
              <EditOutlined
                style={{color: BASE_COLORS.blue}}
                onClick={async () => {
                  dispatch(setModalVisible(true));
                  dispatch(setIsEditAction(true));
                  try {
                    const str: any = `RoomTypes/${record?.imageName}`;
                    const imageUrl = record?.imageUrl;

                    const file = await urlToFile(imageUrl);
                    const fileObj = {
                      uid: `${1}`,
                      name: `image${1}.png`,
                      status: 'done',
                      url: imageUrl,
                      thumbUrl: imageUrl,
                      originFileObj: file,
                    };

                    const editedData = {
                      amenities: record?.amenities,
                      apiTypeCode: record?.apiTypeCode,
                      roomArea: record?.roomArea,
                      hotelName: record?.hotelName,
                      id: record.id,
                      image: fileObj,
                      numberOfRooms: record?.numberOfRooms,
                      roomTypeName: record?.roomTypeName,
                      url: record?.imageUrl,
                      areas: record?.areas,
                    };

                    form.setFieldsValue(editedData);
                    setRowData(editedData);
                    const amenityIds: number[] = [];

                    record.amenities.map((post: Amenities, i: number) => amenityIds.push(post.id));
                    setselectedAmenitiesEdit(record.amenities);
                    setselectedAmenities(amenityIds);
                    setSelectedItems(record.amenities);
                  } catch (error) {}
                }}
              />
            </div>
            <div hidden={record.roomTypeName === 'Villa' ? true : !permissions.DELETE}>
              <Popconfirm
                title="Are you sure to delete this data?"
                onConfirm={() => {
                  deleteRoomType(record.id);
                }}
                onCancel={() => console.log('onCancel')}
                okText="Yes"
                cancelText="No">
                <DeleteOutlined style={{color: BASE_COLORS.red}} />
              </Popconfirm>
            </div>
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    listRoomTypes(searchPayload, pagination.pageSize, 0);
  }, []);

  const urlToFile = async (url: string) => {
    const response = await fetch(url);
    const blob = await response.blob();
    const file = new File([blob], `image.png`, {type: blob.type});
    return file;
  };

  const deleteRoomType = async (id: number) => {
    try {
      const result = await DeleteRoomType(id);
      if (result.statusCode === '20000') {
        notificationController.success({message: result.message});
        listRoomTypes(searchPayload, pagination.pageSize, pagination.current ? pagination.current - 1 : 0);
      } else {
        notificationController.error({message: result.message});
      }
    } catch (error) {}
  };

  const listRoomTypes = async (searchQuery: FilterProps, pageSize: number | undefined, current: number) => {
    try {
      const result: any = await getAllRoomTypes(hotelConfig?.hotelId, searchQuery, pageSize, current);
      setPagination({
        pageSize: pageSize,
        current: result.pagination.pageNumber + 1,
        total: result.pagination.totalRecords,
      });
      setRoomTypes(result?.result?.roomType);
    } catch (error) {}
  };

  const getAllAmentities = async () => {
    try {
      const result: any = await getAllAmenities();
      // setPax(result?.result?.amenity);
      setAllAmenities(result?.result?.amenity);
    } catch (error: any) {}
  };

  const getAllRoomArea = async () => {
    try {
      const result: any = await getAllRoomAreas(hotelConfig.hotelId);
      setAllRoomAreas(result?.result?.area);
    } catch (error: any) {}
  };

  const resetForm = () => {
    form.resetFields();
    dispatch(setLoading(false));
    setFileList([]);
    setSelectedItems([]);
    setselectedAmenities([]);
    getAllAmentities();
    getAllRoomArea();
    form2.resetFields();
  };

  const handlePagination = (pagination: TablePaginationConfig) => {
    setPagination({
      pageSize: pagination.pageSize,
      current: pagination.current ? pagination.current - 1 : 0,
      total: pagination.total,
    });
    listRoomTypes(searchPayload, pagination.pageSize, pagination.current ? pagination.current - 1 : 0);
  };

  const onChangeTableSearch = (values: any) => {
    const obj = {...searchPayload, ...values};
    setsearchPayload(obj);
    listRoomTypes(obj, pagination.pageSize, 0);
  };

  !permissions.DELETE && !permissions.EDIT && _.remove(columns, (col: any) => col.dataIndex === 'actions');

  return (
    <>
      <PageTitle>{t('common.dataTables')}</PageTitle>
      <S.CurrencyWrapper>
        <S.TableWrapper>
          <Tables
            title="Room Type"
            tableData={roomTypes}
            columns={columns}
            searchFields={['roomTypeName']}
            onChangeFilter={handlePagination}
            onChangeSearch={onChangeTableSearch}
            modalChildren={
              <RoomTypeContent
                form2={form2}
                fileList={fileList}
                selectedItems={selectedItems}
                setSelectedItems={setSelectedItems}
                selectedAmenities={selectedAmenities}
                setselectedAmenities={setselectedAmenities}
                allAmenities={allAmenities}
                setAllAmenities={setAllAmenities}
                allRoomAreas={allRoomAreas}
                setAllRoomAreas={setAllRoomAreas}
                selectedRoomAreas={selectedRoomAreas}
                setselectedRoomAreas={setselectedRoomAreas}
                // selectedAmenitiesEdit={selectedAmenitiesEdit}
                // setselectedAmenitiesEdit={setselectedAmenitiesEdit}
                setFileList={setFileList}
                form={form}
                openModel={openModel}
                setOpenModel={setOpenModel}
                rowData={rowData}
                reloadData={() =>
                  listRoomTypes(searchPayload, pagination.pageSize, pagination.current ? pagination.current - 1 : 0)
                }
              />
            }
            isCreate={permissions.ADD}
            modalSize="large"
            onCancelModal={() => resetForm()}
            modalTitle={isEditAction ? 'Update Room Type' : 'Create Room Type'}
            pagination={{
              defaultPageSize: 10,
              defaultCurrent: 0,
              current: pagination.current,
              total: pagination.total,
              showSizeChanger: true,
              pageSizeOptions: ['10', '20'],
            }}
            modalFooter={
              <Space>
                {isEditAction ? (
                  <Button
                    danger
                    title="Cancel"
                    type="ghost"
                    onClick={() => {
                      dispatch(setIsEditAction(false));
                      dispatch(setModalVisible(false));
                    }}>
                    Cancel
                  </Button>
                ) : (
                  <Button
                    danger
                    title="Clear"
                    type="ghost"
                    onClick={() => {
                      resetForm();
                      dispatch(setIsClear(true));
                    }}>
                    Clear
                  </Button>
                )}
                <Button
                  title=""
                  disabled={isEditAction ? !isTouch : false}
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    form.submit();
                  }}>
                  {isEditAction ? 'Update' : 'Save'}
                </Button>
              </Space>
            }
          />
        </S.TableWrapper>
      </S.CurrencyWrapper>
    </>
  );
};

export default RoomTypePage;
