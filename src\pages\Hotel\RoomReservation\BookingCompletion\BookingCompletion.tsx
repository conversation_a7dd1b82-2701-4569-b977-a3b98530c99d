/* eslint-disable @typescript-eslint/ban-ts-comment */
import React, {useEffect, useState} from 'react';
import * as S from './BookingCompletion.style';
import {Col, Row, Select, TimePicker} from 'antd';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {Input} from '@app/components/common/inputs/Input/Input.styles';
import {Button} from '@app/components/common/buttons/Button/Button';
import {CARD, MONEY} from '@app/assets';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {setCurrent} from '@app/store/slices/reservationSlice';
import {
  calculateTotalNights,
  commonNames,
  convertNumberFormat,
  convertNumberFormatWithDecimal,
  formatDate,
  roundNumberWithDecimal,
} from '@app/utils/utils';
import {ICreatePayload, IReservedRooms} from '../interface/interface';
import {cancelBlockedReservation, createReservation} from '@app/api/hotel/reservation/reservation.api';
import {Popconfirm} from '@app/components/common/Popconfirm/Popconfirm';
import {notificationController} from '@app/controllers/notificationController';
import {flattenDeep, isEmpty} from 'lodash';
import {useNavigate} from 'react-router-dom';
import {BASE_COLORS, FONT_SIZE} from '@app/styles/themes/constants';
import {BsCashStack} from 'react-icons/bs';
import {StayTypeTitle} from '@app/components/common/StayTypeTitle/StayTypeTitle';
import dayjs from 'dayjs';
import {Radio, RadioGroup} from '@app/components/common/Radio/Radio';
import styled from 'styled-components';
import {DatePicker} from '@app/components/common/pickers/DatePicker';
import moment from 'moment';
import {MdOutlineDiscount} from 'react-icons/md';
import {StayTypeExtraChild} from '@app/components/common/StayTypeTitle/StayTypeExtraChild';
import {useMediaQuery} from 'react-responsive';
import DialogBox from '@app/components/common/DialogBox/DialogBox';
import {useCallbackPrompt} from '@app/hooks/useCallbackPrompt';
import {calculateTotalTaxWithSequence, roundNumber} from '@app/utils/functions';
import {useTranslation} from 'react-i18next';
import {BankOutlined} from '@ant-design/icons';
import CurrencyInput from '@app/components/common/inputs/CurrencyInput/CurrencyInput';

const {Option} = Select;

export const BOOKING_TYPES_OPTIONS = [
  {
    title: 'Agent',
    value: 'AGENT',
  },
  {
    title: 'Local',
    value: 'LOCAL',
  },
  {
    title: 'OTA',
    value: 'OTA',
  },
  {
    title: 'Website',
    value: 'WEB_SITE',
  },
  {
    title: 'Event',
    value: 'EVENT',
  },
  {
    title: 'Tribe',
    value: 'TRIBE',
  },
  {
    title: 'Mobile',
    value: 'MOBILE',
  },
];

export default function BookingCompletion() {
  const [form] = BaseForm.useForm();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const {t} = useTranslation();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [isLoading, setIsLoading] = useState(false);
  const [paymentType, setpaymentType] = useState<'CASH' | 'CREDITCARD'>('CASH');
  const [onlinePayment, setonlinePayment] = useState('no');
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [loadCancellingReservation, setloadCancellingReservation] = useState(false);
  const [balanceAmount, setBalanceAmount] = useState<number>(0);
  const {selectedRooms} = useAppSelector(state => state.reservationSlice);
  const {hotelId} = useAppSelector(state => state.hotelSlice.hotelConfig);
  const [showPrompt, confirmNavigation, cancelNavigation] = useCallbackPrompt(showDialog);

  const CustomDatePicker = styled(DatePicker)`
    width: 100%;
  `;
  const CustomTimePicker = styled(TimePicker)`
    width: 100%;
  `;

  const currency = selectedRooms && selectedRooms.stayTypes[0].roomDetails.priceType;
  const taxList = selectedRooms && selectedRooms.stayTypes[0].roomDetails.taxList;

  // Media Queries
  const isTabletOrMobile = useMediaQuery({query: '(max-width: 1224px)'});

  const calculateTotalDeduction = () => {
    return selectedRooms?.allCreditBalanceReponse
      .filter((balance: any) => selectedRooms?.selectedCreditBalances.includes(balance.id))
      .reduce((sum: any, balance: any) => sum + balance.amount, 0);
  };

  const invoiceCreditAmountRequestList = selectedRooms?.allCreditBalanceReponse
    .filter((balance: any) => selectedRooms?.selectedCreditBalances.includes(balance.id))
    .map((credit: {id: any; balanceAmount: any}) => {
      return {
        id: credit.id,
        creditAmount: credit.balanceAmount,
      };
    });

  const calculateGrandTotal = () => {
    const totalPrice = calculateTotalPrice() + taxAmount;
    const totalDeduction = calculateTotalDeduction();
    const adjustedPrice = Math.max(0, totalPrice - totalDeduction); // Ensure price doesn't go below 0
    return adjustedPrice;
  };

  useEffect(() => {
    !selectedRooms.stayTypes[0].roomDetails.paymentRequest && setonlinePayment('no');
    const totalAmount = calculateGrandTotal();
    form.setFieldValue('needToPayAdvance', Math.round(totalAmount).toFixed(2));
  }, []);

  useEffect(() => {
    setShowDialog(true);
  }, []);

  useEffect(() => {
    if (onlinePayment === 'no') {
      const totalAmount = calculateTotalPrice() + Math.round(taxAmount * 100) / 100;
      form.setFieldValue('amount', Math.round(calculateGrandTotal()).toFixed(2));
    } else {
      const totalAmount = calculateTotalPrice() + Math.round(taxAmount * 100) / 100;
      form.setFieldValue('needToPayAdvance', Math.round(calculateGrandTotal()).toFixed(2));
    }
  }, [onlinePayment]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleChangePaymentType = (value: any) => {
    setpaymentType(value);
  };

  const calculateDiscountPrice = (): number => {
    let totalPrice = 0;

    if (selectedRooms && selectedRooms.stayTypes) {
      const payableRooms = selectedRooms.stayTypes.filter(reserveRoom => {
        const {roomDetails} = reserveRoom;
        return roomDetails;
      });

      totalPrice = payableRooms.reduce((total, reserveRoom) => {
        const {roomDetails}: any = reserveRoom;
        const discountAmount = parseFloat(roomDetails.discountAmount || 0);
        return (totalPrice += discountAmount);
      }, 0);
    }
    totalPrice = Math.max(totalPrice, 0);
    return totalPrice;
  };

  const calculateTotalPrice = (): number => {
    let totalPrice = 0;
    const numberOfNights = calculateTotalNights(
      selectedRooms && selectedRooms.checkedIn,
      selectedRooms && selectedRooms.checkedOut,
    );

    if (selectedRooms && selectedRooms.stayTypes) {
      const payableRooms = selectedRooms.stayTypes.filter(reserveRoom => {
        const {roomDetails} = reserveRoom;
        return roomDetails.payable === true;
      });

      const roomsWithDiscount: any = selectedRooms.stayTypes.filter(reserveRoom => {
        const {roomDetails} = reserveRoom;
        return roomDetails.discountAmount > 0;
      });

      totalPrice = payableRooms.reduce((total, reserveRoom) => {
        const {roomDetails} = reserveRoom;
        const roomPricewithExtraChild =
          roomDetails.roomPrice + (isNaN(roomDetails.totalExtraChildAmount) ? 0 : roomDetails.totalExtraChildAmount);
        const roomPriceNumber = parseFloat(roomPricewithExtraChild);
        const discountAmount = parseFloat(roomDetails.discountAmount) || 0;
        const discountedPrice = Math.max(roomPriceNumber - discountAmount, 0);
        return (totalPrice += discountedPrice);
      }, 0);

      // if (payableRooms.length > 0) {
      //   const extraChildPriceSum = payableRooms.reduce(
      //     (total, extraChild) =>
      //       total +
      //       (isNaN(extraChild.roomDetails.totalExtraChildAmount) ? 0 : extraChild.roomDetails.totalExtraChildAmount),
      //     0,
      //   );

      //   for (const stayTypeIndex in selectedRooms?.extraChildCountInRooms) {
      //     if (selectedRooms?.extraChildCountInRooms.hasOwnProperty(stayTypeIndex)) {
      //       const discountRoomAtIndex = roomsWithDiscount[stayTypeIndex];

      //       if (discountRoomAtIndex) {
      //         const discountRoomIndex = discountRoomAtIndex.roomDetails.roomKey;

      //         const extraChildCount = selectedRooms?.extraChildCountInRooms[discountRoomIndex];

      //         const extraChildPrice = Number(
      //           discountRoomAtIndex.roomDetails.priceType === 'LKR'
      //             ? discountRoomAtIndex.roomDetails.lkrExtraPrice
      //             : discountRoomAtIndex.roomDetails.usdExtraPrice,
      //         );
      //         const extraChildDiscount = Number(extraChildCount) * extraChildPrice * numberOfNights;
      //         totalPrice -= extraChildDiscount;
      //       }
      //     }
      //   }

      //   totalPrice += extraChildPriceSum;
      // }
    }
    totalPrice = Math.max(totalPrice, 0);
    return totalPrice;
  };

  const taxDetails = calculateTotalTaxWithSequence(
    Number(roundNumberWithDecimal(calculateTotalPrice(), 2)),
    taxList,
    selectedRooms.isVatApplicable,
  );
  const taxArray = taxDetails?.taxArray;
  const taxAmount = taxDetails?.totalTaxAmount;

  const cancel = () => {
    console.log('value');
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSubmit = async (values: any) => {
    setIsLoading(true);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updatedTaxList = taxList.map((tax: any) => ({
      ...tax,
      useReservation: tax.vat ? selectedRooms.isVatApplicable : tax.useReservation,
    }));

    //@ts-ignore
    const reservedRooms: IReservedRooms[] = selectedRooms.stayTypes.map(res => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const childCountMap: any = {};
      res.roomDetails.childPolicyIdList &&
        res.roomDetails.childPolicyIdList.forEach(childPolicyId => {
          if (childCountMap[childPolicyId]) {
            childCountMap[childPolicyId]++;
          } else {
            childCountMap[childPolicyId] = 1;
          }
        });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const childrenRequestList: any = Object.entries(childCountMap).map(([childPolicyId, count]) => ({
        childPolicyId: parseInt(childPolicyId),
        count,
      }));

      return {
        // roomId: res.roomDetails.roomId,
        roomIdList: flattenDeep(res.roomDetails.roomList.map(i => i.roomId)),
        id: res.roomDetails.id,
        roomTypeId: res.roomDetails.roomTypeId,
        stayTypeId: res.roomDetails.stayTypeId,
        roomPaidStatus: 'PAID_ROOM',
        discountAmount:
          res.roomDetails.discountOption === 'AMOUNT'
            ? res.roomDetails.discountAmount
            : res.roomDetails.enteredDiscountValue,
        discountType: res.roomDetails.discountOption,
        discount: res.roomDetails.discountAmount,
        discountPercentage:
          res.roomDetails.discountOption === 'AMOUNT'
            ? ''
            : res.roomDetails.enteredDiscountValue
            ? res.roomDetails.enteredDiscountValue
            : '',
        noOfAdults: res.roomDetails.numberOfAdult,
        noOfChildren: res.roomDetails.numberOfChildren,
        keyHandOver: false,
        freeOfCharge: !res.roomDetails.payable,
        extraChildCount: res.roomDetails.extraChildCount === undefined ? 0 : Number(res.roomDetails.extraChildCount),
        mainGuest: {
          firstName: res.roomDetails.mainGuest.guestName,
          lastName: res.roomDetails.mainGuest.guestLastName,
          email: res.roomDetails.mainGuest.email,
          idNumber: res.roomDetails.mainGuest.guestNic,
          nicNumber: true,
          lastVisit: selectedRooms?.checkedIn,
          applicable: res.roomDetails.mainGuest.applicable,
          emailGuest: res.roomDetails.mainGuest.emailGuest,
          detailGuest: res.roomDetails.mainGuest.detailGuest,
        },
        reservedRoomDayPriceResponseList: res.roomDetails.reservedRoomDayPriceResponseList,
        otherGuests: res.roomDetails.otherGuests,
        childrenRequestList: childrenRequestList,
        childPolicyIdList: res.roomDetails.childPolicyIdList,
        // roomId: res.roomDetails.roomId,
      };
    });

    const payload: ICreatePayload = {
      channelId: selectedRooms.channelId,
      id: selectedRooms.id,
      guideId: values.guideId,
      reservationTypeId: values.reservationTypeId,
      countryId: values.countryId,
      checkInDate: selectedRooms?.checkedIn,
      checkOutDate: selectedRooms?.checkedOut,
      reservedRooms: reservedRooms,
      checkInTime: 'MORNING',
      checkOutTime: 'MORNING',
      arrivalTime: selectedRooms?.stayTypes[0]?.arrivalTime,
      internalRemarks: selectedRooms?.stayTypes[0]?.internalRemarks,
      reservationExpiredDate: values.reservationExpiredDate
        ? dayjs(values.reservationExpiredDate).format('YYYY-MM-DD')
        : null,
      reservationExpiredTime: values.reservationExpiredTime
        ? dayjs(values.reservationExpiredTime).format('HH:mm:ss')
        : null,
      paymentRequest: values.paymentRequest === 'yes' ? true : false,
      resident: selectedRooms?.stayTypes[0]?.roomDetails?.priceType === 'LKR' ? true : false,
      advancedPayment: values?.amount,
      paymentMethod: paymentType,
      mainGuest: selectedRooms?.stayTypes[0]?.mainGuest,
      hotelId: hotelId,
      totalAmount: calculateTotalPrice(),
      totalTax: Math.round(taxDetails?.totalTaxAmount * 100) / 100,
      // @ts-ignore
      needToPayAdvance: values.needToPayAdvance ? Number(values.needToPayAdvance) : 0,
      vatRegistry: {
        email: selectedRooms?.vatRegistry.email,
        registryType: ['RESERVATION'],
        hotelId: hotelId,
        vatNumber: selectedRooms?.vatRegistry.vatNumber,
        onlyThisHotelView: false,
        name: selectedRooms?.vatRegistry.name,
        address: selectedRooms?.vatRegistry.address,
      },
      taxList: updatedTaxList,
      vatRegistryApplicable: selectedRooms?.isVatDetailsApplicable,
      vatApplicable: selectedRooms?.isVatApplicable,
      bookingType: values.bookingType,
      searchAdultCount: selectedRooms?.searchAdultCount,
      searchChildCount: selectedRooms?.searchChildCount,
      invoiceCreditAmountRequestList: invoiceCreditAmountRequestList,
    };

    try {
      const result = await createReservation(payload);
      if (result.statusCode === '20000') {
        notificationController.success({message: result.message});
        await dispatch(setCurrent(0));
        navigate('/reservations');
        setIsLoading(false);
      } else {
        notificationController.error({message: result.message});
        setIsLoading(false);
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  const disabledDate = (current: any) => {
    const currentDate = moment().startOf('day');
    const checkinDate = moment(selectedRooms.checkedIn).add(1, 'day');
    return (current && current > checkinDate.startOf('day')) || (current && current < currentDate);
  };

  const cancelBlockedReservationDetails = async (reservationId: number, isConfirmNavigation: boolean) => {
    try {
      setloadCancellingReservation(true);
      const response = await cancelBlockedReservation(reservationId);
      if (response.statusCode === '20000') {
        setloadCancellingReservation(false);
        // @ts-ignore
        isConfirmNavigation && confirmNavigation();
        notificationController.success({message: 'Your Booking has been cancelled'});
        dispatch(setCurrent(0));
      } else {
        setloadCancellingReservation(false);
        notificationController.error({message: response.message});
      }
    } catch (error) {
      setloadCancellingReservation(false);
    }
  };

  const NUMBER_REGEX = /^\d*\.?\d*$/;

  return (
    <>
      <Row gutter={[20, 20]}>
        <Col md={isTabletOrMobile ? 18 : 8}>
          <S.Card>
            <S.Padding>
              <S.Title>{t('commonNames.bookingDeltailsReserve')}</S.Title>
              <S.DateSection>
                <S.CheckIn>
                  <S.CheckInOutText>Check-in</S.CheckInOutText>
                  <S.DateText>{formatDate(selectedRooms && selectedRooms.checkedIn)}</S.DateText>
                </S.CheckIn>
                <S.VerticalLine />
                <S.CheckOut>
                  <S.CheckInOutText>Check-out</S.CheckInOutText>
                  <S.DateText>{formatDate(selectedRooms && selectedRooms.checkedOut)}</S.DateText>
                </S.CheckOut>
              </S.DateSection>
              <S.StayNights>
                <S.RegularText>Total length of stay:</S.RegularText>
                <S.NightCount>
                  {calculateTotalNights(
                    selectedRooms && selectedRooms.checkedIn,
                    selectedRooms && selectedRooms.checkedOut,
                  )}{' '}
                  {calculateTotalNights(
                    selectedRooms && selectedRooms.checkedIn,
                    selectedRooms && selectedRooms.checkedOut,
                  ) <= 1
                    ? 'Night'
                    : 'Nights'}
                </S.NightCount>
              </S.StayNights>
              <S.HorizontalLine />
              {/* <S.BoldTitle>You selected:</S.BoldTitle> */}
              {selectedRooms &&
                selectedRooms?.stayTypes
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  ?.reduce((uniqueStayTypes: any, stayType) => {
                    const stayTypeName = stayType?.roomDetails?.stayType;
                    if (!uniqueStayTypes.includes(stayTypeName)) {
                      uniqueStayTypes.push(stayTypeName);
                    }
                    return uniqueStayTypes;
                  }, [])
                  .map((stayTypeName: string, idx: number) => {
                    const filteredStayTypes = selectedRooms?.stayTypes?.filter(
                      stayType => stayType?.roomDetails?.stayType === stayTypeName,
                    );
                    const roomCount = Number(filteredStayTypes[0]?.roomCount);
                    const roomPrice = Number(filteredStayTypes[0]?.roomDetails?.roomPrice);
                    const totalPrice = roomCount * roomPrice;
                    return (
                      <S.RoomPriceContainer key={idx}>
                        <S.StayWrapper>
                          <S.RoomNameText>{filteredStayTypes[0]?.roomCount} x </S.RoomNameText>
                          &nbsp;
                          <StayTypeTitle
                            adultCount={filteredStayTypes[0].roomDetails.numberOfAdult}
                            childCount={filteredStayTypes[0].roomDetails.numberOfChildren}
                            isBold={false}
                            meal={filteredStayTypes[0].roomDetails.mealType}
                            name={filteredStayTypes[0].roomDetails.roomType}
                            size={FONT_SIZE.xs}
                          />
                        </S.StayWrapper>
                        <S.RoomPriceText>
                          {filteredStayTypes[0]?.roomDetails?.priceType} {convertNumberFormatWithDecimal(totalPrice, 2)}
                        </S.RoomPriceText>
                      </S.RoomPriceContainer>
                    );
                  })}
              {selectedRooms && selectedRooms?.extraChild?.some(child => child.extraChildCount !== 0) && (
                <S.ExtraChildTitle>Extra bed summary</S.ExtraChildTitle>
              )}
              {selectedRooms &&
                selectedRooms?.extraChild?.map((child, idx) => {
                  return (
                    child.extraChildCount !== 0 && (
                      <S.RoomPriceContainer key={idx}>
                        <S.StayWrapper>
                          <S.RoomNameText>{child?.extraChildCount} x</S.RoomNameText>
                          &nbsp;
                          <StayTypeExtraChild
                            adultCount={child.numberOfAdult}
                            childCount={1}
                            isBold={false}
                            meal={child.mealType}
                            name={child.roomType}
                            size={FONT_SIZE.xs}
                          />
                        </S.StayWrapper>
                        <S.RoomPriceText>
                          {selectedRooms?.stayTypes[0].roomDetails.priceType}{' '}
                          {convertNumberFormatWithDecimal(child.extraChildPrice, 2)}
                        </S.RoomPriceText>
                      </S.RoomPriceContainer>
                    )
                  );
                })}
            </S.Padding>
          </S.Card>
          <br />

          <S.Card>
            <S.Padding>
              <S.Title>{t('reservation.priceSummary')}</S.Title>
            </S.Padding>
            <S.PriceSection>
              <S.PriceWrapper>
                <S.PriceHeader $hasCredit={!isEmpty(selectedRooms.selectedCreditBalances)}>Grand Total</S.PriceHeader>
                <S.PriceHeader
                  $crossLine={!isEmpty(selectedRooms.selectedCreditBalances)}
                  $hasCredit={!isEmpty(selectedRooms.selectedCreditBalances)}>
                  {selectedRooms && selectedRooms.stayTypes[0]?.roomDetails?.priceType}{' '}
                  {convertNumberFormatWithDecimal(calculateTotalPrice() + taxAmount, 2)}
                  {/* {convertNumberFormatWithDecimal(calculateGrandTotal(), 2)} */}
                </S.PriceHeader>
              </S.PriceWrapper>
              {!isEmpty(selectedRooms.selectedCreditBalances) && (
                <S.PriceWrapper>
                  <S.PriceHeader $hasCredit={false}>Total Payable</S.PriceHeader>
                  <S.PriceHeader $hasCredit={false}>
                    {selectedRooms && selectedRooms.stayTypes[0]?.roomDetails?.priceType}{' '}
                    {/* {convertNumberFormatWithDecimal(calculateTotalPrice() + taxAmount, 2)} */}
                    {convertNumberFormatWithDecimal(calculateGrandTotal(), 2)}
                  </S.PriceHeader>
                </S.PriceWrapper>
              )}

              <S.TaxWrapper>
                {/* {!isEmpty(taxList) ? (
                  <S.TaxSubHeader>
                    {`+${currency} ${convertNumberFormatWithDecimal(taxAmount, 2)}`} taxes and charges
                  </S.TaxSubHeader>
                ) : (
                  <S.TaxSubHeader>No Taxes Apply</S.TaxSubHeader>
                )} */}
                <S.TaxSubHeader>(Include Taxes and Charges)</S.TaxSubHeader>
              </S.TaxWrapper>
            </S.PriceSection>
            {calculateDiscountPrice() > 0 || !isEmpty(taxList) ? (
              <S.Padding>
                <S.Title>{t('reservation.priceBreakdown')}</S.Title>

                <S.ListDiscountRow>
                  <S.DiscountIconWrapper>
                    <BankOutlined color={BASE_COLORS.opacityOne} />
                    <S.TaxInfoText>Room Price</S.TaxInfoText>
                  </S.DiscountIconWrapper>
                  <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                    calculateTotalPrice(),
                    2,
                  )}`}</S.TaxInfoText>
                </S.ListDiscountRow>
                <S.ListDiscountRow>
                  <S.DiscountIconWrapper>
                    <MdOutlineDiscount color={BASE_COLORS.opacityOne} />
                    <S.TaxInfoText>Discount</S.TaxInfoText>
                  </S.DiscountIconWrapper>
                  <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                    calculateDiscountPrice(),
                    2,
                  )}`}</S.TaxInfoText>
                </S.ListDiscountRow>

                {!isEmpty(taxList) ? (
                  <S.TaxInformationWapper>
                    <S.TaxLeftWrapper>
                      <BsCashStack color={BASE_COLORS.opacityOne} />
                    </S.TaxLeftWrapper>
                    <S.TaxRightWrapper>
                      <S.TaxInfoText>{`Excludes ${currency} ${convertNumberFormatWithDecimal(
                        taxAmount,
                        2,
                      )} in taxes and charges`}</S.TaxInfoText>
                      <S.ListTaxWrapper>
                        {taxArray.map((tax, idx) => {
                          return (
                            <S.ListTaxRow key={`tax-list${idx}`}>
                              <S.TaxInfoText>{`${tax.name}`}</S.TaxInfoText>
                              <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                                tax.taxAmount,
                                2,
                              )}`}</S.TaxInfoText>
                            </S.ListTaxRow>
                          );
                        })}
                      </S.ListTaxWrapper>
                    </S.TaxRightWrapper>
                  </S.TaxInformationWapper>
                ) : null}
              </S.Padding>
            ) : null}
          </S.Card>
        </Col>

        <Col md={isTabletOrMobile ? 24 : 16}>
          <BaseForm
            form={form}
            size="middle"
            onValuesChange={changedValues => {
              const objKey = Object.keys(changedValues);
              const name = objKey[0];
              const value = changedValues[name];

              if (name === 'paymentRequest') {
                if (value === 'no') {
                  const totalAmount = calculateTotalPrice() + Math.round(taxAmount * 100) / 100;
                  form.setFieldValue('amount', Math.round(calculateGrandTotal()).toFixed(2));
                } else {
                  const totalAmount = calculateTotalPrice() + Math.round(taxAmount * 100) / 100;
                  form.setFieldValue('needToPayAdvance', Math.round(calculateGrandTotal()).toFixed(2));
                }
              }
            }}
            onFinish={handleSubmit}>
            {calculateTotalPrice() > 0 ? (
              <>
                {selectedRooms.stayTypes[0].roomDetails.paymentRequest && (
                  <S.ArrivalTimeWrapper>
                    <S.BlueCard>
                      <S.Padding>
                        <Row gutter={{xs: 10, md: 15, xl: 30}}>
                          <Col xs={24} md={14}>
                            <BaseForm.Item
                              name="paymentRequest"
                              label={t('commonNames.sendPaymentLink')}
                              rules={[
                                {
                                  required: true,
                                  message: 'Required field',
                                },
                              ]}
                              initialValue="no">
                              <RadioGroup
                                onChange={e => {
                                  setonlinePayment(e.target.value);
                                }}>
                                <Radio value="yes" disabled>
                                  Yes
                                </Radio>
                                <Radio value="no">No</Radio>
                              </RadioGroup>
                            </BaseForm.Item>
                          </Col>
                          <Col xs={24} md={10}>
                            <BaseForm.Item
                              name="bookingType"
                              label={t('commonNames.bookingTypeFlag')}
                              rules={[
                                {
                                  required: false,
                                  message: 'Required field',
                                },
                              ]}>
                              <Select placeholder="Select a booking type">
                                {BOOKING_TYPES_OPTIONS.map((post, index) => {
                                  return (
                                    <Option key={index} value={post.value}>
                                      {post.title}
                                    </Option>
                                  );
                                })}
                              </Select>
                            </BaseForm.Item>
                          </Col>
                        </Row>
                        {onlinePayment === 'yes' && (
                          <>
                            <S.FieldSetWrapper>
                              <S.FieldSet>
                                <S.Legend>Payment Deadline</S.Legend>
                                <Row gutter={{xs: 10, md: 15, xl: 30}}>
                                  <Col xs={24} md={8}>
                                    <BaseForm.Item
                                      name="reservationExpiredDate"
                                      label="Date"
                                      rules={[{required: true, message: 'Date is required'}]}>
                                      <CustomDatePicker placement="topLeft" disabledDate={disabledDate} />
                                    </BaseForm.Item>
                                  </Col>
                                  <Col xs={24} md={8}>
                                    <BaseForm.Item
                                      name="reservationExpiredTime"
                                      label="Time"
                                      rules={[{required: true, message: 'Time is required'}]}>
                                      <CustomTimePicker
                                        placement="topRight"
                                        disabledHours={() => {
                                          const currentDate = new Date();
                                          const selectedDate = form.getFieldValue('reservationExpiredDate');
                                          if (selectedDate && selectedDate.isSame(currentDate, 'day')) {
                                            return Array.from({length: currentDate.getHours()}, (_, index) => index);
                                          }
                                          return [];
                                        }}
                                        disabledMinutes={selectedHour => {
                                          const currentDate = new Date();
                                          const selectedDate = form.getFieldValue('reservationExpiredDate');
                                          if (selectedDate && selectedDate.isSame(currentDate, 'day')) {
                                            if (selectedHour === currentDate.getHours()) {
                                              return Array.from(
                                                {length: currentDate.getMinutes()},
                                                (_, index) => index,
                                              );
                                            }
                                          }
                                          return [];
                                        }}
                                      />
                                    </BaseForm.Item>
                                  </Col>
                                </Row>
                              </S.FieldSet>
                            </S.FieldSetWrapper>

                            <Row gutter={{xs: 10, md: 15, xl: 30}}>
                              <Col xs={24} md={12}>
                                <BaseForm.Item
                                  name="needToPayAdvance"
                                  label="Advance Amount"
                                  rules={[
                                    {
                                      required: false,
                                    },
                                    ({getFieldValue}) => ({
                                      validator(_, value) {
                                        const input = String(value).trim();
                                        const totalAmount = calculateGrandTotal();

                                        if (!input.match(NUMBER_REGEX) && String(value) !== '') {
                                          return Promise.reject('Please enter numbers only');
                                        }
                                        if (Number(value) > Number(Math.round(totalAmount))) {
                                          return Promise.reject(
                                            "Ensure advance payment doesn't exceed the Total price",
                                          );
                                        }

                                        return Promise.resolve();
                                      },
                                    }),
                                  ]}>
                                  <CurrencyInput
                                    addonBefore={`${
                                      selectedRooms && selectedRooms?.stayTypes[0]?.roomDetails?.priceType
                                    }.`}
                                  />
                                </BaseForm.Item>
                              </Col>
                              {/* {balanceAmount > 0 && (
                                <Col xs={24} md={12}>
                                  <BaseForm.Item name="balanceAmount" label="Balance Amount">
                                    <CurrencyInput
                                      addonBefore={`${
                                        selectedRooms && selectedRooms?.stayTypes[0]?.roomDetails?.priceType
                                      }.`}
                                      placeholder="Enter balance amount"
                                    />
                                  </BaseForm.Item>
                                </Col>
                              )} */}
                            </Row>
                          </>
                        )}
                      </S.Padding>
                    </S.BlueCard>
                  </S.ArrivalTimeWrapper>
                )}
                {onlinePayment === 'no' && (
                  <S.PaymentWrapper>
                    <S.BlueCard>
                      <S.Padding>
                        <S.CardTitle>Advance Payment (Optional)</S.CardTitle>
                        <S.PaymentOptionWrapper>
                          <S.PaymentOptionSection onClick={() => handleChangePaymentType('CASH')}>
                            <S.PaymentOptionOutline $selected={paymentType === 'CASH'}>
                              <S.Image src={MONEY} />
                            </S.PaymentOptionOutline>
                            <S.PaymentType $selected={paymentType === 'CASH'}>Cash</S.PaymentType>
                          </S.PaymentOptionSection>
                          <S.PaymentOptionSection onClick={() => handleChangePaymentType('CREDITCARD')}>
                            <S.PaymentOptionOutline $selected={paymentType === 'CREDITCARD'}>
                              <S.Image src={CARD} />
                            </S.PaymentOptionOutline>
                            <S.PaymentType $selected={paymentType === 'CREDITCARD'}>Credit Card</S.PaymentType>
                          </S.PaymentOptionSection>
                        </S.PaymentOptionWrapper>
                        <Row gutter={{xs: 10, md: 15, xl: 30}}>
                          <Col xs={24} md={12}>
                            <BaseForm.Item
                              name="amount"
                              label="Amount"
                              rules={[
                                {
                                  required: false,
                                },
                                ({getFieldValue}) => ({
                                  validator(_, value) {
                                    if (value === undefined) {
                                      return Promise.resolve();
                                    }
                                    const input = value.trim();
                                    if (!input.match(NUMBER_REGEX) && value !== '') {
                                      return Promise.reject('Please enter numbers only');
                                    }
                                    return Promise.resolve();
                                  },
                                }),
                              ]}>
                              <CurrencyInput
                                addonBefore={`${selectedRooms && selectedRooms?.stayTypes[0]?.roomDetails?.priceType}.`}
                              />
                            </BaseForm.Item>
                          </Col>
                        </Row>
                      </S.Padding>
                    </S.BlueCard>
                  </S.PaymentWrapper>
                )}
              </>
            ) : (
              <S.BlueCard>
                <S.Padding>
                  <S.FreeChargeRoomsPlaceholder>The room(s) are free of charge</S.FreeChargeRoomsPlaceholder>
                </S.Padding>{' '}
              </S.BlueCard>
            )}
            <Row gutter={{xs: 10, md: 15, xl: 30}} justify="end">
              <Col xs={24} md={6}>
                <Popconfirm
                  title="Are you sure to cancel this reservation?"
                  onConfirm={() => cancelBlockedReservationDetails(selectedRooms.id, false)}
                  onCancel={cancel}
                  okText="Yes"
                  cancelText="No">
                  <Button type="default" style={{marginTop: '1rem'}}>
                    Cancel Booking
                  </Button>
                </Popconfirm>
              </Col>
              <Col xs={24} md={7}>
                <Button loading={isLoading} htmlType="submit" type="primary" style={{marginTop: '1rem'}}>
                  Complete Booking
                </Button>
              </Col>
            </Row>
          </BaseForm>
        </Col>
      </Row>
      <DialogBox
        // @ts-ignore
        showDialog={showPrompt}
        confirmNavigation={() => {
          cancelBlockedReservationDetails(selectedRooms.id, true);
        }}
        cancelNavigation={cancelNavigation}
        loading={loadCancellingReservation}
      />
    </>
  );
}
