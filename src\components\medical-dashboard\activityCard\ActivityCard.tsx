import React from 'react';
import {Column, ColumnConfig} from '@ant-design/plots';
import {getAllReservationStatistics} from '@app/api/hotel/reservationStatistics.api';
import {useAppSelector} from '@app/hooks/reduxHooks';

export interface IReservationStatisticsData {
  id: number;
  month: string;
  numberOfNight: number;
  percentage: string;
}

export const ActivityCard: React.FC = () => {
  const [roomReservationStatisticsData, setRoomReservationStatisticsData] = React.useState<
    IReservationStatisticsData[]
  >([]);
  const hotelConfig = useAppSelector(state => state.hotelSlice.hotelConfig);

  React.useEffect(() => {
    listAllReservationStatistics(hotelConfig.hotelId);
  }, []);

  const listAllReservationStatistics = async (hotelId: number) => {
    try {
      const results: any = await getAllReservationStatistics(hotelId);
      setRoomReservationStatisticsData(results.result.room);
    } catch (error) {}
  };

  const config: ColumnConfig = {
    data: roomReservationStatisticsData,
    xField: 'month',
    yField: 'numberOfNight',
    yAxis: {
      title: {
        text: 'Occupancy Percentage',
      },
      label: {
        formatter: (value: string) => `${value}`,
      },
    },
    label: {
      position: 'middle',
      content: ({percentage}) => `${percentage}`,
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    meta: {
      type: {
        alias: 'numberOfNight',
      },
      guest: {
        alias: 'Room night count',
        formatter: (value: string) => `${value}`,
      },
      percentage: {
        alias: 'Percentage',
      },
    },
    columnStyle: {
      borderRadius: 25,
    },
  };

  return <Column {...config} />;
};
