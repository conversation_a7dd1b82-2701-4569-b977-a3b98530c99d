import React from 'react';
import {FaCloud} from 'react-icons/fa';
import {Link} from 'react-router-dom';
import styled from 'styled-components';

const Container = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
`;

const Button = styled.button`
  position: relative;
  top: -6px;
  border: 0;
  transition: all 40ms linear;
  margin: 10px 2px;
  padding: 8px 15px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 5px;

  &:focus {
    outline: none;
  }

  &:active,
  &.active {
    top: 2px;
  }
`;

const IconButton = styled(Button)`
  padding: 30px; /* Adjust padding to create a square shape */
`;

const PrimaryIconButton = styled(IconButton)`
  background-color: #4274d7;
  box-shadow: 0 0 0 1px #417fbd inset, 0 0 0 2px rgba(255, 255, 255, 0.15) inset, 0 8px 0 0 #4d5bbe,
    0 8px 8px 1px rgba(0, 0, 0, 0.5);
  color: white;

  &:active,
  &.active {
    box-shadow: 0 0 0 1px #417fbd inset, 0 0 0 1px rgba(255, 255, 255, 0.15) inset, 0 1px 3px 1px rgba(0, 0, 0, 0.3);
  }
`;

const IconLabel = styled.span`
  margin-left: 10px;
`;

interface Props {
  title: string;
  to: string;
}

const IconAwesomeButton: React.FC<Props> = ({title, to}) => {
  return (
    <Container>
      <Link to={to}>
        <PrimaryIconButton>
          <FaCloud />
          <IconLabel>Cloud</IconLabel>
        </PrimaryIconButton>
      </Link>
    </Container>
  );
};

export default IconAwesomeButton;
