import React, {useState} from 'react';
import {Row, Col, Input, Radio, Checkbox, Button, InputNumber} from 'antd';
import {Typography} from 'antd';

const {Text} = Typography;
const {TextArea} = Input;

interface FoodItemDetailsProps {
  selectedItem: {
    itemImage: string;
    item: string;
    price: number;
    currency: string;
  };
  comment: string | undefined;
  setComment: (value: string) => void;
  disableRemarkBtn: boolean;
}

// Define interfaces for our ingredient data
interface IngredientOption {
  id: string;
  name: string;
  price: number;
}

interface IngredientCategory {
  name: CategoryName;
  type: 'radio' | 'checkbox';
  options: IngredientOption[];
}

// Define literal types for category names to use as keys
type CategoryName = 'Meat' | 'Topping' | 'Side' | 'Beverage';

// Define the type for the selectedOptions object
interface SelectedOptions extends Record<CategoryName, string | string[]> {
  Meat: string;
  Side: string;
  Beverage: string;
  Topping: string[];
}

const FoodItemDetails: React.FC<FoodItemDetailsProps> = ({selectedItem, comment, setComment, disableRemarkBtn}) => {
  // Initial ingredients data
  const [ingredientsData] = useState<IngredientCategory[]>([
    {
      name: 'Meat',
      type: 'radio',
      options: [
        {id: 'beef', name: 'Beef', price: 0},
        {id: 'chicken', name: 'Chicken', price: -0.5},
        {id: 'pork', name: 'Pork', price: 0.5},
      ],
    },
    {
      name: 'Topping',
      type: 'checkbox',
      options: [
        {id: 'extra-cheese', name: 'Extra Cheese', price: 0.75},
        {id: 'fried-onion', name: 'Fried Onion', price: 0.5},
        {id: 'lettuce', name: 'Lettuce', price: 0.25},
        {id: 'pepper', name: 'Pepper', price: 0.25},
        {id: 'tomato-slice', name: 'Tomato Slice', price: 0.5},
      ],
    },
    {
      name: 'Side',
      type: 'radio',
      options: [
        {id: 'french-fries', name: 'French Fries', price: 0},
        {id: 'hash-browns', name: 'Hash Browns', price: 0.75},
        {id: 'onion-rings', name: 'Onion Rings', price: 1.0},
      ],
    },
    {
      name: 'Beverage',
      type: 'radio',
      options: [
        {id: 'cola', name: 'Cola', price: 1.5},
        {id: 'cola-no-sugar', name: 'Cola No Sugar', price: 1.5},
        {id: 'water', name: 'Water', price: 1.0},
      ],
    },
  ]);

  // State for selected ingredients
  const [selectedOptions, setSelectedOptions] = useState<SelectedOptions>({
    Meat: 'beef',
    Side: 'french-fries',
    Beverage: '',
    Topping: [],
  });

  const [quantity, setQuantity] = useState(1);

  const handleRadioChange = (category: CategoryName, value: string) => {
    setSelectedOptions({
      ...selectedOptions,
      [category]: value,
    });
  };

  const handleCheckboxChange = (category: CategoryName, optionId: string) => {
    if (category === 'Topping') {
      const currentSelected = selectedOptions[category];

      if (Array.isArray(currentSelected)) {
        if (currentSelected.includes(optionId)) {
          setSelectedOptions({
            ...selectedOptions,
            [category]: currentSelected.filter(id => id !== optionId),
          });
        } else {
          setSelectedOptions({
            ...selectedOptions,
            [category]: [...currentSelected, optionId],
          });
        }
      }
    }
  };

  // Calculate total price based on selections
  const calculateTotalPrice = () => {
    // Base price
    let basePrice = selectedItem?.price || 5.9;

    // Add prices for selected radio options
    ingredientsData.forEach(category => {
      if (category.type === 'radio') {
        const selectedId = selectedOptions[category.name];
        if (typeof selectedId === 'string' && selectedId) {
          const selectedOption = category.options.find(option => option.id === selectedId);
          if (selectedOption) {
            basePrice += selectedOption.price;
          }
        }
      }
    });

    // Add prices for selected checkbox options
    const selectedToppings = selectedOptions['Topping'];
    if (Array.isArray(selectedToppings)) {
      ingredientsData
        .find(category => category.name === 'Topping')
        ?.options.forEach(option => {
          if (selectedToppings.includes(option.id)) {
            basePrice += option.price;
          }
        });
    }

    // Multiply by quantity
    return (basePrice * quantity).toFixed(2);
  };

  return (
    <div style={{width: '100%'}}>
      <Row gutter={20}>
        <Col span={12}>
          <img
            style={{
              width: '100%',
              marginTop: '1rem',
              maxHeight: 250,
              objectFit: 'cover',
            }}
            src={selectedItem?.itemImage}
            alt={selectedItem?.item}
          />
        </Col>
        <Col span={12}>
          <Text style={{textAlign: 'left', fontSize: '1.5rem', display: 'block', fontWeight: 'bold'}}>
            {selectedItem?.item || 'Customised Burger'}
          </Text>
          <Text style={{textAlign: 'left', fontSize: '1rem', display: 'block', color: '#e8845a'}}>
            ${calculateTotalPrice()}
          </Text>
          {/* <Text style={{textAlign: 'left', fontSize: '0.9rem', display: 'block', color: '#666', marginTop: '0.5rem'}}>
            Aliquam est volutpat. Nam dui mi, tincidunt quis, accumsan porttitor, facilisis luctus.
          </Text> */}

          {/* Map through ingredient categories */}
          {/* {ingredientsData.map(category => (
            <div key={category.name} style={{marginTop: '1rem'}}>
              <Text style={{fontSize: '1rem', fontWeight: 'bold', display: 'block', marginBottom: '0.5rem'}}>
                {category.name}
              </Text>

              {category.type === 'radio' ? (
                // Radio buttons for meat, side, and beverage
                <Radio.Group
                  value={selectedOptions[category.name] as string}
                  onChange={e => handleRadioChange(category.name, e.target.value)}>
                  <Row gutter={[0, 8]}>
                    {category.options.map(option => (
                      <Col span={12} key={option.id}>
                        <Radio value={option.id}>{option.name}</Radio>
                      </Col>
                    ))}
                  </Row>
                </Radio.Group>
              ) : (
                // Checkboxes for toppings
                <Row gutter={[0, 8]}>
                  {category.options.map(option => (
                    <Col span={12} key={option.id}>
                      <Checkbox
                        checked={
                          Array.isArray(selectedOptions[category.name]) &&
                          (selectedOptions[category.name] as string[]).includes(option.id)
                        }
                        onChange={() => handleCheckboxChange(category.name, option.id)}>
                        {option.name}
                      </Checkbox>
                    </Col>
                  ))}
                </Row>
              )}
            </div>
          ))} */}

          {/* Extra Request (formerly Remarks) */}
          <div style={{marginTop: '1rem'}}>
            <Text style={{fontSize: '1rem', fontWeight: 'bold', display: 'block', marginBottom: '0.5rem'}}>
              Extra Request
            </Text>
            <TextArea
              rows={4}
              placeholder="Extra Request"
              readOnly={disableRemarkBtn}
              value={comment}
              onChange={e => setComment(e.target.value)}
            />
            {comment && comment.length > 60 && (
              <div style={{color: 'red', fontSize: '14px'}}>Character Limit Exceeded! (Max: 60 characters)</div>
            )}
          </div>
          <Text style={{fontSize: '0.8rem', display: 'block', marginTop: '0.5rem'}}>
            Category: {selectedItem?.item}
          </Text>
        </Col>
      </Row>
    </div>
  );
};

export default FoodItemDetails;
