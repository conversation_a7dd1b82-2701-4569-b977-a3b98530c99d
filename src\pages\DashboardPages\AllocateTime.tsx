import React, {useEffect, useState} from 'react';
import {Button, Modal, TimePicker} from 'antd';
import {ClockCircleOutlined} from '@ant-design/icons';
import type {Moment} from 'moment';
import moment from 'moment';

interface AllocateTimeProps {
  mode: 'Table' | 'Card';
  context?: number;
  onPrepareTime: (formattedTime: string, context?: number) => void;
  preparingTime: string;
}

const AllocateTime: React.FC<AllocateTimeProps> = ({mode, onPrepareTime, context, preparingTime}) => {
  // const {mode, context, onPrepareTime} = props;

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [duration, setDuration] = useState<Moment | null>(moment('00:30', 'HH:mm'));

  useEffect(() => {
    if (preparingTime) {
      setDuration(moment(preparingTime, 'HH:mm'));
    }
  }, [preparingTime]);

  const showModal = () => setIsModalOpen(true);
  const handleCancel = () => setIsModalOpen(false);

  const handleOk = () => {
    if (!duration) return;

    const formattedTime = `${duration.format('HH:mm')}:00`;
    // console.log('====================================');
    // console.log(formattedTime, onPrepareTime);
    // console.log('====================================');
    onPrepareTime && onPrepareTime(formattedTime, context);

    setIsModalOpen(false);
  };

  return (
    <div style={{padding: 20, fontFamily: 'Poppins, sans-serif'}}>
      {mode === 'Table' ? (
        <Button
          size="small"
          type="primary"
          onClick={showModal}
          style={{
            display: 'flex',
            alignItems: 'center',
            backgroundColor: '#1677ff',
            borderColor: '#1677ff',
            padding: '4px 10px',
            fontSize: '13px',
            fontWeight: 500,
            borderRadius: '6px',
            height: '35px',
          }}>
          Prepare Time
        </Button>
      ) : (
        <Button
          size="small"
          type="primary"
          icon={<ClockCircleOutlined style={{fontSize: '14px'}} />}
          onClick={showModal}
          style={{
            backgroundColor: '#1677ff',
            borderColor: '#1677ff',
            padding: '4px 6px',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '-20px',
          }}
        />
      )}

      <Modal
        title="Set Food Preparation Timer"
        open={isModalOpen}
        onOk={handleOk}
        centered
        onCancel={handleCancel}
        okText="Start Timer"
        cancelText="Cancel">
        <div style={{marginTop: 20}}>
          <label style={{fontWeight: 'bold'}}>Preparation Duration (HH:mm):</label>
          <TimePicker
            value={duration}
            onChange={value => setDuration(value)}
            format="HH:mm"
            showNow={false}
            defaultValue={moment('00:00', 'HH:mm')}
            style={{width: '100%', marginTop: 10}}
          />
        </div>
      </Modal>
    </div>
  );
};

export default AllocateTime;
