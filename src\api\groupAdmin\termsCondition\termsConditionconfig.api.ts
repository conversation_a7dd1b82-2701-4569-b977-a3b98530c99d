import authInstance from '@app/api/authInstance';
import instance, {HOTEL_SERVICE, LOGIN_SERVICE} from '@app/api/instance';

export interface IApiResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface IupdateTermsPayload {
  id: number;
  name: string;
  termsPolicy: string;
}

export interface ICreateTermsPayload {
  name: string;
  termsPolicy: string;
}

export const getAllTermsconfig = (hotelId: number): Promise<IApiResponse> =>
  instance.get<IApiResponse>(HOTEL_SERVICE + `system-config/all?hotelId=${hotelId}`).then(({data}) => data);

export const updateTermsconfig = (payload: IupdateTermsPayload): Promise<IApiResponse> => {
  return instance.put<IApiResponse>(HOTEL_SERVICE + 'system-config', payload).then(({data}) => data);
};

export const createTermsconfig = (payload: ICreateTermsPayload): Promise<IApiResponse> => {
  return instance.put<IApiResponse>(HOTEL_SERVICE + 'system-config', payload).then(({data}) => data);
};
