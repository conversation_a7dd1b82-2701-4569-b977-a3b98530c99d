import instance, {HOTEL_SERVICE} from '../instance';

export const getAllRoomOverView = (hotelId: number) =>
  instance
    .get(HOTEL_SERVICE + `reservation/roomType/monthReport/${hotelId}?&reservedRoomStatus=CHECKEDIN,BOOKED`)
    .then(({data}) => data);

export const getAllOverView = (hotelId: number, startDate: string, endDate: string, status: string[]) => {
  const joinedStatus = status.join(',');
  return instance
    .get(
      HOTEL_SERVICE +
        `reservation/roomType/${hotelId}?startDate=${startDate}&endDate=${endDate}&reservedRoomStatus=${joinedStatus}`,
    )
    .then(({data}) => data);
};
