import React, {useState} from 'react';
import * as S from './PassportUpload.style';
import {Popover} from '../Popover/Popover';

const MainGuestPassport = ({roomIndex, uploadImages, onMainGuestImageChange, savedImage}: any) => {
  const [imageChanged, setImageChanged] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleImageChange = (event: any) => {
    onMainGuestImageChange(event, roomIndex);
    setImageChanged(true);
  };
  const handleFileInputClick = () => {
    document.getElementById(`passportImage-${roomIndex}`)?.click();
  };

  return (
    <S.FileInputContainer>
      {uploadImages[roomIndex] || savedImage ? (
        <S.UploadedImage
          src={uploadImages[roomIndex] ? URL.createObjectURL(uploadImages[roomIndex]) : savedImage}
          alt="Main Guest's Passport Image"
        />
      ) : (
        <S.UploadIcon />
      )}
      <S.CustomPopover
        style={{padding: '0px'}}
        placement="leftTop"
        content={
          <img
            width={500}
            height="auto"
            src={uploadImages[roomIndex] ? URL.createObjectURL(uploadImages[roomIndex]) : savedImage}
          />
        }
        trigger="hover">
        <S.ZoomIcon />
      </S.CustomPopover>
      <S.FileInputLabel onClick={handleFileInputClick}>
        {uploadImages[roomIndex] || savedImage ? 'Change Image' : 'Upload Image'}
      </S.FileInputLabel>
      <S.FileInput
        accept="image/*"
        type="file"
        id={`passportImage-${roomIndex}`}
        onChange={event => handleImageChange(event)}
      />
    </S.FileInputContainer>
  );
};

export default MainGuestPassport;
