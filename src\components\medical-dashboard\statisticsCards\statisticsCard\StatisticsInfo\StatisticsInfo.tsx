import React from 'react';
import {Space} from 'antd';
import {CaretDownOutlined, CaretUpOutlined} from '@ant-design/icons';
import {getDifference} from 'utils/utils';
import * as S from './StatisticsInfo.styles';

interface StatisticsInfoProps {
  name: string;
  value: number;
  prevValue: number;
  count: number;
}

export const StatisticsInfo: React.FC<StatisticsInfoProps> = ({name, value, prevValue, count}) => {
  return (
    <Space direction="vertical" size={6}>
      <S.Title>{name}</S.Title>
      <S.Count>{count}</S.Count>
    </Space>
  );
};
