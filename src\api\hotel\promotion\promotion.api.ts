import instance, { HOTEL_SERVICE } from '@app/api/instance';

export const getAllPromotions = (
  groupId: number,
  pageSize: number | undefined,
  current: number,
  hotelId: number
): Promise<PromotionProps> =>
  instance
    .get<PromotionProps>(
      HOTEL_SERVICE + `promotion/group/${groupId}?hotelId=${hotelId}&page=${current}&sortField=id&size=${pageSize}&direction=DESC`,
    )
    .then(({ data }) => data);

export const createPromotion = (payload: PromotionPayload): Promise<PromotionProps> => {
  const promotionFormData = new FormData();
  promotionFormData.append('promotion  ', JSON.stringify(payload.promotion));
  promotionFormData.append('promotionImage', payload.promotionImage);
  return instance.post<PromotionProps>(HOTEL_SERVICE + `promotion`, promotionFormData).then(({ data }) => data);
};

export const updatePromotion = (payload: PromotionPayload): Promise<PromotionProps> => {
  const promotionFormData = new FormData();
  promotionFormData.append('promotion  ', JSON.stringify(payload.promotion));
  promotionFormData.append('promotionImage', payload.promotionImage);
  return instance.put<PromotionProps>(HOTEL_SERVICE + `promotion`, promotionFormData).then(({ data }) => data);
};

export const deletePromotion = (id: number): Promise<PromotionProps> =>
  instance.delete<PromotionProps>(HOTEL_SERVICE + `promotion/${id}`).then(({ data }) => data);

export interface PromotionProps {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface PromotionPayload {
  promotion: {
    title: string;
    description: string;
    hotelId: number;
    groupsId: number;
    startDate: string;
    endDate: string;
    id?: number;
  };
  promotionImage: string;
}
