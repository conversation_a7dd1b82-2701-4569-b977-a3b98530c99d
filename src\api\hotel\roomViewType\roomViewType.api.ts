import instance, {HOTEL_SERVICE} from '@app/api/instance';

export interface IRoomViewTypeRequest {
  id?: number;
  viewType: string;
  description?: string;
  imageUrl?: File[];
  hotelId: number;
}

export interface IRoomViewTypeResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export const CreateRoomViewType = (roomViewTypePayload: IRoomViewTypeRequest): Promise<IRoomViewTypeResponse> => {
  const formData = new FormData();
  const obj = JSON.stringify({
    viewType: roomViewTypePayload.viewType,
    description: roomViewTypePayload.description,
    hotelId: roomViewTypePayload.hotelId,
    active: true,
  });
  const image: any = roomViewTypePayload?.imageUrl;
  image.map((image: any) => {
    formData.append('typeImages', image);
  });

  formData.append('viewType', obj);

  return instance.post<IRoomViewTypeResponse>(HOTEL_SERVICE + 'viewType', formData).then(({data}) => data);
};

export const getAllViewtype = (hotelId: number): Promise<IRoomViewTypeResponse> =>
  instance
    .get<IRoomViewTypeResponse>(
      HOTEL_SERVICE +
        `viewType/search?size=100&page=0&sortField=id&direction=DESC&viewType=&description&hotelName&hotelId=${hotelId}`,
    )
    .then(({data}) => data);

export const UpdateRoomViewType = (roomViewTypePayload: IRoomViewTypeRequest): Promise<IRoomViewTypeResponse> => {
  const formData = new FormData();
  const obj = JSON.stringify({
    id: roomViewTypePayload.id,
    viewType: roomViewTypePayload.viewType,
    description: roomViewTypePayload.description,
    hotelId: roomViewTypePayload.hotelId,
    active: true,
  });
  const image: any = roomViewTypePayload?.imageUrl;
  image.map((image: any) => {
    formData.append('typeImages', image);
  });
  formData.append('viewType', obj);

  return instance.put<IRoomViewTypeResponse>(HOTEL_SERVICE + 'viewType', formData).then(({data}) => data);
};

export const DeleteRoomViewType = (id: number): Promise<IRoomViewTypeResponse> =>
  instance.delete<IRoomViewTypeResponse>(HOTEL_SERVICE + `viewType/${id}`).then(({data}) => data);

export const getAllViewtypeByHotelId = (hotelId: number): Promise<IRoomViewTypeResponse> =>
  instance
    .get<IRoomViewTypeResponse>(
      HOTEL_SERVICE + 'viewType/search?size=100&page=0&sortField=id&direction=DESC&viewType=&description&hotelName',
    )
    .then(({data}) => data);
