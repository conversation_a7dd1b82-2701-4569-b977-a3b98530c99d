import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const getReservationBankStatus = (): Promise<any> => {
  return instance.get<any>(HOTEL_SERVICE + `payment-transaction`).then(({data}) => data);
};

export const getBankingPaymentStatusByReservationNo = (reservationNo: string): Promise<any> => {
  return instance.get<any>(HOTEL_SERVICE + `retrieve-order/${reservationNo}`).then(({data}) => data);
};
