import React, {useEffect} from 'react';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import * as S from './Modals.style';
import {Col, Row} from 'antd';
import {FormInstance} from 'antd/es/form/Form';
import {useAppDispatch} from '@app/hooks/reduxHooks';
import {setLoading} from '@app/store/slices/commonSlice';
import {Option, Select} from '@app/components/common/selects/Select/Select';
import {updateBookingType} from '@app/api/hotel/reservation/reservation.api';
import {notificationController} from '@app/controllers/notificationController';
import {BOOKING_TYPES_OPTIONS} from '../../BookingCompletion/BookingCompletion';
import {commonNames} from '@app/utils/utils';
import {useTranslation} from 'react-i18next';

export interface IPaymentValues {
  amount: string;
}

interface FieldData {
  name: string | number;
  value?: any;
}

interface Props {
  form: FormInstance;
  reloadData: () => void;
  isModalVisible: boolean;
  onCancel: () => void;
  bookingType?: string;
  reservationId: number;
  data: any;
}

export const UpdateBookingType: React.FC<Props> = ({
  isModalVisible,
  onCancel,
  form,
  bookingType,
  reloadData,
  reservationId,
  data,
}) => {
  const [fields, setFields] = React.useState<FieldData[]>([{name: 'amount', value: undefined}]);
  const dispatch = useAppDispatch();
  const {t} = useTranslation();
  const onCloseModal = () => {
    reloadData();
    form.resetFields();
    onCancel();
  };

  const create = async (reservationId: number, bookingType: string) => {
    try {
      const response = await updateBookingType(reservationId, bookingType);
      if (response.statusCode === '20000') {
        notificationController.success({message: response.message});
        await onCloseModal();
      } else {
        notificationController.error({message: response.message});
      }
    } catch (error) {}
  };

  const onFinish = () => {
    dispatch(setLoading(true));
    const formData = form.getFieldsValue();
    create(reservationId, formData.bookingType);
  };
  useEffect(() => {
    if (isModalVisible) {
      form.setFieldValue('bookingType', bookingType);
    }
  }, [bookingType, isModalVisible, form]);

  return (
    <S.FormContent>
      <BaseForm
        size="middle"
        form={form}
        initialValues={{bookingType: bookingType}}
        fields={fields}
        onFinish={onFinish}>
        <Row
          gutter={{xs: 10, md: 15, xl: 30}}
          style={{
            display: 'flex',
            justifyContent: 'center',
          }}>
          <Col xs={24} md={20}>
            <BaseForm.Item
              name="bookingType"
              label={t('commonNames.bookingTypeFlag')}
              rules={[
                {
                  required: false,
                  message: 'Required field',
                },
              ]}>
              <Select placeholder="Select a booking type">
                {BOOKING_TYPES_OPTIONS.map((post, index) => {
                  return (
                    <Option key={index} value={post.value}>
                      {post.title}
                    </Option>
                  );
                })}
              </Select>
            </BaseForm.Item>
          </Col>
        </Row>
      </BaseForm>
    </S.FormContent>
  );
};
