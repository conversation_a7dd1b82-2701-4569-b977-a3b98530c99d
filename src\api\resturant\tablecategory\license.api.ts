import authInstance from '@app/api/authInstance';
import {LOGIN_SERVICE} from '@app/api/instance';

export interface License {
  id?: number;
  name: string;
  description?: string;
  features: string[];
  isFreeTrial?: boolean;
  hasTrial?: boolean;
  trialDays?: number;
  active: boolean;
  maxRooms?: number;
  maxHotels?: number;
  pricePrefix?: string;
  annualPrice?: number;
  monthlyPrice?: number;
  perRoomPrice?: number;
  perPropertyPrice?: number;
  type: 'YEAR' | 'MONTH' | 'FLEXIBLE';
}

export interface LicenseResponse {
  message: string;
  result: {
    license: License[];
  };
  status: string;
  statusCode: string;
}

export const CreateLicense = (licensePayload: License): Promise<LicenseResponse> => {
  return authInstance.post<LicenseResponse>(LOGIN_SERVICE + 'api/v1/license', licensePayload).then(({data}) => data);
};

// export const UpdateLicense = (licenseId: number, licensePayload: Partial<License>): Promise<LicenseResponse> => {
//   return authInstance
//     .put<LicenseResponse>(`${LOGIN_SERVICE}api/v1/license/${licenseId}`, licensePayload)
//     .then(({data}) => data);
// };

export const UpdateLicense = (licensePayload: Partial<License>): Promise<LicenseResponse> => {
  return authInstance.put<LicenseResponse>(`${LOGIN_SERVICE}api/v1/license`, licensePayload).then(({data}) => data);
};

export const FetchLicenses = (): Promise<LicenseResponse> => {
  return authInstance.get<LicenseResponse>(LOGIN_SERVICE + 'api/v1/license').then(({data}) => data);
};

export const DeleteLicense = (licenseId: number): Promise<LicenseResponse> => {
  return authInstance.delete<LicenseResponse>(`${LOGIN_SERVICE}api/v1/license/${licenseId}`).then(({data}) => data);
};
