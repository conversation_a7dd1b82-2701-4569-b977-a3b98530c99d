/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-05-03 17:01:44
 * @modify date 2023-05-03 17:01:44
 * @desc [room content]
 */
import React, {useEffect, useState} from 'react';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import * as S from '../Room/Forms/Form.style';
import {UploadFile} from 'antd';
import {Select} from '@app/components/common/selects/Select/Select.styles';
import {Option} from '@app/components/common/selects/Select/Select';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {setIsTouchAction, setLoading, setModalVisible} from '@app/store/slices/commonSlice';
import {UpdateRoomStatus} from '@app/api/hotel/room.api';
import {notificationController} from '@app/controllers/notificationController';
import {getAllViewtype} from '@app/api/hotel/roomViewType/roomViewType.api';
import {FieldData, Props} from './interface';
import {getRoomTypes} from '@app/store/slices/commonApiSlice';
import {IHotelId} from '@app/store/slices/interface';

export const RoomStatusContent: React.FC<Props> = ({form, reloadData, rowData}) => {
  const [fields, setFields] = React.useState<FieldData[]>([
    {name: 'RoomNumber', value: ''},
    {name: 'UnitCode', value: ''},
    {name: 'PhoneExtention', value: ''},
    {name: 'RoomStatus', value: ''},
    {name: 'RoomType', value: ''},
  ]);
  const dispatch = useAppDispatch();

  const onFinish = () => {
    dispatch(setLoading(true));
    const formData = form.getFieldsValue();
    const data: any = [
      {
        id: rowData?.id,
        status: formData?.roomStatus,
        // houseKeepingStatus: rowData?.houseKeepingStatus,
        onDemandStatus: rowData?.onDemandStatus,
      },
    ];

    UpdateRoomStatus(data).then(
      res => {
        if (res.statusCode === '20000') {
          notificationController.success({message: res.message});
          onCloseModal();
        } else {
          notificationController.error({message: res.message});
        }
        dispatch(setLoading(false));
      },
      error => {
        dispatch(setLoading(false));
      },
    );
  };

  const onCloseModal = () => {
    reloadData();
    form.resetFields();
    dispatch(setLoading(false));
    dispatch(setModalVisible(false));
  };

  return (
    <S.FormContent>
      <BaseForm
        name="stepForm"
        form={form}
        fields={fields}
        onFinish={onFinish}
        onFieldsChange={(_, allFields) => {
          dispatch(setIsTouchAction(true));
        }}>
        <BaseForm.Item
          name="roomStatus"
          label="Room Status"
          rules={[{required: true, message: 'Room Status is required'}]}>
          <Select placeholder="Select Room Status">
            <Option value="READY">Ready</Option>
            {/* <Option value="CHECKEDIN">Check In</Option>
            <Option value="CHECKEDOUT">Check Out</Option>
            <Option value="BOOKED">Booked</Option>
            <Option value="TEMPORARILY_BLOCKED">Temporarily Blocked</Option>
            <Option value="HOUSEKEEPING_BLOCK">Housekeeping Block</Option>
            <Option value="CLEANING_REQUEST">Cleaning request</Option>
            <Option value="CHECKEDIN_CLEANING_REQUEST">Check in cleaning request</Option>
            <Option value="CHECKEDOUT_CLEANING_REQUEST">Check out cleaning request</Option>
            <Option value="ONDEMAND_REQUEST">On Demand</Option>
            <Option value="ARGENT_REQUEST">Argent Request</Option> */}
          </Select>
        </BaseForm.Item>
      </BaseForm>
    </S.FormContent>
  );
};
