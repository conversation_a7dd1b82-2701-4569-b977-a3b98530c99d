import authInstance from '@app/api/authInstance';
import {LOGIN_SERVICE} from '@app/api/instance';

export const getAllAdminPermissionByRoleId = (roleId: number): Promise<AdminPrivilegeResponse> =>
  authInstance
    .get<AdminPrivilegeResponse>(LOGIN_SERVICE + `api/v1/hotel-role-hotel-permission/hotel-role/${roleId}`)
    .then(({data}) => data);

export const adminPermissionUpdate = (payload: AdminPrivilegePayload): Promise<AdminPrivilegeResponse> =>
  authInstance
    .put<AdminPrivilegeResponse>(LOGIN_SERVICE + `api/v1/service-role-services-permission`, payload)
    .then(({data}) => data);

export interface AdminPrivilegeResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface AdminPrivilegePayload {
  id: any;
  hotelRoleId: number;
  hotelPermissionId: number;
  status: string;
}
