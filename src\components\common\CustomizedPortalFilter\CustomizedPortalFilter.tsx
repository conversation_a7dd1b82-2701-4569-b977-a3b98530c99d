import React from 'react';
import * as S from './PortalFilter.style';
import {FaFilterCircleXmark} from 'react-icons/fa6';
import {FaFilter} from 'react-icons/fa';
import PortalForms from './PortalForms';

interface IDateRange {
  startDate: string;
  endDate: string;
}

interface IAdvanceDateFilter {
  key: string | number;
  defaultFilterShow?: boolean;
  showVatNumberInput?: boolean;
  onApply: (data: {
    range: IDateRange;
    vatNumber: string;
    selectDateType: 'SINGLE' | 'RANGE' | undefined;
    dateType: string;
  }) => void;
  loadingButton: boolean;
}

function CustomizedPortalFilter(props: IAdvanceDateFilter) {
  const {key, defaultFilterShow = false, showVatNumberInput = false, onApply, loadingButton} = props;
  const [showFilter, setShowFilter] = React.useState<boolean>(defaultFilterShow);

  return (
    <S.FilterWrapper key={key}>
      {showFilter && (
        <PortalForms onApply={onApply} showVatNumberInput={showVatNumberInput} loadingButton={loadingButton} />
      )}
      {!defaultFilterShow ? (
        <S.FilterBtn
          onClick={() => {
            showFilter &&
              onApply({
                range: {endDate: '', startDate: ''},
                vatNumber: '',
                dateType: 'year',
                selectDateType: 'SINGLE',
              });
            setShowFilter(!showFilter);
          }}>
          <div style={{fontSize: '14px', marginLeft: '8px', marginTop: '6px'}}>
            {showFilter ? <FaFilterCircleXmark style={{color: 'red'}} /> : <FaFilter />}
          </div>
        </S.FilterBtn>
      ) : null}
    </S.FilterWrapper>
  );
}

export default React.memo(CustomizedPortalFilter);
