import authInstance from '@app/api/authInstance';
import instance, {HOTEL_SERVICE} from '../instance';

export const getAllMinNight = (): Promise<MinNightResponse> =>
  instance.get<MinNightResponse>(HOTEL_SERVICE + `minimum-night-config`).then(({data}) => data);

export const getAllMinNightById = (id: number, duration?: [string, string]): Promise<MinNightResponse> =>
  instance
    .get<MinNightResponse>(
      HOTEL_SERVICE +
        `minimum-night-config?hotelId=${id}` +
        (duration?.[0] && duration?.[1] ? `&startDate=${duration[0]}&endDate=${duration[1]}` : ''),
    )
    .then(({data}) => data);

export const getAllMinNightByIdType = (
  id: number | undefined,
  directWeb: {direct: any; web: any} | undefined,
): Promise<MinNightResponse> =>
  instance
    .get<MinNightResponse>(
      HOTEL_SERVICE + `minimum-night-config/search?hotelId=${id}&web=${directWeb?.web}&direct=${directWeb?.direct}`,
    )
    .then(({data}) => data);

export const CreateMinNight = (payload: MinNightResponse): Promise<MinNightResponse> => {
  return instance.post<MinNightResponse>(HOTEL_SERVICE + 'minimum-night-config', payload).then(({data}) => data);
};

export const UpdateMinNight = (payload: MinNightResponse): Promise<MinNightResponse> => {
  return instance.put<MinNightResponse>(HOTEL_SERVICE + 'minimum-night-config', payload).then(({data}) => data);
};

export const DeleteMinNight = (id: number): Promise<MinNightResponse> =>
  instance.delete<MinNightResponse>(HOTEL_SERVICE + `minimum-night-config/${id}`).then(({data}) => data);

export interface MinNightResponse {
  map(arg0: (item: any) => any): unknown;
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

// export interface CreateEmployeeProps {
//   firstName: string;
//   lastName: string;
//   contactNumber: number;
//   email: string;
//   groupsId: number;
//   username?: string;
//   hotelId?: number;
// }

// export interface UpdateEmployeeProps {
//   id: number;
//   firstName: string;
//   lastName: string;
//   contactNumber: number;
//   email: string;
//   groupsId: number;
//   username?: string;
//   hotelId?: number;
// }
