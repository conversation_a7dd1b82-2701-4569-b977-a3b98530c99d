export interface Doctor {
  id: number;
  name: string;
  specifity: number;
  rating: number;
  gps?: {
    latitude: number;
    longitude: number;
  };
  imgUrl: string;
  phone: string;
  address: string;
}

export const getDoctorsData = (): Promise<Doctor[]> => {
  return new Promise(res => {
    setTimeout(() => {
      res([
        {
          id: 1,
          name: 'Group Admin',
          specifity: 1,
          rating: 5,
          gps: {
            latitude: 51.505,
            longitude: -0.09,
          },
          imgUrl: 'https://icon-library.com/images/avatar-icon-images/avatar-icon-images-4.jpg',
          phone: '+X-XXX-XXX-XXXX',
          address: '98 Santa Clara Court Cherry Hill, NJ 08003',
        },
        {
          id: 2,
          name: 'Admin Group',
          specifity: 2,
          rating: 5,
          gps: {
            latitude: 41.732438,
            longitude: 44.7688134,
          },
          imgUrl: 'https://icon-library.com/images/avatar-icon-images/avatar-icon-images-4.jpg',
          phone: '+X-XXX-XXX-XXXX',
          address: '850 South Tunnel St. Newburgh, NY 12550',
        },
        {
          id: 3,
          name: '<PERSON><PERSON>',
          specifity: 3,
          rating: 5,
          gps: {
            latitude: 40.73061,
            longitude: -73.935242,
          },
          imgUrl: 'https://www.svgrepo.com/show/382106/male-avatar-boy-face-man-user-9.svg',
          phone: '+X-XXX-XXX-XXXX',
          address: '111 Foxrun Street Conyers, GA 30012',
        },
        {
          id: 4,
          name: 'Keerthana Raveen',
          specifity: 4,
          rating: 5,
          imgUrl: 'https://www.svgrepo.com/show/382106/male-avatar-boy-face-man-user-9.svg',
          phone: '+X-XXX-XXX-XXXX',
          address: '9 Wagon Street Ravenna, OH 44266',
        },
        {
          id: 5,
          name: 'James Moss',
          specifity: 5,
          rating: 4,
          gps: {
            latitude: 59.334122,
            longitude: 18.071997,
          },
          imgUrl: 'https://www.svgrepo.com/show/382106/male-avatar-boy-face-man-user-9.svg',
          phone: '+X-XXX-XXX-XXXX',
          address: '9568 Tower St. Somerset, NJ 08873',
        },
        {
          id: 6,
          name: 'Sara Mills',
          specifity: 6,
          rating: 5,
          gps: {
            latitude: -26.195246,
            longitude: 28.034088,
          },
          imgUrl: 'https://www.svgrepo.com/show/382106/male-avatar-boy-face-man-user-9.svg',
          phone: '+X-XXX-XXX-XXXX',
          address: '850 South Tunnel St. Newburgh, NY 12550',
        },
        {
          id: 7,
          name: 'Francisco Venancio',
          specifity: 7,
          rating: 5,
          gps: {
            latitude: 55.17111,
            longitude: -118.796928,
          },
          imgUrl: 'https://www.svgrepo.com/show/382106/male-avatar-boy-face-man-user-9.svg',
          phone: '+X-XXX-XXX-XXXX',
          address: '322 South Del Monte Rd. West Hempstead, NY 11552',
        },
        {
          id: 8,
          name: 'Jorden Cannon',
          specifity: 8,
          rating: 4,
          gps: {
            latitude: -22.908333,
            longitude: -43.196388,
          },
          imgUrl: 'https://www.svgrepo.com/show/382106/male-avatar-boy-face-man-user-9.svg',
          phone: '+X-XXX-XXX-XXXX',
          address: '7634 Taylor St. Boston, MA 02127',
        },
      ]);
    }, 0);
  });
};
