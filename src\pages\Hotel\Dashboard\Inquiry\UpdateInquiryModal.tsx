/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {Modal} from '@app/components/common/Modal/Modal';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {getReservedRoomModalData} from '@app/store/slices/reservationSlice';
import {FC, useEffect, useState} from 'react';
import {Col, Row, Space, TimePicker} from 'antd';
import * as S from './Inquiry.style';
import {BASE_COLORS, FONT_SIZE} from '@app/styles/themes/constants';
import {find, isEmpty} from 'lodash';
import {BsCashStack} from 'react-icons/bs';
import {calculateTotalNights, commonNames, convertNumberFormatWithDecimal, formatDate} from '@app/utils/utils';
import {
  calculateTotalTaxWithSequence,
  checkVATAvailability,
  getCountries,
  roundNumber,
  validateEmail,
} from '@app/utils/functions';
import {StayTypeTitle} from '@app/components/common/StayTypeTitle/StayTypeTitle';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {Radio, RadioGroup} from '@app/components/common/Radio/Radio';
import styled from 'styled-components';
import {DatePicker} from '@app/components/common/pickers/DatePicker';
import moment from 'moment';
import {Input} from '@app/components/common/inputs/Input/Input';
import {CARD, MONEY} from '@app/assets';
import {Button} from '@app/components/common/buttons/Button/Button';
import {Option, Select} from '@app/components/common/selects/Select/Select';
import {Popconfirm} from '@app/components/common/Popconfirm/Popconfirm';
import dayjs from 'dayjs';
import {updateReservationInquiry} from '@app/api/hotel/inquiry/inquiry.api';
import {notificationController} from '@app/controllers/notificationController';
import {cancelBlockedReservation, getCreditNote} from '@app/api/hotel/reservation/reservation.api';
import {Switch} from '@app/components/common/Switch/Switch';
import {BankOutlined, CheckOutlined, CloseOutlined} from '@ant-design/icons';
import {
  // IReservationUpdateGuest,
  getAllGuests,
  // searchedExactVatUsersApi,
  // searchedVatUsersApi,
  // updateGuest,
  updateInquiryGuest,
  updateReservationDates,
} from '@app/api/hotel/guest/guest.api';
import {AutoComplete} from '@app/components/common/AutoComplete/AutoComplete';
import {useTranslation} from 'react-i18next';
import {DatePicker as AntDatePicker} from 'antd';
import CurrencyInput from '@app/components/common/inputs/CurrencyInput/CurrencyInput';
import {getAllMinNightByIdType} from '@app/api/MinimumNight/MinimumNight.api';
import {setCurrent, setSelectedRooms} from '@app/store/slices/reservationSlice';

interface UpdateInquiryModalProps {
  visible: boolean;
  onCancel: () => void;
  rowData: any;
  reloadData: () => void;
}
interface MinNightData {
  always?: boolean;
  startDate?: string;
  endDate?: string;
  numOfDays: number;
}

const CustomDatePicker = styled(DatePicker)`
  width: 100%;
`;

const CustomTimePicker = styled(TimePicker)`
  width: 100%;
`;

const NUMBER_REGEX = /^\d*\.?\d*$/;

const UpdateInquiryModal: FC<UpdateInquiryModalProps> = ({visible, onCancel, rowData, reloadData}) => {
  const [onlinePayment, setonlinePayment] = useState('no');
  const [paymentType, setpaymentType] = useState<'CASH' | 'CREDITCARD'>('CASH');
  const [inquiryUpdateType, setinquiryUpdateType] = useState('confirm');
  const [loadingDate, setLoadingDate] = useState(false);
  const [country, setCountry] = useState<any[]>();
  const [isVatApplicable, setisVatApplicable] = useState(true);
  const [data, setData] = useState([]);
  const [isExistMain, setisExistMain] = useState(false);
  const [mainguestData, setMainguestData]: any = useState({
    id: null,
    firstName: null,
    lastName: null,
    email: null,
    phoneNumber: null,
  });
  const {RangePicker} = AntDatePicker;
  const [form] = BaseForm.useForm();
  const [guestForm] = BaseForm.useForm();
  const [dateForm] = BaseForm.useForm();
  const {t} = useTranslation();

  const dispatch = useAppDispatch();
  const {modalData} = useAppSelector(state => state.reservationSlice);
  const {hotelId, groupId} = useAppSelector(state => state.hotelSlice.hotelConfig);
  const [directWeb, setDirectWeb] = useState({
    direct: true,
    web: false,
  });
  const [minNightData, setMinNightData] = useState([]);

  const [selectedBalances, setSelectedBalances]: any = useState([]);
  const [balanceAmounts, setBalanceAmounts]: any = useState([]);

  const {current, selectedRooms} = useAppSelector(state => state.reservationSlice);

  const disabledDate = (current: any) => {
    const currentDate = moment().startOf('day');
    const checkinDate = moment(modalData?.reservationListResponse.checkInDate).add(1, 'day');
    return (current && current > checkinDate.startOf('day')) || (current && current < currentDate);
  };

  const calculateTotalPrice = (rooms: any[]) => {
    let totalPrice = 0;

    rooms.forEach((room: {roomPrice: number}) => {
      totalPrice += room.roomPrice;
    });

    return roundNumber(totalPrice, 2);
  };

  const initCheckInDate =
    modalData?.reservationListResponse.checkInDate && modalData?.reservationListResponse.checkInDate;
  const initCheckOutDate =
    modalData?.reservationListResponse.checkOutDate && modalData?.reservationListResponse.checkOutDate;

  const subTotalAmount = calculateTotalPrice(modalData?.reservedRoomDetailsResponseList);

  const taxList = modalData?.reservationListResponse?.taxResponseList;

  const {taxArray, totalTaxAmount} = calculateTotalTaxWithSequence(subTotalAmount, taxList, isVatApplicable);

  const totalAmount = subTotalAmount + Math.round(totalTaxAmount * 100) / 100;

  const currency = modalData?.reservationListResponse?.reservationCurrencyPrefix;

  const calculateTotalDeduction = () => {
    return balanceAmounts
      .filter((balance: any) => selectedBalances.includes(balance.id))
      .reduce((sum: any, balance: any) => sum + balance.amount, 0);
  };

  const payableAmount = subTotalAmount - calculateTotalDeduction() + Math.round(totalTaxAmount * 100) / 100;

  const populateInitialData = () => {
    form.setFieldValue('needToPayAdvance', Math.round(payableAmount).toFixed(2));
    const guestData = {
      id: modalData?.reservationListResponse?.mainGuestId,
      firstName: modalData?.reservationListResponse?.mainGuestFirstName,
      lastName: modalData?.reservationListResponse?.mainGuestLastName,
      email: modalData?.reservationListResponse?.email,
      phoneNumber: modalData?.reservationListResponse?.phoneNumber,
    };
    guestForm.setFieldsValue(guestData);
    setMainguestData(guestData);
  };

  useEffect(() => {
    populateInitialData();
  }, [totalAmount, payableAmount]);

  const handleChangePaymentType = (value: any) => {
    setpaymentType(value);
  };

  const resetModal = () => {
    // setonlinePayment('yes');
    setpaymentType('CASH');
    setinquiryUpdateType('confirm');
    onCancel();
    reloadData();
  };

  const handleSubmit = async () => {
    const formData = form.getFieldsValue();
    const updatedTaxList = taxList.map((tax: any) => ({
      ...tax,
      useReservation: tax.vat ? isVatApplicable : tax.useReservation,
    }));
    const payload = {
      id: modalData?.reservationListResponse?.reservationId,
      channelId: modalData?.reservationListResponse?.channelId,
      mainGuest: {
        email: formData.email,
      },
      resident: modalData?.reservationListResponse?.resident,
      hotelId: hotelId,
      checkInDate: modalData?.reservationListResponse?.checkInDate,
      checkOutDate: modalData?.reservationListResponse?.checkOutDate,
      advancedPayment: formData?.amount,
      paymentMethod: paymentType,
      needToPayAdvance: formData.needToPayAdvance ? Number(formData.needToPayAdvance) : 0,
      taxList: updatedTaxList,
      countryId: formData.countryId,
      reservationExpiredDate: formData.reservationExpiredDate
        ? dayjs(formData.reservationExpiredDate).format('YYYY-MM-DD')
        : null,
      reservationExpiredTime: formData.reservationExpiredTime
        ? dayjs(formData.reservationExpiredTime).format('HH:mm:ss')
        : null,
      totalAmount: Number(subTotalAmount),
      reservedRoomsRequestList: modalData?.reservedRoomDetailsResponseList,
      vatApplicable: isVatApplicable,
      invoiceCreditAmountRequestList: !isEmpty(selectedBalances)
        ? [
            {
              id: balanceAmounts[0].id,
              creditAmount: calculateTotalDeduction(),
            },
          ]
        : [],
    };

    try {
      const response = await updateReservationInquiry(payload);
      if (response.statusCode === '20000') {
        notificationController.success({message: response.message});
        resetModal();
      } else {
        notificationController.error({message: response.message});
      }
    } catch (error) {}

    //
  };

  useEffect(() => {
    if (modalData?.reservationListResponse?.vatApplicable !== undefined) {
      setisVatApplicable(modalData?.reservationListResponse?.vatApplicable);
    }
    if (initCheckOutDate && initCheckInDate) {
      dateForm.setFieldValue('dateRange', [moment(initCheckInDate), moment(initCheckOutDate)]);
    }
  }, [modalData]);

  useEffect(() => {
    (async () => {
      if (rowData && visible) {
        const status = ['INQUIRY'];
        await dispatch(getReservedRoomModalData({reservationId: rowData.reservationId, status: status}));
      }
    })();
    setSelectedBalances([]);
    fetchCreditNoteForGuest(rowData?.mainGuestId);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowData, visible]);

  const cancelBlockedReservationDetails = async (reservationId: number) => {
    try {
      const response = await cancelBlockedReservation(reservationId);
      if (response.statusCode === '20000') {
        //@ts-ignore
        notificationController.success({message: 'Your Booking has been cancelled'});
        dispatch(setCurrent(current - 1));

        resetModal();
      } else {
        notificationController.error({message: response.message});
      }
    } catch (error) {}
  };

  const handlePressAction = () => {
    if (inquiryUpdateType === 'confirm') {
      handleSubmit();
    } else {
      cancelBlockedReservationDetails(modalData.reservationListResponse.reservationId);
    }
  };

  const guestUpdate = async () => {
    try {
      const values = guestForm.getFieldsValue();
      const details = {
        id: mainguestData?.id,
        firstName: values?.firstName,
        lastName: values?.lastName,
        email: values?.email,
        phoneNumber: values?.phoneNumber,
        reservationId: modalData?.reservationListResponse?.reservationId,
        groupId: groupId,
      };
      const payload: any = {
        profile: details,
      };
      const response = await updateInquiryGuest(payload);
      if (response.statusCode === '20000') {
        notificationController.success({message: response.message});
      } else {
        notificationController.error({message: response.message});
      }
    } catch (error) {}
  };

  const handleSearch = async (newValue: string, searchField: string) => {
    let searchParams: Record<string, any> = {};
    const guestTypes = ['RESERVATION,BANQUET'];

    if (searchField === 'FIRST_NAME') {
      searchParams = {firstName: newValue};
    } else if (searchField === 'LAST_NAME') {
      searchParams = {lastName: newValue};
    } else if (searchField === 'EMAIL') {
      searchParams = {email: newValue};
    }
    const result = await getAllGuests(hotelId, searchParams, 20, 0, guestTypes);

    const data = result?.result?.Guests?.map(
      (item: {
        id: number;
        idNumber: number;
        firstName: string;
        email: string;
        phoneNumber: string;
        lastName: string;
        countryId: number;
        applicable: boolean;
      }) => ({
        value: item.id,
        id: item.id,
        label:
          searchField === 'EMAIL'
            ? item.email
            : `${item.firstName} ${item.lastName} ${item.idNumber !== null ? `-${item.idNumber}` : ''}`,
        idNumber: item.idNumber,
        firstName: item.firstName,
        phoneNumber: item.phoneNumber,
        email: item.email,
        lastName: item.lastName,
        countryId: item.countryId,
        applicable: item.applicable,
      }),
    );
    setData(data);
  };

  const handlePopulateMainguest = (guestData: {
    id: number;
    value?: number;
    idNumber?: string;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    email?: string;
    countryId?: number;
    applicable?: boolean;
  }) => {
    const {firstName, lastName, phoneNumber, email} = guestData;

    guestForm.setFieldValue('firstName', firstName);
    guestForm.setFieldValue('lastName', lastName);
    guestForm.setFieldValue('email', email);
    guestForm.setFieldValue('phoneNumber', phoneNumber);

    setMainguestData(guestData);
    setSelectedBalances([]);
    fetchCreditNoteForGuest(guestData?.value);
    setData([]);
    setisExistMain(true);
    guestForm.validateFields();
  };

  const onChangeMainGuest = (changedFieldName: string) => {
    const fields = ['firstName', 'lastName', 'email', 'phoneNumber'];
    const fieldForReset = fields.filter(o => o !== changedFieldName);
    if (mainguestData[changedFieldName] !== null) {
      guestForm.resetFields(fieldForReset);
      setMainguestData({
        id: null,
        firstName: null,
        lastName: null,
        email: null,
        phoneNumber: null,
      });
    }
    setisExistMain(false);
  };

  useEffect(() => {
    (async () => {
      const countries = await getCountries('');
      setCountry(countries);
    })();
  }, []);

  const isVatActive = checkVATAvailability(taxList);

  const checkoutUpdate = async (values: any): Promise<void> => {
    setLoadingDate(true);
    try {
      const {dateRange} = values;

      if (!dateRange || dateRange.length !== 2) {
        throw new Error('Invalid date range');
      }

      const [newCheckIn, newCheckOut] = dateRange.map(date => date.format('YYYY-MM-DD'));

      const payload = {
        id: modalData?.reservationListResponse?.reservationId,
        newCheckIn,
        newCheckOut,
        inquiryReservationRequest:
          modalData?.reservedRoomDetailsResponseList?.map((room: any) => ({
            reservedRoomId: room.reservedRoomId,
            newCheckIn,
            newCheckOut,
          })) || [],
      };

      const response = await updateReservationDates(payload);

      // Handle success or failure response
      if (response.statusCode === '20000') {
        notificationController.success({message: response.message});
        const status = ['INQUIRY'];
        await dispatch(
          getReservedRoomModalData({reservationId: response.result.reservation.newReservationId, status: status}),
        );
      } else {
        notificationController.error({message: response.message});
      }
    } catch (error) {
      console.error('Error updating dates:', error);
      notificationController.error({message: 'An error occurred while updating dates.'});
    } finally {
      setLoadingDate(false);
    }
  };

  const handleCancel = () => {
    dateForm.resetFields();
    onCancel();
  };

  const getAllMinNightData = async () => {
    try {
      const results = await getAllMinNightByIdType(hotelId, directWeb);

      const updatedArray = results.result.minimumNightConfig.map((item: {startDate: string; endDate: string}) => ({
        ...item,
        duration:
          item.startDate && item.endDate
            ? `${item.startDate.split('T')[0]} to ${item.endDate.split('T')[0]}`
            : 'Always',
      }));
      setMinNightData(updatedArray);
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    if (onlinePayment === 'no') {
      const totalAmount =
        calculateTotalPrice(modalData?.reservedRoomDetailsResponseList) + Math.round(totalTaxAmount * 100) / 100;
      form.setFieldValue('amount', Math.round(totalAmount).toFixed(2));
    } else {
      const totalAmount =
        calculateTotalPrice(modalData?.reservedRoomDetailsResponseList) + Math.round(totalTaxAmount * 100) / 100;
      form.setFieldValue('needToPayAdvance', Math.round(totalAmount).toFixed(2));
    }
  }, [visible, onlinePayment, totalTaxAmount]);

  useEffect(() => {
    getAllMinNightData();
  }, []);

  const checkDateRange = (startDate: moment.Moment, endDate: moment.Moment) => {
    for (const data of minNightData as MinNightData[]) {
      if (data.always) {
        return {isOverlapping: true, minNight: data.numOfDays};
      }
      if (data.startDate && data.endDate) {
        const rangeStart = moment(data.startDate).format('YYYY-MM-DD');
        const rangeEnd = moment(data.endDate).format('YYYY-MM-DD');

        const isOverlapping =
          startDate.isBetween(rangeStart, rangeEnd, undefined, '[]') ||
          startDate.isSame(rangeStart, 'day') ||
          startDate.isSame(rangeEnd, 'day');

        const diffDays = endDate.diff(startDate, 'days');
        if (isOverlapping) {
          const validateRange = diffDays >= data.numOfDays;
          return {isOverlapping: !validateRange, minNight: data.numOfDays, range: `${rangeStart} - ${rangeEnd}`};
        }
      }
    }
    return null; // No match found
  };

  const fetchCreditNoteForGuest = async (guestId: any) => {
    try {
      const creditNoteData = await getCreditNote(guestId, rowData.resident, hotelId);
      setBalanceAmounts(creditNoteData.result.invoiceCreditNote);
      setSelectedBalances([creditNoteData.result.invoiceCreditNote[0].id]);
    } catch (error) {
      console.error('Error fetching credit notes:', error);
    }
  };

  return (
    <Modal
      footer={
        <Space>
          <Button type="ghost">Close</Button>
          <Popconfirm
            title={`Are you sure to ${inquiryUpdateType === 'confirm' ? 'Confirm' : 'Cancel'} ?`}
            onConfirm={() =>
              inquiryUpdateType === 'confirm'
                ? form.submit()
                : cancelBlockedReservationDetails(modalData.reservationListResponse.reservationId)
            }>
            <Button danger={inquiryUpdateType !== 'confirm'} type="primary">
              {inquiryUpdateType === 'confirm' ? 'Confirm' : 'Cancel'}
            </Button>
          </Popconfirm>
        </Space>
      }
      title="Inquiry"
      width={1200}
      open={visible}
      onCancel={handleCancel}>
      <Row gutter={[16, 16]}>
        <Col style={{marginBottom: '1rem'}} md={10} lg={10} xl={10}>
          <S.BlurCardWrapper>
            <S.Card>
              <S.Padding>
                <S.Title>Booking Details</S.Title>
                <S.DateSection>
                  <S.CheckIn>
                    <S.CheckInOutText>Check-in</S.CheckInOutText>
                    <S.DateText>{formatDate(modalData && modalData?.reservationListResponse.checkInDate)}</S.DateText>
                  </S.CheckIn>
                  <S.VerticalLine />
                  <S.CheckOut>
                    <S.CheckInOutText>Check-out</S.CheckInOutText>
                    <S.DateText>{formatDate(modalData && modalData?.reservationListResponse.checkOutDate)}</S.DateText>
                  </S.CheckOut>
                </S.DateSection>
                <S.StayNights>
                  <S.RegularText>Total length of stay:</S.RegularText>
                  <S.NightCount>
                    {calculateTotalNights(
                      modalData && modalData?.reservationListResponse.checkInDate,
                      modalData && modalData?.reservationListResponse.checkOutDate,
                    )}{' '}
                    {calculateTotalNights(
                      modalData && modalData?.reservationListResponse.checkInDate,
                      modalData && modalData?.reservationListResponse.checkOutDate,
                    ) <= 1
                      ? 'Night'
                      : 'Nights'}
                  </S.NightCount>
                </S.StayNights>
                <S.HorizontalLine />
                <S.BoldTitle>Selected Stay Types: </S.BoldTitle>

                {modalData &&
                  modalData?.reservedRoomDetailsResponseList.map((stayType, index) => {
                    return (
                      <S.RoomPriceContainer key={index}>
                        <S.StayWrapper>
                          <StayTypeTitle
                            adultCount={stayType.noOfAdults}
                            childCount={stayType.noOfChildren}
                            isBold={false}
                            meal={stayType.meal}
                            name={stayType.roomTypeName}
                            size={FONT_SIZE.xs}
                          />
                        </S.StayWrapper>
                        <S.RoomPriceText>
                          {currency} {convertNumberFormatWithDecimal(stayType.roomPrice, 2)}
                        </S.RoomPriceText>
                      </S.RoomPriceContainer>
                    );
                  })}
              </S.Padding>
            </S.Card>
          </S.BlurCardWrapper>
          {isVatActive ? (
            <S.VatCard style={{marginTop: '1rem'}}>
              <S.Title>Applicable VAT</S.Title>
              <div>
                <Switch
                  checkedChildren={<CheckOutlined />}
                  unCheckedChildren={<CloseOutlined />}
                  defaultChecked
                  checked={isVatApplicable}
                  onChange={value => setisVatApplicable(value)}
                />
              </div>
            </S.VatCard>
          ) : null}
          <S.BlurCardWrapper>
            <S.Card>
              <S.Padding>
                <S.Title>{t('reservation.priceSummary')}</S.Title>
              </S.Padding>
              <S.PriceSection>
                <S.PriceWrapper>
                  <S.TPriceHeader $hasCredit={!isEmpty(selectedBalances)}>Grand Total</S.TPriceHeader>
                  <S.TPriceHeader $crossLine={!isEmpty(selectedBalances)} $hasCredit={!isEmpty(selectedBalances)}>
                    {selectedRooms && selectedRooms.stayTypes[0]?.roomDetails?.priceType} {currency}{' '}
                    {convertNumberFormatWithDecimal(totalAmount, 2)}
                  </S.TPriceHeader>
                </S.PriceWrapper>
                {!isEmpty(selectedBalances) && (
                  <S.PriceWrapper>
                    <S.TPriceHeader $hasCredit={false}>Total Payable</S.TPriceHeader>
                    <S.TPriceHeader $hasCredit={false}>
                      {selectedRooms && selectedRooms.stayTypes[0]?.roomDetails?.priceType} {currency}{' '}
                      {convertNumberFormatWithDecimal(totalAmount - calculateTotalDeduction(), 2)}
                    </S.TPriceHeader>
                  </S.PriceWrapper>
                )}
                <S.TaxWrapper>
                  <S.TaxSubHeader>(Include Taxes and Charges)</S.TaxSubHeader>
                  {/* {!isEmpty(taxList) ? (
                    <S.TaxSubHeader>
                      {`+${currency} ${convertNumberFormatWithDecimal(totalTaxAmount, 2)}`} taxes and charges
                    </S.TaxSubHeader>
                  ) : (
                    <S.TaxSubHeader>No Taxes Apply</S.TaxSubHeader>
                  )} */}
                </S.TaxWrapper>
              </S.PriceSection>
              {!isEmpty(taxList) ? (
                <S.Padding>
                  <S.Title>{t('reservation.priceBreakdown')}</S.Title>

                  <S.ListDiscountRow>
                    <S.DiscountIconWrapper>
                      <BankOutlined color={BASE_COLORS.opacityOne} />
                      <S.TaxInfoText>Room Price</S.TaxInfoText>
                    </S.DiscountIconWrapper>
                    <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(subTotalAmount, 2)}`}</S.TaxInfoText>
                  </S.ListDiscountRow>

                  {!isEmpty(taxList) ? (
                    <S.TaxInformationWapper>
                      <S.TaxLeftWrapper>
                        <BsCashStack color={BASE_COLORS.opacityOne} />
                      </S.TaxLeftWrapper>
                      <S.TaxRightWrapper>
                        <S.TaxInfoText>{`Excludes ${currency} ${convertNumberFormatWithDecimal(
                          totalTaxAmount,
                          2,
                        )} in taxes and charges`}</S.TaxInfoText>
                        <S.ListTaxWrapper>
                          {taxArray.map((tax, idx) => {
                            return (
                              <S.ListTaxRow key={`tax-list${idx}`}>
                                <S.TaxInfoText>{` ${tax.name}`}</S.TaxInfoText>
                                <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                                  tax.taxAmount,
                                  2,
                                )}`}</S.TaxInfoText>
                              </S.ListTaxRow>
                            );
                          })}
                        </S.ListTaxWrapper>
                      </S.TaxRightWrapper>
                    </S.TaxInformationWapper>
                  ) : null}
                </S.Padding>
              ) : null}
            </S.Card>
          </S.BlurCardWrapper>
        </Col>

        <Col md={14} lg={14} xl={14}>
          <BaseForm form={dateForm} onFinish={checkoutUpdate} size="middle">
            <S.ArrivalTimeWrapper>
              <S.BlueCard>
                <S.Padding>
                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <BaseForm.Item
                        name="dateRange"
                        label="Check-In / Check-Out"
                        rules={[
                          {
                            required: true,
                            message: 'Please select check-in and check-out dates',
                          },

                          ({}) => ({
                            validator(_, value) {
                              if (value && value[1].diff(value[0], 'days') >= 1) {
                                const dateDiff = value[1].diff(value[0], 'days');
                                const startDate = value[0];
                                const endDate = value[1];
                                const isInRange = checkDateRange(startDate, endDate)?.isOverlapping;
                                const minimumNight = checkDateRange(startDate, endDate)?.minNight;
                                const notValidRange = checkDateRange(startDate, endDate)?.range;

                                if (isInRange) {
                                  return Promise.reject(
                                    <p style={{display: 'block', fontSize: '14px'}}>
                                      {`Please select a minimum of ${minimumNight} nights of ${notValidRange} range`}
                                    </p>,
                                  );
                                }
                                return Promise.resolve();
                              } else {
                                return Promise.reject(
                                  <p style={{display: 'block', marginLeft: '10px', fontSize: '14px'}}>
                                    Please select a minimum of 1 night
                                  </p>,
                                );
                              }
                            },
                          }),
                        ]}>
                        <RangePicker placeholder={['Check-In', 'Check-Out']} />
                      </BaseForm.Item>
                    </Col>
                    <Col
                      xs={24}
                      md={12}
                      style={{
                        // display: 'flex',
                        display: 'block',
                        alignItems: 'center',
                        marginTop: '40px',
                      }}>
                      <Button htmlType="submit" type="primary" loading={loadingDate}>
                        Update
                      </Button>
                    </Col>
                  </Row>
                </S.Padding>
              </S.BlueCard>
            </S.ArrivalTimeWrapper>
          </BaseForm>

          <BaseForm form={guestForm} onFinish={guestUpdate} size="middle">
            <S.ArrivalTimeWrapper>
              <S.BlueCard>
                <S.Padding>
                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <BaseForm.Item
                        name="firstName"
                        label="First Name"
                        rules={[
                          {
                            required: true,
                          },
                        ]}>
                        <Input onChange={e => onChangeMainGuest('firstName')} placeholder="Enter first name" />
                      </BaseForm.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <BaseForm.Item
                        name="lastName"
                        label="Last Name"
                        rules={[
                          {
                            required: true,
                          },
                        ]}>
                        <Input onChange={e => onChangeMainGuest('lastName')} placeholder="Enter Last name" />
                      </BaseForm.Item>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <BaseForm.Item
                        name="email"
                        label="Email Address"
                        rules={[
                          {
                            validator: validateEmail,
                            required: true,
                          },
                        ]}>
                        <AutoComplete
                          options={data}
                          onSearch={value => handleSearch(value, 'EMAIL')}
                          onSelect={value => {
                            const selectedGuest = find(data, (o: {value: number}) => o.value === value);
                            if (selectedGuest !== undefined) handlePopulateMainguest(selectedGuest);
                          }}>
                          <Input onChange={e => onChangeMainGuest('email')} placeholder="Enter first name" />
                        </AutoComplete>
                      </BaseForm.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <BaseForm.Item
                        name="phoneNumber"
                        label="Phone Number"
                        rules={[
                          {
                            required: true,
                          },
                        ]}>
                        <Input onChange={e => onChangeMainGuest('phoneNumber')} placeholder="Enter Phone number" />
                      </BaseForm.Item>
                    </Col>
                  </Row>
                  <Row justify="end">
                    <Col>
                      <Popconfirm
                        placement="topRight"
                        title="Are you sure to update the guest details?"
                        onConfirm={() => guestForm.submit()}
                        okText="Yes"
                        cancelText="No">
                        <Button type="primary"> {t('commonNames.updateGuest')}</Button>
                      </Popconfirm>
                    </Col>
                  </Row>
                </S.Padding>
              </S.BlueCard>
            </S.ArrivalTimeWrapper>
          </BaseForm>

          <S.BlurCardWrapper>
            <S.BlueCard>
              <S.Padding>
                <Row>
                  <Col md={14} lg={14} xl={11}>
                    <Select
                      value={inquiryUpdateType}
                      onSelect={(value: any) => setinquiryUpdateType(value)}
                      defaultValue="confirm"
                      style={{width: '100%'}}
                      options={[
                        {
                          label: 'Confirm Inquiry',
                          value: 'confirm',
                        },
                        {
                          label: 'Cancel Inquiry',
                          value: 'cancel',
                        },
                      ]}
                    />
                  </Col>
                </Row>
              </S.Padding>
            </S.BlueCard>
          </S.BlurCardWrapper>
          {inquiryUpdateType === 'confirm' && (
            <>
              <BaseForm
                form={form}
                size="middle"
                onValuesChange={changedValues => {
                  const objKey = Object.keys(changedValues);
                  const name = objKey[0];
                  const value = changedValues[name];

                  if (name === 'paymentRequest') {
                    if (value === 'no') {
                      const totalAmount =
                        calculateTotalPrice(modalData?.reservedRoomDetailsResponseList) +
                        Math.round(totalTaxAmount * 100) / 100;
                      form.setFieldValue('amount', Math.round(payableAmount).toFixed(2));
                    } else {
                      const totalAmount =
                        calculateTotalPrice(modalData?.reservedRoomDetailsResponseList) +
                        Math.round(totalTaxAmount * 100) / 100;
                      form.setFieldValue('needToPayAdvance', Math.round(payableAmount).toFixed(2));
                    }
                  }
                }}
                onFinish={handlePressAction}>
                <>
                  <S.ArrivalTimeWrapper>
                    <S.BlueCard>
                      <S.Padding>
                        <Row gutter={{xs: 10, md: 15, xl: 30}}>
                          <Col xs={24} md={12}>
                            <BaseForm.Item
                              name="countryId"
                              label="Country"
                              rules={[
                                {
                                  required: true,
                                  message: 'Required field',
                                },
                              ]}>
                              <Select
                                showSearch
                                filterOption={false}
                                onSearch={async (name: string) => {
                                  const countries = await getCountries(name);
                                  setCountry(countries);
                                }}
                                placeholder="Select Country">
                                {modalData?.reservationListResponse.resident && (
                                  <Option key="srilanka" value={166}>
                                    Sri Lanka
                                  </Option>
                                )}
                                {!modalData?.reservationListResponse?.resident &&
                                  country?.map((post: {title: string; value: number}, key) => {
                                    return (
                                      <Option key={key} value={post.value}>
                                        {post.title}
                                      </Option>
                                    );
                                  })}
                              </Select>
                            </BaseForm.Item>
                          </Col>
                        </Row>
                        <Row gutter={{xs: 10, md: 15, xl: 30}}>
                          <Col xs={24} md={24}>
                            <BaseForm.Item
                              name="paymentRequest"
                              label={t('commonNames.sendPaymentLink')}
                              rules={[
                                {
                                  required: true,
                                  message: 'Required field',
                                },
                              ]}
                              initialValue="no">
                              <RadioGroup
                                onChange={e => {
                                  setonlinePayment(e.target.value);
                                }}>
                                <Radio value="yes" disabled>
                                  Yes
                                </Radio>
                                <Radio value="no">No</Radio>
                              </RadioGroup>
                            </BaseForm.Item>
                          </Col>
                        </Row>

                        {onlinePayment === 'yes' && (
                          <>
                            <S.FieldSetWrapper>
                              <S.FieldSet>
                                <S.Legend>Payment Deadline</S.Legend>
                                <Row gutter={{xs: 10, md: 15, xl: 30}}>
                                  <Col xs={24} md={10}>
                                    <BaseForm.Item
                                      name="reservationExpiredDate"
                                      label="Date"
                                      rules={[{required: true, message: 'Date is required'}]}>
                                      <CustomDatePicker placement="topLeft" disabledDate={disabledDate} />
                                    </BaseForm.Item>
                                  </Col>
                                  <Col xs={24} md={10}>
                                    <BaseForm.Item
                                      name="reservationExpiredTime"
                                      label="Time"
                                      rules={[{required: true, message: 'Time is required'}]}>
                                      <CustomTimePicker
                                        placement="topRight"
                                        disabledHours={() => {
                                          const currentDate = new Date();
                                          const selectedDate = form.getFieldValue('reservationExpiredDate');
                                          if (selectedDate && selectedDate.isSame(currentDate, 'day')) {
                                            return Array.from({length: currentDate.getHours()}, (_, index) => index);
                                          }
                                          return [];
                                        }}
                                        disabledMinutes={selectedHour => {
                                          const currentDate = new Date();
                                          const selectedDate = form.getFieldValue('reservationExpiredDate');
                                          if (selectedDate && selectedDate.isSame(currentDate, 'day')) {
                                            if (selectedHour === currentDate.getHours()) {
                                              return Array.from(
                                                {length: currentDate.getMinutes()},
                                                (_, index) => index,
                                              );
                                            }
                                          }
                                          return [];
                                        }}
                                      />
                                    </BaseForm.Item>
                                  </Col>
                                </Row>
                              </S.FieldSet>
                            </S.FieldSetWrapper>
                            <Row gutter={{xs: 10, md: 15, xl: 30}}>
                              <Col xs={24} md={12}>
                                <BaseForm.Item
                                  name="needToPayAdvance"
                                  label="Advance Amount"
                                  rules={[
                                    {
                                      required: false,
                                    },
                                    // eslint-disable-next-line @typescript-eslint/no-unused-vars
                                    ({getFieldValue}) => ({
                                      validator(_, value) {
                                        const input = String(value).trim();

                                        if (!input.match(NUMBER_REGEX) && String(value) !== '') {
                                          return Promise.reject('Please enter numbers only');
                                        }
                                        if (Number(value) > Number(Math.round(payableAmount))) {
                                          return Promise.reject(
                                            "Ensure advance payment doesn't exceed the Total price",
                                          );
                                        }

                                        return Promise.resolve();
                                      },
                                    }),
                                  ]}>
                                  <CurrencyInput addonBefore={`${currency}.`} />
                                </BaseForm.Item>
                              </Col>
                            </Row>
                          </>
                        )}
                      </S.Padding>
                    </S.BlueCard>
                  </S.ArrivalTimeWrapper>

                  {onlinePayment === 'no' && (
                    <S.PaymentWrapper>
                      <S.BlueCard>
                        <S.Padding>
                          <S.CardTitle>Advance Payment (Optional)</S.CardTitle>
                          <S.PaymentOptionWrapper>
                            <S.PaymentOptionSection onClick={() => handleChangePaymentType('CASH')}>
                              <S.PaymentOptionOutline $selected={paymentType === 'CASH'}>
                                <S.Image src={MONEY} />
                              </S.PaymentOptionOutline>
                              <S.PaymentType $selected={paymentType === 'CASH'}>Cash</S.PaymentType>
                            </S.PaymentOptionSection>
                            <S.PaymentOptionSection onClick={() => handleChangePaymentType('CREDITCARD')}>
                              <S.PaymentOptionOutline $selected={paymentType === 'CREDITCARD'}>
                                <S.Image src={CARD} />
                              </S.PaymentOptionOutline>
                              <S.PaymentType $selected={paymentType === 'CREDITCARD'}>Credit Card</S.PaymentType>
                            </S.PaymentOptionSection>
                          </S.PaymentOptionWrapper>
                          <Row gutter={{xs: 10, md: 15, xl: 30}}>
                            <Col xs={24} md={16}>
                              <BaseForm.Item
                                name="amount"
                                label="Amount"
                                rules={[
                                  {
                                    required: false,
                                  },
                                  ({getFieldValue}) => ({
                                    validator(_, value) {
                                      if (value === undefined) {
                                        return Promise.resolve();
                                      }
                                      const input = value.trim();
                                      if (!input.match(NUMBER_REGEX) && value !== '') {
                                        return Promise.reject('Please enter numbers only');
                                      }
                                      if (Number(value) > Number(Math.round(totalAmount))) {
                                        return Promise.reject("Ensure advance payment doesn't exceed the Total price");
                                      }
                                      return Promise.resolve();
                                    },
                                  }),
                                ]}>
                                <CurrencyInput addonBefore={`${currency}.`} />
                              </BaseForm.Item>
                            </Col>
                          </Row>
                        </S.Padding>
                      </S.BlueCard>
                    </S.PaymentWrapper>
                  )}
                </>
              </BaseForm>
            </>
          )}
        </Col>
      </Row>
    </Modal>
  );
};

export default UpdateInquiryModal;
