import authInstance from '@app/api/authInstance';
import {LOGIN_SERVICE} from '@app/api/instance';

export const getAllRolePermissionByHotelId = (
  hoteRolelId: number,
  serviceId: number | '',
): Promise<HotelRolePrivilegeResponse> =>
  authInstance
    .get<HotelRolePrivilegeResponse>(
      LOGIN_SERVICE + `api/v1/hotel-role-hotel-permission/hotel-role/${hoteRolelId}?serviceId=${serviceId}`,
    )
    .then(({data}) => data);

export const getAllRolePermissionByGroupId = (
  groupsRolelId: number,
  serviceId: number | '',
): Promise<HotelRolePrivilegeResponse> =>
  authInstance
    .get<HotelRolePrivilegeResponse>(
      LOGIN_SERVICE + `api/v1/groups-role-groups-permission/groups-role/${groupsRolelId}?serviceId=${serviceId}`,
    )
    .then(({data}) => data);

export const hotelRolePermissionUpdate = (payload: HotelRolePrivilegePayload): Promise<HotelRolePrivilegeResponse> =>
  authInstance
    .put<HotelRolePrivilegeResponse>(LOGIN_SERVICE + `api/v1/hotel-role-hotel-permission`, payload)
    .then(({data}) => data);

export const groupsRolePermissionUpdate = (payload: GroupRolePrivilegePayload): Promise<HotelRolePrivilegeResponse> =>
  authInstance
    .put<HotelRolePrivilegeResponse>(LOGIN_SERVICE + `api/v1/groups-role-groups-permission`, payload)
    .then(({data}) => data);

export const hotelRolePermissionUpdateAll = (
  payload: HotelRolePrivilegePayload[],
): Promise<HotelRolePrivilegeResponse> =>
  authInstance
    .put<HotelRolePrivilegeResponse>(LOGIN_SERVICE + `api/v1/hotel-role-hotel-permission/update-all`, payload)
    .then(({data}) => data);

export const groupsRolePermissionUpdateAll = (
  payload: GroupRolePrivilegePayload[],
): Promise<HotelRolePrivilegeResponse> =>
  authInstance
    .put<HotelRolePrivilegeResponse>(LOGIN_SERVICE + `api/v1/groups-role-groups-permission/update-all`, payload)
    .then(({data}) => data);

export const serviceRolePermissionUpdateAll = (payload: RolePrivilegePayload[]): Promise<HotelRolePrivilegeResponse> =>
  authInstance
    .put<HotelRolePrivilegeResponse>(LOGIN_SERVICE + `api/v1/service-role-services-permission-update-all`, payload)
    .then(({data}) => data);

export interface HotelRolePrivilegeResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface HotelRolePrivilegePayload {
  id: any;
  status: string;
  hotelRoleId: number;
  hotelPermissionId: number;
}
export interface GroupRolePrivilegePayload {
  id: any;
  status: string;
  groupsRoleId: number;
  groupsPermissionId: number;
}

export interface RolePrivilegePayload {
  id: any;
  status: string;
  serviceRoleId: number;
  servicePermissionId: number;
}
