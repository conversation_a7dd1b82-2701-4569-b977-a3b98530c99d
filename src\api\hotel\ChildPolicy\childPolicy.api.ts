import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const CreateChildPolicy = (payload: CreateChildPolicyProps[]): Promise<ChildPolicyResponse> => {
  return instance.post<ChildPolicyResponse>(HOTEL_SERVICE + 'child-policy', payload).then(({data}) => data);
};

export const UpdateChildPolicy = (payload: UpdateChildPolicyProps[]): Promise<ChildPolicyResponse> => {
  return instance.put<ChildPolicyResponse>(HOTEL_SERVICE + 'child-policy', payload).then(({data}) => data);
};

export const getAllChildPolicies = (id: any): Promise<ChildPolicyResponse> =>
  instance.get<ChildPolicyResponse>(HOTEL_SERVICE + `child-policy?hotelId=${id}`).then(({data}) => data);

export const getActiveChildPolicies = (id: any): Promise<ChildPolicyResponse> =>
  instance.get<ChildPolicyResponse>(HOTEL_SERVICE + `current-child-policy?hotelId=${id}`).then(({data}) => data);

export const DeleteChildPolicy = (
  adultId: number,
  childId: number,
  kidId: number,
  type: string,
): Promise<ChildPolicyResponse> => {
  const endpoint =
    type === 'ROOM'
      ? `${HOTEL_SERVICE}child-policy/${adultId},${childId},${kidId}`
      : `${HOTEL_SERVICE}child-policy/${childId},${kidId}`;

  return instance.delete<ChildPolicyResponse>(endpoint).then(({data}) => data);
};

export interface CreateChildPolicyProps {
  childPolicyReserveType: string;
  childPolicyTypes: string;
  minAge: number;
  maxAge: number;
  adult: boolean;
  paid: boolean;
  hotelId: number;
  startDate: Date;
  version: string;
}

export interface UpdateChildPolicyProps {
  id: number | undefined;
  childPolicyTypes: string;
  childPolicyReserveType: string;
  minAge: number;
  maxAge: number;
  adult: boolean;
  paid: boolean;
  startDate: Date;
  hotelId: number;
  version: any;
}

export interface ChildPolicyResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}
