import React from 'react';
import './stats.css';
import BlinkingText from '../../Restaurant/WaiterDashboard/BlinkingText';
import {MdQuestionAnswer, MdMenuBook, MdCheckCircle, MdOutlineRestaurantMenu, MdRoomService} from 'react-icons/md';
import {useResponsive} from '@app/hooks/useResponsive';

interface DashboardStatsProps {
  dashboardCount: {
    requestConfirmation?: number;
    accepted?: number;
    beingPrepared?: number;
    readyToServe?: number;
  };
  newOrderItemCount: number;
  reconfirmTables: string[];
}

const DashboardStats: React.FC<DashboardStatsProps> = ({dashboardCount, newOrderItemCount, reconfirmTables}) => {
  const {tabletOnly} = useResponsive();
  return (
    <div className="bullet-container">
      {/* Request Confirmation */}
      <div className="bullet-point request-confirmation">
        <div className="bullet">
          <div>
            <MdQuestionAnswer className="bullet-icon" />
            Request Confirmation {tabletOnly ? '' : ' :'}
          </div>
          <div className="numbering"> {dashboardCount?.requestConfirmation}</div>
        </div>
        {reconfirmTables.length > 0 && (
          <div className="reconfirm-tables">
            {reconfirmTables.length > 0 && (
              <BlinkingText
                size={tabletOnly ? 8 : 14}
                title="Table"
                texts={reconfirmTables}
                interval={1300}
                type="CHEF"
              />
            )}
          </div>
        )}
      </div>

      {/* New Orders */}

      <div className="bullet-point new-orders">
        <div className="bullet">
          <div>
            <MdMenuBook className="bullet-icon" />
            New Orders{tabletOnly ? '' : ' :'}
          </div>
          <div className="numbering">{newOrderItemCount}</div>
        </div>
      </div>

      {/* Accepted */}
      <div className="bullet-point accepted">
        <div className="bullet">
          <div>
            <MdCheckCircle className="bullet-icon" />
            Accepted{tabletOnly ? '' : ' :'}
          </div>
          <div> {dashboardCount?.accepted}</div>
        </div>
      </div>

      {/* Being Prepared */}
      <div className="bullet-point being-prepared">
        <div className="bullet">
          <div>
            <MdOutlineRestaurantMenu className="bullet-icon" />
            Being Prepared{tabletOnly ? '' : ' :'}
          </div>
          <div className="numbering">{dashboardCount?.beingPrepared}</div>
        </div>
      </div>

      {/* Ready to Serve */}
      <div className="bullet-point ready-to-serve">
        <div className="bullet">
          <div>
            <MdRoomService className="bullet-icon" />
            Ready to Serve{tabletOnly ? '' : ' :'}
          </div>
          <div className="numbering">{dashboardCount?.readyToServe}</div>
        </div>
      </div>
      {/* <Row gutter={{xs: 10, md: 15, xl: 30}} style={{width: '100%'}}>
        <Col
          md={8}
          xl={5}
          xs={24}
          sm={24}
          style={{
            display: 'flex',
            justifyContent: 'space-around',
          }}>
          <CountCard>
            <div>Request Confirmation</div>
            <div
              style={{
                fontSize: '30px',
                color: '#9d5803',
                fontWeight: 700,
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'center',
                height: 60,
                margin: 6,
              }}>
              <div>{dashboardCount?.requestConfirmation}</div>
              {reconfirmTables.length > 0 && (
                <div style={{position: 'absolute', bottom: 0, right: 0}}>
                  <BlinkingText size={14} title="Table" texts={reconfirmTables} interval={1300} type="CHEF" />
                </div>
              )}
            </div>
          </CountCard>
        </Col>
        <Col
          md={8}
          xl={5}
          xs={24}
          sm={24}
          style={{
            display: 'flex',
            justifyContent: 'space-around',
          }}>
          <CountCard>
            <div>New Orders</div>
            <div
              style={{
                fontSize: '30px',
                color: '#9d5803',
                fontWeight: 700,
              }}>
              {newOrderItemCount}
            </div>
          </CountCard>
        </Col>
        <Col md={8} xl={5} xs={24} sm={24}>
          <CountCard>
            <div>Accepted</div>
            <div
              style={{
                fontSize: '30px',
                color: '#096dd9',
                fontWeight: 700,
              }}>
              {dashboardCount?.accepted}
            </div>
          </CountCard>
        </Col>
        <Col md={8} xl={5} xs={24} sm={24}>
          <CountCard>
            <div>Being Prepared</div>
            <div
              style={{
                fontSize: '30px',
                color: '#096dd9',
                fontWeight: 700,
              }}>
              {dashboardCount?.beingPrepared}
            </div>
          </CountCard>
        </Col>
        <Col md={8} xl={4} xs={24} sm={24}>
          <CountCard>
            <div>Ready to Serve</div>
            <div
              style={{
                fontSize: '30px',
                color: '#096dd9',
                fontWeight: 700,
              }}>
              {dashboardCount?.readyToServe}
            </div>
          </CountCard>
        </Col>
      </Row>*/}
    </div>
  );
};

export default DashboardStats;
