import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const CreateOnlineRateChanger = (payload: CreateOnlineRateChangerProps): Promise<OnlineRateChangerResponse> => {
  return instance
    .post<OnlineRateChangerResponse>(HOTEL_SERVICE + 'online-rate-changer', payload)
    .then(({data}) => data);
};

export const UpdateOnlineRateChanger = (payload: UpdateOnlineRateChangerProps): Promise<OnlineRateChangerResponse> => {
  return instance.put<OnlineRateChangerResponse>(HOTEL_SERVICE + 'online-rate-changer', payload).then(({data}) => data);
};

export const getAllOnlineRateChangers = (
  hotelId: number,
  {rateName, roomTypeRoomTypeName}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<OnlineRateChangerResponse> =>
  instance
    .get<OnlineRateChangerResponse>(
      HOTEL_SERVICE +
        `online-rate-changer/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&rateName=${
          rateName ? rateName : ''
        }&roomTypeRoomTypeName=${roomTypeRoomTypeName ? roomTypeRoomTypeName : ''}&hotelId=${hotelId}`,
    )
    .then(({data}) => data);

export const DeleteOnlineRateChanger = (id: number): Promise<OnlineRateChangerResponse> =>
  instance.delete<OnlineRateChangerResponse>(HOTEL_SERVICE + `online-rate-changer/${id}`).then(({data}) => data);

export interface CreateOnlineRateChangerProps {
  rateName: string;
  fromDate: string;
  toDate: string;
  minimumStayNights: number;
  maximumStayNights: number;
  roomPrice: number;
  singlePrice?: number;
  doublePrice?: number;
  extraPersonPrice?: number;
  extraChildPrice?: number;
  bookingRateCode?: number;
  feratelRateCode?: number;
  expediaRateCode?: number;
  lastMinuteRateCode?: number;
  tablehotelsRateCode?: number;
  travelocityRateCode?: number;
  tripConnectRateCode?: number;
  agodaRateCode: number;
  roomTypeId: number;
}

export interface UpdateOnlineRateChangerProps {
  id: number;
  rateName: string;
  fromDate: string;
  toDate: string;
  minimumStayNights: number;
  maximumStayNights: number;
  roomPrice: number;
  singlePrice?: number;
  doublePrice?: number;
  extraPersonPrice?: number;
  extraChildPrice?: number;
  bookingRateCode?: number;
  feratelRateCode?: number;
  expediaRateCode?: number;
  lastMinuteRateCode?: number;
  tablehotelsRateCode?: number;
  travelocityRateCode?: number;
  tripConnectRateCode?: number;
  agodaRateCode: number;
  roomTypeId: number;
}

export interface OnlineRateChangerResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  rateName: string;
  roomTypeRoomTypeName: string;
}
