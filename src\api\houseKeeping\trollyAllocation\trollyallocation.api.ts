import houseKeepingInstance from '@app/api/houseKeepingInstance';
import {HOUSE_KEEPING_SERVICE} from '@app/api/instance';
import {ICreatePayload} from '@app/pages/houseKeeping/masterPage/trolleyAllocation/interface';

export const getAllTrollyAllocation = (hotelId: number,): Promise<TrollyAllocationResponse> =>
  houseKeepingInstance
    .get<TrollyAllocationResponse>(
      HOUSE_KEEPING_SERVICE +
        `trolleyAllocation/search?size=10&page=0&sortField=id&direction=DESC&hotelId=${hotelId}
  `
    )
    .then(({data}) => data);

export const CreateTrollyAllocation = (payload: ICreatePayload): Promise<TrollyAllocationResponse> => {
  return houseKeepingInstance
    .post<TrollyAllocationResponse>(HOUSE_KEEPING_SERVICE + 'trolleyAllocation', payload)
    .then(({data}) => data);
};

export const UpdateTrollyAllocation = (payload: ICreatePayload): Promise<TrollyAllocationResponse> => {
  return houseKeepingInstance
    .put<TrollyAllocationResponse>(HOUSE_KEEPING_SERVICE + 'trolleyAllocation', payload)
    .then(({data}) => data);
};

export const DeleteTrollyAllocation = (id: number): Promise<TrollyAllocationResponse> => {
  return houseKeepingInstance
    .delete<TrollyAllocationResponse>(HOUSE_KEEPING_SERVICE + `trolleyAllocation/${id}`)
    .then(({data}) => data);
};

export interface TrollyAllocationResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}
export interface FilterProps {
  houseKeeperId: string;
  trolleyNo: string;
  description: string;
  roomType: string;
  phoneExtention: string;
  roomName: string;
}
