import instance, {HOTEL_SERVICE} from '@app/api/instance';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const updateCleaningStatus = (payload: any): Promise<Response> => {
  return instance.put<Response>(HOTEL_SERVICE + 'room/status', payload).then(({data}) => data);
};

export interface Response {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}
