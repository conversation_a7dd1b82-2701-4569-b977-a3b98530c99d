import {BASE_COLORS} from '@app/styles/themes/constants';
import React from 'react';
import Switch from 'react-switch';
import {RiMoneyCnyCircleFill} from 'react-icons/ri';
import {TbCashOff} from 'react-icons/tb';

export default function PayableSwitch({handleChange, checked}: any) {
  return (
    <label htmlFor="small-radius-switch">
      <Switch
        checked={checked}
        onChange={handleChange}
        handleDiameter={28}
        offColor={BASE_COLORS.redBorder}
        onColor={BASE_COLORS.primary}
        offHandleColor={BASE_COLORS.primary}
        onHandleColor={BASE_COLORS.redBorder}
        height={30}
        width={80}
        borderRadius={25}
        activeBoxShadow="0px 0px 1px 2px #fffc35"
        uncheckedIcon={
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              fontSize: 15,
              color: BASE_COLORS.white,
              paddingRight: 2,
            }}>
            Free
          </div>
        }
        checkedIcon={
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              fontSize: 15,
              color: BASE_COLORS.white,
              paddingRight: 2,
            }}>
            Pay
          </div>
        }
        uncheckedHandleIcon={
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              fontSize: 15,
            }}>
            <TbCashOff color={BASE_COLORS.white} />
          </div>
        }
        checkedHandleIcon={
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              color: 'red',
              fontSize: 25,
            }}>
            <RiMoneyCnyCircleFill color={BASE_COLORS.white} />
          </div>
        }
        className="react-switch"
        id="small-radius-switch"
      />
    </label>
  );
}
