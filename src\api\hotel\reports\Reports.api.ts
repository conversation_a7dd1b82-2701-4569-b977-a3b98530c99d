import instance, {HOTEL_SERVICE} from '@app/api/instance';
import restInstance, {RESTAURANT_SERVICE} from '@app/api/resturantInstance';

export const getHotelRevenueReport = (params: IReportRequestParam, searchPayload: any): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `revenue-report?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${params.endDate}&dateType=${
          params.dateType
        }&invoiceType=${params.invoiceType}&invoiceDate=${
          searchPayload.invoiceDate ? searchPayload.invoiceDate : ''
        }&revenueNumber=${searchPayload.invoiceNo ? searchPayload.invoiceNo : ''}&reservationRefNo=${
          searchPayload.reservationNo ? searchPayload.reservationNo : ''
        }`,
    )
    .then(({data}) => data);

export const getHotelServiceChargeReport = (params: IReportRequestParam, searchPayload: any): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `reports/service-charge?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${
          params.endDate
        }&dateType=${params.dateType}&invoiceType=${params.invoiceType}&invoiceDate=${
          searchPayload.invoiceDate ? searchPayload.invoiceDate : ''
        }&reservationRefNo=${searchPayload.reservationNo ? searchPayload.reservationNo : ''}&revenueNumber=${
          searchPayload.invoiceNo ? searchPayload.invoiceNo : ''
        }`,
    )
    .then(({data}) => data);

export const getHotelAgentReport = (params: IReportRequestParam, searchPayload: any): Promise<ReportResponse> => {
  return instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `reports/sales-commission?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${
          params.endDate
        }&dateType=${params.dateType}&channelId=${params.channelId}&invoiceType=${params.invoiceType}&invoiceDate=${
          searchPayload.invoiceDate ? searchPayload.invoiceDate : ''
        }&reservationRefNo=${searchPayload.reservationNo ? searchPayload.reservationNo : ''}&guestName=${
          searchPayload.guestName ? searchPayload.guestName : ''
        }&revenueNumber=${searchPayload.invoiceNo ? searchPayload.invoiceNo : ''}`,
    )
    .then(({data}) => data);
};

export const getHotelReceiptReport = (params: IReportRequestParam, searchPayload: any): Promise<ReportResponse> => {
  return instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `total-receipt-reports?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${
          params.endDate
        }&dateType=${params.dateType}&invoiceType=${params.invoiceType}&paymentMethod=${
          params.paymentMethods ? params.paymentMethods : ''
        }&proformaInvoiceNo=${searchPayload.invoiceNo ? searchPayload.invoiceNo : ''}&receiptRefNo=${
          searchPayload.receiptNo ? searchPayload.receiptNo : ''
        }&paymentDate=${searchPayload.invoiceDate ? searchPayload.invoiceDate : ''}&paymentType=${
          searchPayload.paymentStatus ? searchPayload.paymentStatus : ''
        }&revenueNumber=${searchPayload.reservationNo ? searchPayload.reservationNo : ''}`,
    )
    .then(({data}) => data);
};

export const getHotelRefundReport = (params: IReportRequestParam, searchPayload: any): Promise<ReportResponse> => {
  return instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `refund-reports?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${params.endDate}&dateType=${
          params.dateType
        }&invoiceType=${params.invoiceType}&refundRefNumber=${
          searchPayload.refundNo ? searchPayload.refundNo : ''
        }&reservationRefNo=${searchPayload.reservationNo ? searchPayload.reservationNo : ''}&receiptRefNo=${
          searchPayload.receiptNo ? searchPayload.receiptNo : ''
        }&paymentMethod=${searchPayload.paymentMethod ? searchPayload.paymentMethod : ''}&invoiceDate=${
          searchPayload.invoiceDate ? searchPayload.invoiceDate : ''
        }&refundType=${searchPayload.refundStatus ? searchPayload.refundStatus : ''}`,
    )
    .then(({data}) => data);
};

export const getHotelCreditNoteReportDetail = (
  params: IReportRequestParam,
  searchPayload: any,
): Promise<ReportResponse> => {
  console.log({searchPayload});

  return instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `credit-note-reports?hotelId=${params?.hotelId}&startDate=${params?.startDate}&endDate=${
          params?.endDate
        }&dateType=${params?.dateType}&invoiceType=${params?.invoiceType}&paymentMethod=${
          params?.paymentMethods ? params?.paymentMethods : ''
        }  &invoiceDate=${searchPayload?.invoiceDate ? searchPayload?.invoiceDate : ''}&receiptRefNo=${
          searchPayload?.receiptNo ? searchPayload?.receiptNo : ''
        }&guestName=${searchPayload?.guestName ? searchPayload?.guestName : ''}&reservationRefNo=${
          searchPayload?.reservationNo ? searchPayload?.reservationNo : ''
        }&invoiceNo=${searchPayload?.invoiceNo ? searchPayload?.invoiceNo : ''}&currency=${
          searchPayload?.currency ? searchPayload?.currency : ''
        }
        `,
    )
    .then(({data}) => data);
};

export const getHotelProformaInvoiceReport = (params: IReportRequestParam): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `proforma-reports?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${params.endDate}&dateType=${params.dateType}&invoiceType=${params.invoiceType}`,
    )
    .then(({data}) => data);

export const getVatRegistryReport = (
  params: IReportRequestParam,
  reportType: string,
  vatNumber: string,
  searchPayload: any,
): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `vat-register-guest-report?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${
          params.endDate
        }&dateType=${params.dateType}&typeOfReport=${reportType}&vatNumber=${vatNumber}&invoiceType=${
          params.invoiceType
        }&invoiceDate=${searchPayload?.invoiceDate ? searchPayload?.invoiceDate : ''}&invoiceNo=${
          searchPayload?.invoiceNo ? searchPayload?.invoiceNo : ''
        }&purchaser=${searchPayload?.purchaser ? searchPayload?.purchaser : ''}&guestName=${
          searchPayload?.guestFirstName ? searchPayload?.guestFirstName : ''
        }&addrss=${searchPayload?.guestAddress ? searchPayload?.guestAddress : ''}&channel=${
          searchPayload?.channel ? searchPayload?.channel : ''
        }
`,
    )
    .then(({data}) => data);

export const getResturantRevenueReport = (
  params: IResturantReportRequestParam,
  searchPayload: any,
): Promise<ReportResponse> =>
  restInstance
    .get<ReportResponse>(
      RESTAURANT_SERVICE +
        `total-revenue-report?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${
          params.endDate
        }&dateType=${params.dateType}&restaurantReportCategory=${params.reportCategory}&restaurantInvoiceType=${
          params.invoiceType
        }&invoiceDate=${searchPayload?.invoiceDate ? searchPayload?.invoiceDate : ''}&roomNumber=${
          searchPayload?.reservationNo ? searchPayload?.reservationNo : ''
        }&invoiceNo=${searchPayload?.invoiceNo ? searchPayload?.invoiceNo : ''}`,
    )
    .then(({data}) => data);
export const getResturantVatRevenueReport = (
  params: IResturantReportRequestParam,
  reportType: string,
  vatNumber: string,
  searchPayload: any,
): Promise<ReportResponse> =>
  restInstance
    .get<ReportResponse>(
      RESTAURANT_SERVICE +
        `vat-registry-report?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${
          params.endDate
        }&dateType=${params.dateType}&restaurantReportCategory=${params.reportCategory}&restaurantInvoiceType=${
          params.invoiceType
        }&typeOfReport=${
          reportType === 'RESTURANT_VAT_REGISTRY_GUEST_REPORT'
            ? 'VAT_REGISTRY_GUEST_REPORT'
            : 'NON_VAT_REGISTRY_GUEST_REPORT'
        }&vatNumber=${vatNumber}&invoiceDate=${
          searchPayload?.invoiceDate ? searchPayload?.invoiceDate : ''
        }&roomNumber=${searchPayload?.reservationNo ? searchPayload?.reservationNo : ''}&purchaser=${
          searchPayload?.purchaser ? searchPayload?.purchaser : ''
        }&invoiceNo=${searchPayload?.invoiceNo ? searchPayload?.invoiceNo : ''}
`,
    )
    .then(({data}) => data);

export const getResturantServiceChargeReport = (
  params: IResturantReportRequestParam,
  searchPayload: any,
): Promise<ReportResponse> =>
  restInstance
    .get<ReportResponse>(
      RESTAURANT_SERVICE +
        `service-charge-report?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${
          params.endDate
        }&dateType=${params.dateType}&restaurantReportCategory=${params.reportCategory}&restaurantInvoiceType=${
          params.invoiceType
        }&invoiceDate=${searchPayload?.invoiceDate ? searchPayload?.invoiceDate : ''}&roomNumber=${
          searchPayload?.reservationNo ? searchPayload?.reservationNo : ''
        }&invoiceNo=${searchPayload?.invoiceNo ? searchPayload?.invoiceNo : ''}`,
    )
    .then(({data}) => data);

export const getResturantReceiptReport = (params: IResturantReportRequestParam): Promise<ReportResponse> =>
  restInstance
    .get<ReportResponse>(
      RESTAURANT_SERVICE +
        `total-receipt-report?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${params.endDate}&dateType=${params.dateType}&restaurantReportCategory=${params.reportCategory}&restaurantInvoiceType=${params.invoiceType}`,
    )
    .then(({data}) => data);

// hotel reservation report

export const getReservationSummaryReport = (params: IReportRequestParam, searchPayload: any): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `reservation-summary-reports?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${
          params.endDate
        }&dateType=${params.dateType}&invoiceType=${params.invoiceType}&checkInDate=${
          searchPayload?.checkInDate ? searchPayload?.checkInDate : ''
        }&reservationRefNo=${searchPayload?.reservationNo ? searchPayload?.reservationNo : ''}&checkOutDate=${
          searchPayload?.checkOutDate ? searchPayload?.checkOutDate : ''
        }&guestName=${searchPayload?.guestName ? searchPayload?.guestName : ''}&mealName=${
          searchPayload?.mealPlan ? searchPayload?.mealPlan : ''
        }&paymentStatus=${searchPayload?.paymentStatus ? searchPayload?.paymentStatus : ''}&roomNumber=${
          searchPayload?.roomDetails ? searchPayload?.roomDetails : ''
        }&phoneNo=${searchPayload?.telephoneNo ? searchPayload?.telephoneNo : ''}
`,
    )
    .then(({data}) => data);

export const getReservationListSummaryReport = (
  params: IReportRequestParam,
  searchPayload: any,
): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `reservation-list-summary?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${
          params.endDate
        }&dateType=${params.dateType}&invoiceType=${params.invoiceType}&guestName=${
          searchPayload?.guestName ? searchPayload?.guestName : ''
        }&reservationRefNo=${searchPayload?.reservationNo ? searchPayload?.reservationNo : ''}&checkInDate=${
          searchPayload?.checkInDate ? searchPayload?.checkInDate : ''
        }&checkOutDate=${searchPayload?.checkOutDate ? searchPayload?.checkOutDate : ''}`,
    )
    .then(({data}) => data);
['', '', '', '', 'specialArrangement'];

export const getReservationSourceReport = (params: IReportRequestParam, searchValue: any): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `reports/source-of-reservation?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${
          params.endDate
        }&dateType=${params.dateType}&invoiceType=${params.invoiceType}&channel=${searchValue ? searchValue : ''}`,
    )
    .then(({data}) => data);

export const getOccupancySummaryReport = (params: IReportRequestParam): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `occupancy-summary?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${params.endDate}&dateType=${params.dateType}&invoiceType=${params.invoiceType}`,
    )
    .then(({data}) => data);

export const getHotelTurnoverReport = (params: IReportRequestParam): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `turnover-report?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${params.endDate}&dateType=${params.dateType}&invoiceType=${params.invoiceType}`,
    )
    .then(({data}) => data);

export const getHotelGuestReport = (params: IReportRequestParam, searchPayload: any): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `guest-reports?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${params.endDate}&dateType=${
          params.dateType
        }&invoiceType=${params.invoiceType}&guestName=${
          searchPayload?.guestName ? searchPayload?.guestName : ''
        }&nationality=${searchPayload?.nationality ? searchPayload?.nationality : ''}&country=${
          searchPayload?.country ? searchPayload?.country : ''
        }&identity=${searchPayload?.identity ? searchPayload?.identity : ''}&email=${
          searchPayload?.email ? searchPayload?.email : ''
        }&phoneNo=${searchPayload?.contactNumber ? searchPayload?.contactNumber : ''}&address=${
          searchPayload?.address ? searchPayload?.address : ''
        }&dateOfBirth=${searchPayload?.dateOfBirth ? searchPayload?.dateOfBirth : ''}`,

      // &anniversary=${searchPayload?.anniversary ? searchPayload?.anniversary : ''}
    )
    .then(({data}) => data);

export const getHotelGuesHeadCountReport = (params: IReportRequestParam, searchPayload: any): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `head-count-reports?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${
          params.endDate
        }&dateType=${params.dateType}&invoiceType=${params.invoiceType}&reservationRefNo=${
          searchPayload?.reservationRefNumber ? searchPayload?.reservationRefNumber : ''
        }&roomNumber=${searchPayload?.reservedRoomNumber ? searchPayload?.reservedRoomNumber : ''}
`,
    )
    .then(({data}) => data);

export const getHotelCreditNoteReport = (params: IReportRequestParam): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `credit-note-report?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${params.endDate}&dateType=${params.dateType}&invoiceType=${params.invoiceType}`,
    )
    .then(({data}) => data);

export const getGroupTurnoverReport = (params: IReportGroupRequestParam): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `group-turnover-report?groupId=${params.groupId}&startDate=${params.startDate}&endDate=${params.endDate}&dateType=${params.dateType}&invoiceType=${params.invoiceType}`,
    )
    .then(({data}) => data);

export const exportReportData = (params: IReportExportParam): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `report-csv?startDate=${params.startDate}&dateType=${
          params.dateType === 'month' ? 'MONTH' : params.dateType === 'year' ? 'YEAR' : 'DATE'
        }&hotelId=${params.hotelId}&typeOfReport=${params.reportType}&endDate=${params.endDate}&vatNumber=${
          params.vatNumber
        }&paymentMethod=${params.paymentMethods ? params.paymentMethods : ''}&invoiceType=${
          params.invoiceType ? params.invoiceType : ''
        }`,

      {
        responseType: 'blob',
        headers: {
          ContentType: 'application/vnd.ms-excel',
        },
      },
    )
    .then(({data}) => data);

export const exportReportDataRes = (params: IReportResExportParam): Promise<ReportResponse> =>
  restInstance
    .get<ReportResponse>(
      RESTAURANT_SERVICE +
        `report-csv?startDate=${params.startDate}&dateType=${
          params.dateType === 'month' ? 'MONTH' : params.dateType === 'year' ? 'YEAR' : 'DATE'
        }&hotelId=${params.hotelId}&typeOfReport=${params.reportType}&endDate=${
          params.endDate
        }&restaurantReportCategory=${params.reportCategory}&restaurantInvoiceType=${params.invoiceType}&vatNumber=${
          params.vatNumber
        }`,
      {
        responseType: 'blob',
        headers: {
          ContentType: 'application/vnd.ms-excel',
        },
      },
    )
    .then(({data}) => data);

export const exportReportDataGroup = (params: IGroupReportExportParam): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `report-csv?startDate=${params.startDate}&dateType=${
          params.dateType === 'month' ? 'MONTH' : params.dateType === 'year' ? 'YEAR' : 'DATE'
        }&groupId=${params.groupId}&typeOfReport=${params.reportType}&endDate=${params.endDate}`,
      {
        responseType: 'blob',
        headers: {
          ContentType: 'application/vnd.ms-excel',
        },
      },
    )
    .then(({data}) => data);

export const exportAgentReportData = (params: IReportExportParam): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `report-csv?startDate=${params.startDate}&dateType=${
          params.dateType === 'month' ? 'MONTH' : params.dateType === 'year' ? 'YEAR' : 'DATE'
        }&hotelId=${params.hotelId}&typeOfReport=${params.reportType}&endDate=${params.endDate}&channelId=${
          params.channelId
        }&invoiceType=${params.invoiceType}`,
      {
        responseType: 'blob',
        headers: {
          ContentType: 'application/vnd.ms-excel',
        },
      },
    )
    .then(({data}) => data);

export const getProfomaSummaryReport = (params: IReportRequestParam, searchPayload: any): Promise<ReportResponse> =>
  instance
    .get<ReportResponse>(
      HOTEL_SERVICE +
        `proforma-reports?hotelId=${params.hotelId}&startDate=${params.startDate}&endDate=${params.endDate}&dateType=${
          params.dateType
        }&invoiceType=${params.invoiceType}&proformaInvoiceNo=${
          searchPayload.proformaNo ? searchPayload.proformaNo : ''
        }&reservationRefNo=${searchPayload.reservationNo ? searchPayload.reservationNo : ''}&checkInDate=${
          searchPayload.checkInDate ? searchPayload.checkInDate : ''
        }&checkOutDate=${searchPayload.checkOutDate ? searchPayload.checkOutDate : ''}&roomNumber=${
          searchPayload.roomDetails ? searchPayload.roomDetails : ''
        }&guestName=${searchPayload.guestName ? searchPayload.guestName : ''}&currency=${
          searchPayload.prefix ? searchPayload.prefix : ''
        }&status=${searchPayload.paymentStatus ? searchPayload.paymentStatus : ''} `,
    )
    .then(({data}) => data);

export interface ReportResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface IReportRequestParam {
  hotelId: number;
  startDate: string;
  endDate: string;
  dateType: string;
  channelId?: number;
  invoiceType: 'PROFORMA' | 'REVENUE';
  paymentMethods?: string[];
}
export interface IReportExportParam {
  hotelId: number;
  startDate: string;
  endDate: string;
  dateType: string;
  channelId?: number;
  reportType: string;
  vatNumber?: string;
  paymentMethods?: any;
  invoiceType?: any;
}
export interface IReportResExportParam {
  hotelId: number;
  startDate: string;
  endDate: string;
  dateType: string;
  channelId?: number;
  reportType: string;
  invoiceType: string;
  reportCategory: string;
  vatNumber: string;
}
export interface IGroupReportExportParam {
  groupId: number;
  startDate: string;
  endDate: string;
  dateType: string;
  channelId?: number;
  reportType: string;
}
export interface IReportGroupRequestParam {
  groupId: number;
  startDate: string;
  endDate: string;
  dateType: string;
  channelId?: number;
  invoiceType?: 'PROFORMA' | 'REVENUE';
}

export interface IResturantReportRequestParam {
  hotelId: number;
  startDate: string;
  endDate: string;
  dateType: string;
  invoiceType: string;
  reportCategory: string;
}
