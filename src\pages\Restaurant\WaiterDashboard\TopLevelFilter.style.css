.preNextButton {
  background-color: #009432;
  border: none;
  border-radius: 50px;
  padding: 6px 15px;
  margin: 10px;
  cursor: pointer;
  color: white;
  box-shadow: 4px 4px 6px #009432;
  /* z-index: 1; */
  transition: all 0.3s ease-in-out;
}

.ant-layout-sider-children {
  height: auto;
  /* margin-top: -0.1px; */
  padding-top: 0.1px;
  background-color: #2ca062;
  /* border-radius: 0 40px 40px 0; */
}

.preNextButton:hover {
  background-color: #009432;
  box-shadow: 4px 4px 6px #009432;
  transform: scale(1.1);
}

.cardcount {
  font-size: '10px';
  height: '1.5rem';
  width: '1.5rem';
  border-radius: '50%';
  background-color: '#f5be51';
  color: 'white';
}

.cardcount:hover {
  background-color: #fab938;
  color: white;
  transition: background-color 0.2s ease-in;
}
.payment-container {
  width: 100%;
  padding-top: 6px;
}

.payment-title {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 12px;
}

.payment-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 6px;
}

.payment-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  width: 40px;
  height: 30px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

.payment-button:hover {
  border-color: #d1d5db;
}

.payment-button.selected {
  border-color: #ef4444;
  background-color: #ef4444;
  color: #ffffff;
}

.payment-icon {
  font-size: 14px;
  /* margin-bottom: 8px; */
}

.payment-label {
  font-size: 12px;
  color: #4b5563;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.payment-button.selected .payment-label {
  color: #ef4444;
}
.right-container {
  padding-left: 0;
  padding-right: 0;

  @media (min-width: 768px) and (max-width: 834px) {
    padding-left: 150px !important;
    padding-right: 150px !important;
  }
}

@media (max-width: 834px) {
  .right-container {
    display: none;
  }

  .right-container.visible {
    display: block;
  }

  .show-button {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
}

@media (min-width: 835px) {
  .show-button {
    display: none !important;
  }

  .right-container {
    display: block !important;
  }
}

.pushableTop {
  /* background: hsl(235.38deg 11.93% 57.25%);
  border-radius: 12px;
  border: none;
  padding: 0;
  cursor: pointer;
  outline-offset: 4px;
  margin: 0px 7px; */
  margin: 0rem;
  font-family: 'Poppins', serif;
  padding: 0.2rem 1rem;
  cursor: pointer;
}

.pushableBottom {
  /* background: hsl(235.38deg 11.93% 57.25%);
  border-radius: 12px;
  border: none;
  padding: 0;
  cursor: pointer;
  outline-offset: 4px;
  margin: 0px 7px; */
  /* margin: 0.3rem; */
  font-family: 'Poppins', serif;
  padding: 0.2rem 1rem;
  cursor: pointer;
}

.front {
  /* display: flex;
  height: 2rem;
  padding: 8px 22px;
  padding-left: 6px;
  padding-right: 6px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  min-width: 90px;
  color: white;
  font-size: 12px;
  transform: translate3d(-1px, -4px, 2px);
  --opacity: 0.965;
  transition: all 0.1s linear;
  justify-content: center;
  align-items: center; */
  font-family: 'Poppins', serif;
}

/* .pushable:active .front {
  transform: translateY(-2px);
} */
/* width: '10.5rem',
cursor: 'pointer',
borderRadius: '10px',
boxShadow: '3px 3px 3px #bab9b6', */

.food-card-push {
  /* background: hsl(236, 10%, 69%); */
  border-radius: 12px;
  /* border: none; */
  /* padding: 10px; */
  cursor: pointer;
  /* outline-offset: 4px; */
  display: 'flex';
  flex-direction: row;
  width: '50rem';
  /* justify-content: 'center'; */
  /* margin: 0px 7px;   */
  gap: 10px;
}

.food-card-front {
  display: block;
  font-size: 1rem;
  font-weight: 500;
  width: 12.5rem;
  color: white;
  background: none;
  border: none;
  font-size: 12px;
  transform: translate3d(-3px, -3px, 2px);
  --opacity: 0.965;
  transition: all 0.05s linear;
  /* box-shadow: 3px 3px 3px #bab9b6; */

  @media (max-width: 1280px) {
    width: 11.7rem;
  }
  @media (max-width: 1196px) {
    width: 11rem;
  }
  @media (max-width: 1180px) {
    width: 10.7rem;
  }
  @media (max-width: 1024px) {
    width: 9.2rem;
  }
}

.food-card-front:hover {
  color: #ffffff;
  background-color: #ff6563;
}

.food-card-front-disable {
  display: block;
  padding: 3px 2px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  width: 10.5rem;
  color: rgb(163, 157, 157);
  font-size: 12px;
  transform: translate3d(-3px, -3px, 2px);
  --opacity: 0.965;
  transition: all 0.05s linear;
  /* box-shadow: 3px 3px 3px #bab9b6; */
}

.food-card-push:active .food-card-front {
  transform: translateY(-2px);
}

.blink-conformation-button {
  color: #1c87c9;
  /* -webkit-border-radius: 10px;
  border-radius: 10px; */
  border: none;
  color: #eeeeee;
  cursor: pointer;
  display: inline-block;
  font-family: sans-serif;
  font-size: 14px;
  padding: 2px 10px;
  font-weight: 600;
  text-align: center;
  text-decoration: none;
  margin-top: 0.2rem;
}
@keyframes glowing {
  0% {
    color: #ec393996;
    /* box-shadow: 0 0 2px #ff9f9fdc; */
    opacity: 0.5;
  }
  50% {
    color: #ec3939;
    /* box-shadow: 0 0 5px #ff9f9fdc; */
    opacity: 1;
  }
  100% {
    color: #ec393996;
    /* box-shadow: 0 0 2px #ff9f9fdc; */
    opacity: 0.3;
  }
}
.blink-conformation-button {
  animation: glowing 1300ms infinite;
}
