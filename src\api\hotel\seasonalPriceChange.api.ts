import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const createSeasonalPriceChange = (
  payload: CreateSeasonalPriceChangesProps,
): Promise<SeasonalPriceChangeResponse> => {
  return instance.post<SeasonalPriceChangeResponse>(HOTEL_SERVICE + 'seasonalPrice', payload).then(({data}) => data);
};

export const updateSeasonalPriceChange = (
  payload: UpdateSeasonalPriceChangesProps,
): Promise<SeasonalPriceChangeResponse> => {
  return instance.put<SeasonalPriceChangeResponse>(HOTEL_SERVICE + 'seasonalPrice', payload).then(({data}) => data);
};

export const updateAllSeasonalPriceChange = (
  payload: UpdateSeasonalPriceChangesProps,
): Promise<SeasonalPriceChangeResponse> => {
  return instance.put<SeasonalPriceChangeResponse>(HOTEL_SERVICE + 'seasonalPrices', payload).then(({data}) => data);
};

export const getAllSeasonalPriceChanges = (
  hotelId: number,
  {description}: FilterProps,
  pageSize: number | undefined,
  current: number | undefined,
): Promise<SeasonalPriceChangeResponse> => {
  return instance
    .get<SeasonalPriceChangeResponse>(
      HOTEL_SERVICE +
        `seasonalPrice/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&description=${
          description ? description : ''
        }&hotelId=${hotelId ? hotelId : ''}`,
    )
    .then(({data}) => data);
};
export const deleteSeasonalPriceChanges = (id: number): Promise<SeasonalPriceChangeResponse> =>
  instance.delete<SeasonalPriceChangeResponse>(HOTEL_SERVICE + `seasonalPrice/${id}`).then(({data}) => data);

export interface CreateSeasonalPriceChangesProps {
  fromDate: Date;
  toDate: Date;
  description: string;
  price: DoubleRange;
  currencyId: number;
  childPolicyId: number;
  roomTypeId: number;
  stayTypeId: number;
  channelId: number;
}

export interface UpdateSeasonalPriceChangesProps {
  id: number;
  fromDate: Date;
  toDate: Date;
  description: string;
  price: DoubleRange;
  currencyId: number;
  childPolicyId: number;
  roomTypeId: number;
  stayTypeId: number;
  channelId: number;
}

export interface SeasonalPriceChangeResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  description: string;
}
