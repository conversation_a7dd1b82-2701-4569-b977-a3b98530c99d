import inventoryInstance, {INVENTORY_SERVICE} from '@app/api/inventoryInstance';

export const CreateSupplier = (payload: CreateSupplierProps): Promise<SupplierResponse> => {
  return inventoryInstance.post<SupplierResponse>(INVENTORY_SERVICE + 'supplier', payload).then(({data}) => data);
};

export const UpdateSupplier = (payload: UpdateSupplierProps): Promise<SupplierResponse> => {
  return inventoryInstance.put<SupplierResponse>(INVENTORY_SERVICE + 'supplier', payload).then(({data}) => data);
};

export const getAllSuppliers = (
  {name, contactPerson, phoneNo}: FilterProps,
  pageSize: number | undefined,
  current: number,
  groupId: number,
): Promise<SupplierResponse> =>
  inventoryInstance
    .get<SupplierResponse>(
      INVENTORY_SERVICE +
        `supplier/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${
          name ? name : ''
        }&contactPerson=${contactPerson ? contactPerson : ''}&phoneNo=${phoneNo ? phoneNo : ''}&groupId=${groupId}`,
    )
    .then(({data}) => data);

export const searchSuppliers = (
  phoneNo: string,
  pageSize: number | undefined,
  current: number,
  groupId: number,
): Promise<SupplierResponse> =>
  inventoryInstance
    .get<SupplierResponse>(
      INVENTORY_SERVICE +
        `supplier/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=&contactPerson=&phoneNo=${
          phoneNo ? phoneNo : ''
        }&groupId=${groupId}`,
    )
    .then(({data}) => data);

export const getAllHotelSuppliers = (
  {name, contactPerson, phoneNo}: FilterProps,
  pageSize: number | undefined,
  current: number,
  hotelId: number,
  groupId: number,
): Promise<SupplierResponse> =>
  inventoryInstance
    .get<SupplierResponse>(
      INVENTORY_SERVICE +
        `supplier/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${
          name ? name : ''
        }&contactPerson=${contactPerson ? contactPerson : ''}&phoneNo=${
          phoneNo ? phoneNo : ''
        }&hotelId=${hotelId}&groupId=${groupId}`,
    )
    .then(({data}) => data);

export const DeleteSupplier = (id: number): Promise<SupplierResponse> =>
  inventoryInstance.delete<SupplierResponse>(INVENTORY_SERVICE + `supplier/${id}`).then(({data}) => data);

export const DeleteHotelSupplier = (id: number, hoteId: number): Promise<SupplierResponse> =>
  inventoryInstance
    .delete<SupplierResponse>(INVENTORY_SERVICE + `supplier/${id}/hotel/${hoteId}`)
    .then(({data}) => data);

export const getAllCountries = (name?: string): Promise<SupplierResponse> =>
  inventoryInstance
    .get<SupplierResponse>(INVENTORY_SERVICE + `country/search?name=${name ? name : ''}`)
    .then(({data}) => data);

export interface CreateSupplierProps {
  name: string;
  address: string;
  emal: string;
  phoneNo: string;
  contactPerson: string;
  remarks: string;
  countryId: number;
}

export interface UpdateSupplierProps {
  id: number;
  name: string;
  address: string;
  emal: string;
  phoneNo: string;
  contactPerson: string;
  remarks: string;
  countryId: number;
}

export interface SupplierResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  name: string;
  contactPerson: string;
  phoneNo: string;
}
