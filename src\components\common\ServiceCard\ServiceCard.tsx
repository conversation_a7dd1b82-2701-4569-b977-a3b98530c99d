import React, {FC} from 'react';
import * as S from '@app/pages/Hotel/ServiceReservation/ServiceReservation.style';
import {Col, Row} from 'antd';
import {convertNumberFormatWithDecimal} from '@app/utils/utils';
import {BaseForm} from '../forms/BaseForm/BaseForm';
import {Input} from '../inputs/Input/Input';
import {Option, Select} from '../selects/Select/Select';
import {calculateTotalForEachService} from '@app/pages/Hotel/ServiceReservation/helper/helperFunction';
import {IServicesBookinsData} from '@app/pages/Hotel/ServiceReservation/interface';

interface ServiceCardProps {
  service: IServicesBookinsData;
}

const ServiceCard: FC<ServiceCardProps> = ({service}) => {
  console.log('---------------------', service);

  const convertedServiceData = {
    quantity: service.count,
    method: service.hallType,
    numberOfDays: service.noOfDays,
    numberOfHours: service.noOfHours,
    perHourPrice: service.hourPrice,
    fullDayPrice: service.fullDayPrice,
    halfDayPrice: service.halfDayPrice,
    hallId: service.hallId,
    discountType: service.discountType,
    discountValue: service.discount,
  };
  return (
    <Row>
      <Col key={`${service.additionalServiceId}-services`} xs={24} md={24} xl={12}>
        <S.BlurCardWrapper>
          <S.WhiteCard>
            <S.Padding>
              <S.ServiceTitleWrapper>
                <S.ServiceTitle>{service.service}</S.ServiceTitle>
                <S.ServiceTitle>
                  {service.resident ? 'LKR' : 'USD'}{' '}
                  {convertNumberFormatWithDecimal(calculateTotalForEachService(convertedServiceData).finalAmount, 2)}
                </S.ServiceTitle>
              </S.ServiceTitleWrapper>

              <Row gutter={{xs: 10, md: 15, xl: 30}}>
                <Col xs={24} sm={24} md={12} xl={12}>
                  <BaseForm.Item
                    name={`${service.additionalServiceId}-discountOption`}
                    label="Discount Option"
                    rules={[{required: false}]}>
                    <Select disabled={true} placeholder="Discount Option">
                      <Option value="PERCENTAGE">Percentage</Option>
                      <Option value="AMOUNT">Amount</Option>
                    </Select>
                  </BaseForm.Item>
                </Col>
                <Col xs={24} sm={24} md={12} xl={12}>
                  <BaseForm.Item
                    name={`${service.additionalServiceId}-discountValue`}
                    label="Discount Value"
                    rules={[{required: false, message: 'Quantity is required'}]}>
                    <Input disabled={true} placeholder="Discount Value" />
                  </BaseForm.Item>
                </Col>
              </Row>
            </S.Padding>
          </S.WhiteCard>
        </S.BlurCardWrapper>
      </Col>
    </Row>
  );
};

export default ServiceCard;
