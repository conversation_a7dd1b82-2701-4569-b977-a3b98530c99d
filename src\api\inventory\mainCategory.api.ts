import inventoryInstance, {INVENTORY_SERVICE} from '@app/api/inventoryInstance';

export const CreateMainCategory = (payload: CreateMianCategoryProps): Promise<MainCategoryResponse> => {
  return inventoryInstance
    .post<MainCategoryResponse>(INVENTORY_SERVICE + 'main-category', payload)
    .then(({data}) => data);
};

export const UpdateMainCategory = (payload: UpdateMainCategoryProps): Promise<MainCategoryResponse> => {
  return inventoryInstance
    .put<MainCategoryResponse>(INVENTORY_SERVICE + 'main-category', payload)
    .then(({data}) => data);
};

export const getAllMainCategories = (
  hotelId: number,
  groupId: number,
  groupInventoryServiceId: number | undefined,
  {name}: any,
  pageSize: number | undefined,
  current: number,
): Promise<MainCategoryResponse> =>
  inventoryInstance
    .get<MainCategoryResponse>(
      INVENTORY_SERVICE +
        `main-category/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${
          name ? name : ''
        }&groupId=${groupId}&groupInventoryServiceId=${groupInventoryServiceId}&hotelId=${hotelId}`,
    )
    .then(({data}) => data);

export const DeleteMainCategory = (id: number): Promise<MainCategoryResponse> =>
  inventoryInstance.delete<MainCategoryResponse>(INVENTORY_SERVICE + `main-category/${id}`).then(({data}) => data);

export interface CreateMianCategoryProps {
  name: string;
  groupInventoryServiceId: number | undefined;
  hotelId?: number;
  description: string;
  inventoryServiceId: number | null;
}

export interface UpdateMainCategoryProps {
  id: number;
  name: string;
  groupInventoryServiceId: number | undefined;
  hotelId?: number;
  description: string;
  inventoryServiceId: number | null;
}

export interface MainCategoryResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  name: string;
}
