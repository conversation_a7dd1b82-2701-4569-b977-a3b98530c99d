import styled, {css} from 'styled-components';
import {Layout} from 'antd';
import {LAYOUT, media} from '@app/styles/themes/constants';

const {Content} = Layout;

interface HeaderProps {
  $isTwoColumnsLayout: boolean;
  $img?: any;
  $isFoodMenu?: boolean;
  $pathName?: string;
}

export default styled(Content)<HeaderProps & {$pathname: string}>`
  padding: ${({$pathname}) =>
    $pathname === '/toplevel/reservation'
      ? '0'
      : `${LAYOUT.mobile.paddingVertical} ${LAYOUT.mobile.paddingHorizontal}`};

  overflow: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-image: ${props => (props.$isFoodMenu ? `url(${props.$img})` : 'none')};
  object-fit: contain;
  background-position: center;
  background-repeat: repeat;
  background-size: cover;

  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  @media only screen and ${media.md} {
    padding: ${({$pathname}) =>
      $pathname === '/toplevel/reservation'
        ? '0'
        : `${LAYOUT.desktop.paddingVertical} ${LAYOUT.desktop.paddingHorizontal}`};
  }

  @media only screen and ${media.xl} {
    ${props =>
      props?.$isTwoColumnsLayout &&
      css`
        padding: 0;
      `}
  }
`;
