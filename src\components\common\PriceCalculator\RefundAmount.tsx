import {CalendarOutlined, CreditCardFilled, FilePdfOutlined, MoneyCollectOutlined} from '@ant-design/icons';
import {notificationController} from '@app/controllers/notificationController';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {setLoading, setModalVisible} from '@app/store/slices/commonSlice';
import {Button, Card, Checkbox, Col, Form, Input, InputNumber, Modal, Row, Select, Space} from 'antd';
import React, {useEffect, useState} from 'react';
import * as S from './PriceCalculator.style';
import {postRefundAmount} from '@app/api/UserDiscount/UserDiscount.api';
import {convertNumberFormatWithDecimal} from '@app/utils/utils';
import {getRefundData} from '@app/pages/Hotel/RoomReservation/Reservations/Modals/refundModal/refundModal.api';
import moment from 'moment';
import './Refund.style.css';
import CurrencyInput from '../inputs/CurrencyInput/CurrencyInput';
import {BASE_COLORS} from '@app/styles/themes/constants';

interface Receipt {
  receiptStatus: string;
  chargeAmount: any;
  currencyPrefix: any;
  refundAmount: any;
  id: string;
  receiptNumber: string;
  amount: string;
  numericAmount: number;
  createdAt: string;
}

interface RefundAmount {
  refundDataDetails?: any;
  reloadData?: any;
  registryType?: string;
  existingActivities?: any;
  hasDisabled?: boolean;
}

const {Option} = Select;

const RefundAmount: React.FC<RefundAmount> = ({
  refundDataDetails,
  reloadData,
  registryType,
  existingActivities,
  hasDisabled = true,
}) => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();

  const loading = useAppSelector(state => state.commonSlice.loading);
  const {hotelId} = useAppSelector(state => state.hotelSlice.hotelConfig);

  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [receiptData, setReceiptData] = useState<Receipt[]>([]);
  const [selectedReceiptData, setSelectedReceiptData] = useState<
    {amount: number; receiptId: string}[]
    // {amount: number; receiptId: string; refundType: string}[]
  >([]);
  const [balanceAmmount, setBalanceAmmount] = useState<number>(0);

  const [partialPayAmount, setPartialPayAmount] = useState<number>(0);
  const [refundStatus, setRefundStatus] = useState<string>('');
  const [selectedReceipts, setSelectedReceipts] = useState<string[]>([]);
  const [totalSelectedAmount, setTotalSelectedAmount] = useState<number>(0);
  const [lastSelectedReceipt, setLastSelectedReceipt] = useState<string | null>(null);
  const [refundMethod, setRefundMethod] = useState<string>('');
  const positiveDueAmount = Math.abs(refundDataDetails?.refundAmount);

  useEffect(() => {
    const formattedRefundAmount = convertNumberFormatWithDecimal(Number(positiveDueAmount), 2);

    form.setFieldValue('refundAmount', formattedRefundAmount);
  }, [refundDataDetails, isVisible, refundStatus]);

  const showModal = () => {
    setIsVisible(true);
    form.resetFields();

    // Reset states when modal opens
    setSelectedReceipts([]);
    setRefundStatus('');
    setPartialPayAmount(0);
    setTotalSelectedAmount(0);
  };

  //show modal
  const handleClose = () => {
    form.resetFields();
    setSelectedReceipts([]);
    setRefundStatus('');
    setPartialPayAmount(0);
    setTotalSelectedAmount(0);
    setSelectedReceiptData([]);
    setLastSelectedReceipt(null);
    setIsVisible(false);
    reloadData();
  };

  const onPaymentStatusChange = (value: string) => {
    setRefundStatus(value);
    setTotalSelectedAmount(0);
    setSelectedReceipts([]);
    setPartialPayAmount(0);
    form.setFieldValue('partialPayAmount', 0);
    setSelectedReceiptData([]);
    if (value === 'FULL_REFUND') {
      setBalanceAmmount(positiveDueAmount);
    } else {
      setBalanceAmmount(0);
    }
  };

  //Submit form
  const handleSubmit = async () => {
    await form.validateFields();
    dispatch(setLoading(true));

    const payload = {
      invoiceId: refundDataDetails?.id,
      refundType: refundStatus,
      hotelId: hotelId,
      paymentMethod: refundMethod,
      refundReceiptRequestList: selectedReceiptData,
    };
    try {
      const result = await postRefundAmount(payload);
      if (result.statusCode === '20000') {
        notificationController.success({message: result.message});
        handleClose();
        refundData(refundDataDetails?.id);
        setSelectedReceipts([]);
        setTotalSelectedAmount(0);
        setBalanceAmmount(0);
      } else {
        notificationController.error({message: result.message});
      }
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(setLoading(false));
    }
  };

  // Receipt Data
  const refundData = async (invoiceId: number) => {
    try {
      const response: any = await getRefundData(invoiceId, registryType);
      if (response?.statusCode == '20000') {
        const receipts = response?.result?.receipt.map((receipt: any) => {
          // Extract numeric value from the amount string
          const numericAmount = parseFloat(receipt.amount.replace(/[^0-9.-]+/g, ''));
          return {
            ...receipt,
            numericAmount,
          };
        });
        setReceiptData(receipts);
      }
    } catch (error) {
      console.log(error);
    }
  };

  //Card click
  const handleCardClick = (receipt: Receipt) => {
    const balanceReciptAmount = receipt?.numericAmount - receipt?.refundAmount;
    const removedAmount = balanceReciptAmount;

    if (balanceReciptAmount === 0) {
      return;
    }

    if (partialPayAmount === null && refundStatus === 'PARTIAL_REFUND') {
      notificationController.error({message: 'Please enter a partial pay amount before selecting receipts.'});
      return;
    }

    // Check if balanceAmmount is positive
    if (balanceAmmount <= 0) {
      // Unselect the card if it is currently selected

      if (selectedReceipts.includes(receipt.receiptNumber)) {
        setSelectedReceipts(prev => prev.filter(num => num !== receipt.receiptNumber));

        setTotalSelectedAmount(prev => prev - (receipt.numericAmount - receipt.refundAmount));
        setBalanceAmmount(prev => prev + (receipt.numericAmount - receipt.refundAmount));

        // Remove from selectedReceiptData
        setSelectedReceiptData(prev => {
          const filtered = prev.filter(item => item.receiptId !== receipt.id);

          // Only try to transfer the amount if balance is still needed
          if (filtered.length > 0) {
            const lastIndex = filtered.length - 1;
            const last = filtered[lastIndex];
            const amountToTransfer = Math.abs(balanceAmmount);

            const transferableAmount = Math.min(removedAmount, amountToTransfer);
            const updatedLast = {
              ...last,
              amount: last.amount + transferableAmount,
            };

            const newList = [...filtered];
            newList[lastIndex] = updatedLast;
            return newList;
          }

          return filtered;
        });
      }
      return;
    }

    const isCurrentlySelected = selectedReceipts.includes(receipt.receiptNumber);

    if (isCurrentlySelected) {
      // Unselect the receipt
      setSelectedReceipts(prev => prev.filter(num => num !== receipt.receiptNumber));

      setTotalSelectedAmount(prev => prev - balanceReciptAmount);
      setBalanceAmmount(prev => prev + balanceReciptAmount);

      if (lastSelectedReceipt === receipt.receiptNumber) {
        setLastSelectedReceipt(null);
      }

      // Update selectedReceiptData
      setSelectedReceiptData(prev => {
        const filtered = prev.filter(item => item.receiptId !== receipt.id);

        // Only try to transfer the amount if balance is still needed
        if (filtered.length > 0) {
          const lastIndex = filtered.length - 1;
          const last = filtered[lastIndex];
          const amountToTransfer = Math.abs(balanceAmmount);

          const transferableAmount = Math.min(removedAmount, amountToTransfer);
          const updatedLast = {
            ...last,
            amount: last.amount + transferableAmount,
          };

          const newList = [...filtered];
          newList[lastIndex] = updatedLast;
          return newList;
        }

        return filtered;
      });
    } else {
      // Select the receipt
      setSelectedReceipts(prev => [...prev, receipt.receiptNumber]);

      setTotalSelectedAmount(prev => prev + balanceReciptAmount);
      setBalanceAmmount(prev => prev - balanceReciptAmount);
      setLastSelectedReceipt(receipt.receiptNumber);

      // Update selectedReceiptData
      setSelectedReceiptData(prev => [
        ...prev,
        {
          amount: balanceAmmount <= balanceReciptAmount ? balanceAmmount : balanceReciptAmount,
          receiptId: receipt.id,
        },
      ]);
    }
  };

  const onPartialPayAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTotalSelectedAmount(0);
    setSelectedReceipts([]);
    setPartialPayAmount(0);
    setSelectedReceiptData([]);

    const value = e.target.value;
    const val = value.replace(/,/g, '');

    // Convert to a number safely
    const formattedValue = isNaN(Number(val)) ? 0 : Number(val);
    setPartialPayAmount(formattedValue);

    if (value !== null) {
      const numericValue = parseFloat(value);
      setBalanceAmmount(numericValue);
    }
  };

  useEffect(() => {
    if (isVisible && refundDataDetails) refundData(refundDataDetails?.id);
  }, [isVisible, refundDataDetails]);

  useEffect(() => {
    const adjustedTotal = balanceAmmount < 0 ? totalSelectedAmount - Math.abs(balanceAmmount) : totalSelectedAmount;

    form.setFieldValue('refoundTotalAmount', convertNumberFormatWithDecimal(Number(adjustedTotal), 2));
  }, [totalSelectedAmount, balanceAmmount, form]);

  return (
    <>
      <S.ActionButton
        $bgColor={positiveDueAmount <= 0 || !hasDisabled ? `${BASE_COLORS.gray} ` : 'red'}
        style={{
          cursor: positiveDueAmount <= 0 || !hasDisabled ? 'not-allowed' : 'pointer',
        }}>
        <div
          onClick={() => {
            if (positiveDueAmount > 0 || hasDisabled) {
              showModal();
            }
          }}
          style={{
            pointerEvents: positiveDueAmount <= 0 || !hasDisabled ? 'none' : 'auto',
          }}>
          <Space>
            <CreditCardFilled />
            Refund
          </Space>
        </div>
      </S.ActionButton>
      {/* <S.ActionButton
        $bgColor={positiveDueAmount <= 0 ? `${BASE_COLORS.gray} ` : 'red'}
        style={{
          cursor: positiveDueAmount <= 0 ? 'not-allowed' : 'pointer',
        }}>
        <div
          onClick={() => {
            if (positiveDueAmount > 0) {
              showModal();
            }
          }}
          style={{
            pointerEvents: positiveDueAmount <= 0 ? 'none' : 'auto',
          }}>
          <Space>
            <CreditCardFilled />
            Refund
          </Space>
        </div>
      </S.ActionButton> */}
      <Modal
        title={<span className="modal-title">Refund Amount</span>}
        width={1000}
        onCancel={handleClose}
        open={isVisible}
        footer={false}>
        <Form
          form={form}
          initialValues={{
            // refundStatus: '',
            // methodOfRefund: '',
            refoundTotalAmount: '0',
          }}
          layout="vertical">
          <Row gutter={16}>
            <Col span={4}>
              <Form.Item name="refundAmount" label="Refund Amount">
                <Input readOnly prefix={refundDataDetails?.prefix} bordered={false} />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item
                name="methodOfRefund"
                label="Method of Refund"
                rules={[{required: true, message: 'Please select the Refund Method.'}]}>
                <Select
                  style={{width: '100%'}}
                  onChange={value => setRefundMethod(value)}
                  placeholder="Select Refund Method">
                  <Option value="CASH">Cash</Option>
                  <Option value="CREDITCARD">Card</Option>
                  <Option value="BANKTRANSFER">Bank Transfer</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={5}>
              <Form.Item
                label="Refund Status"
                name="refundStatus"
                rules={[{required: true, message: 'Please select the Refund Status.'}]}>
                <Select style={{width: '100%'}} onChange={onPaymentStatusChange} placeholder="Select Refund Status">
                  <Option value="FULL_REFUND">Full Refund</Option>
                  <Option value="PARTIAL_REFUND">Partial Refund</Option>
                </Select>
              </Form.Item>
            </Col>
            {refundStatus === 'PARTIAL_REFUND' && (
              <Col span={5}>
                <Form.Item
                  name="partialPayAmount"
                  label="Partial Amount"
                  rules={[
                    {
                      required: refundStatus === 'PARTIAL_REFUND',
                      message: 'Please enter a partial pay amount.',
                    },
                    {
                      validator: (_, value) => {
                        if (value === null || value === undefined) {
                          return Promise.resolve(); // Skip validation for empty value as it's handled by the `required` rule.
                        }
                        if (value > positiveDueAmount) {
                          return Promise.reject(
                            new Error(`Amount cannot exceed ${positiveDueAmount.toLocaleString()}!`),
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}>
                  <CurrencyInput
                    min={0}
                    max={positiveDueAmount}
                    step={0.01}
                    onChange={onPartialPayAmountChange}
                    placeholder="Enter Refund amount."
                    prefix={refundDataDetails?.prefix}
                    style={{width: '100%'}}
                    value={
                      partialPayAmount !== null
                        ? parseFloat(convertNumberFormatWithDecimal(Number(partialPayAmount), 2))
                        : ''
                    }
                  />
                </Form.Item>
              </Col>
            )}

            <Col span={5}>
              <Form.Item name="refoundTotalAmount" label="Refunding Amount">
                <Input
                  value={totalSelectedAmount}
                  readOnly
                  bordered={false}
                  prefix={refundDataDetails?.prefix}
                  style={{width: '100%'}}
                />
              </Form.Item>
            </Col>
          </Row>
          <div style={{display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '16px'}}>
            {receiptData?.map(receipt => {
              const isSelected = selectedReceipts.includes(receipt.receiptNumber);

              const isLastSelected = lastSelectedReceipt === receipt.receiptNumber && balanceAmmount < 0;

              const amountNeeded = isLastSelected ? Math.abs(balanceAmmount) : 0;
              const balanceAmountReceipt = receipt?.chargeAmount - receipt?.refundAmount;
              const isPending = receipt.receiptStatus === 'PENDING';

              const refundingAmount = balanceAmountReceipt - amountNeeded;

              return (
                <div key={receipt.receiptNumber}>
                  {/* <div style={{ cursor: isPending ? 'not-allowed' : 'pointer' }}> */}
                  <Card
                    key={receipt.receiptNumber}
                    className="card"
                    onClick={() => {
                      handleCardClick(receipt);
                    }}
                    // onClick={() => !isPending && handleCardClick(receipt)} // Disabled for now
                    style={{
                      cursor: balanceAmountReceipt > 0 ? 'pointer' : 'not-allowed',
                      backgroundColor:
                        balanceAmountReceipt === 0
                          ? '#cde9cd'
                          : isLastSelected
                          ? '#334a7c'
                          : isSelected
                          ? '#334a7c'
                          : 'white',
                      opacity: balanceAmountReceipt === 0 ? 0.5 : 1,
                      pointerEvents:
                        (!isSelected && balanceAmmount <= 0) || balanceAmountReceipt === 0 ? 'none' : 'auto',
                    }}
                    extra={
                      <Checkbox
                        checked={selectedReceipts.includes(receipt.receiptNumber)}
                        disabled={(!isSelected && balanceAmmount <= 0) || balanceAmountReceipt === 0}
                      />
                    }>
                    <div className="card-header">
                      <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                        <h3 className="card-title">Receipt No: {receipt?.receiptNumber}</h3>
                        <FilePdfOutlined className="icon icon-pdf" />
                      </div>
                    </div>
                    <div className=".card-content-refundAmount">
                      <div className="receipt-detail">
                        <div className={isSelected ? 'receipt-label-refundAmount-color' : 'receipt-label-refundAmount'}>
                          <CalendarOutlined className="icon icon-calendar" />
                          <span>Receipt Date :</span>
                        </div>
                        <span
                          className={isSelected ? 'receipt-value-refundAmount-color' : 'receipt-value-refundAmount'}>
                          {receipt?.createdAt && moment(receipt.createdAt).format('YYYY-MM-DD')}
                        </span>
                      </div>
                      <div className="receipt-detail">
                        <div className={isSelected ? 'receipt-label-refundAmount-color' : 'receipt-label-refundAmount'}>
                          <MoneyCollectOutlined className="icon icon-money" />
                          <span>Receipt Amount :</span>
                        </div>
                        <span
                          className={isSelected ? 'receipt-value-refundAmount-color' : 'receipt-value-refundAmount'}>
                          {refundDataDetails?.prefix} {convertNumberFormatWithDecimal(Number(receipt?.chargeAmount), 2)}
                        </span>
                      </div>

                      <div className="receipt-detail">
                        <div className={isSelected ? 'receipt-label-refundAmount-color' : 'receipt-label-refundAmount'}>
                          <MoneyCollectOutlined className="icon icon-money" />
                          <span>Refunded Amount :</span>
                        </div>
                        <span
                          className={isSelected ? 'receipt-value-refundAmount-color' : 'receipt-value-refundAmount'}>
                          {refundDataDetails?.prefix} {convertNumberFormatWithDecimal(receipt?.refundAmount, 2)}
                        </span>
                      </div>
                      <div className="receipt-detail">
                        <div className={isSelected ? 'receipt-label-refundAmount-color' : 'receipt-label-refundAmount'}>
                          <MoneyCollectOutlined className="icon icon-money" />
                          <span>Receipt Balance Amount :</span>
                        </div>
                        <span
                          className={isSelected ? 'receipt-value-refundAmount-color' : 'receipt-value-refundAmount'}>
                          {refundDataDetails?.prefix} {convertNumberFormatWithDecimal(balanceAmountReceipt, 2)}
                        </span>
                      </div>
                      {isLastSelected && (
                        <div className="receipt-detail">
                          <div
                            className={isSelected ? 'receipt-label-refundAmount-color' : 'receipt-label-refundAmount'}>
                            <MoneyCollectOutlined className="icon" />
                            <span>Refunding Amount :</span>
                          </div>
                          <span
                            className={isSelected ? 'receipt-value-refundAmount-color' : 'receipt-value-refundAmount'}>
                            <span>{refundDataDetails?.prefix} </span>
                            {convertNumberFormatWithDecimal(refundingAmount, 2)}
                          </span>
                        </div>
                      )}
                    </div>
                  </Card>
                  {/* </div> */}
                  {isPending && (
                    <div style={{color: 'red', fontSize: '14px', marginTop: '4px', textAlign: 'center'}}>
                      This receipt has not been authorized yet
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          <Row justify="end" gutter={16}>
            <div className="modal-footer" key="footer">
              <Space>
                <Button className="modal-button" onClick={handleClose}>
                  Cancel
                </Button>
                <Button
                  className="modal-button"
                  type="primary"
                  onClick={handleSubmit}
                  disabled={selectedReceipts.length === 0 || balanceAmmount > 0 || loading}>
                  Refund
                </Button>
              </Space>
            </div>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default RefundAmount;
