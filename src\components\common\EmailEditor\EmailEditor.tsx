/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-11-26 09:21:20
 * @modify date 2024-11-26 09:21:20
 * @desc [description]
 */

import {useState, useEffect, useRef, FC} from 'react';
import {Input} from '../inputs/Input/Input';
import {Col, Row, Skeleton} from 'antd';
import {Button} from '../buttons/Button/Button';
import {BaseForm} from '../forms/BaseForm/BaseForm';
import {Select} from '../selects/Select/Select';
import {BASE_COLORS} from '@app/styles/themes/constants';
import {getMailTemplate, sendMailTemplate} from '@app/api/mail/mail.api';
import {notificationController} from '@app/controllers/notificationController';
import {isEmpty} from 'lodash';
import {EditOutlined, SendOutlined} from '@ant-design/icons';

interface Props {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onSubmitEmailCustomizer?: (data: any) => void;
  emailType: string;
  reservationId: number;
  pendingMailId: number | null;
  onSuccess?: () => void;
  isOpen: boolean;
  onSendStart: () => void;
  onSendSuccess: () => void;
  onSendError: () => void;
  setIsOpen: (open: boolean) => void;
  isEditable: boolean;
  setIsEditable: (editable: any) => void;
  getUrl?: string;
  sendUrl?: string;
  type: 'EVENT' | 'RESERVATION';
  onEmpty?: () => void;
}

const EmailEditor: FC<Props> = ({
  reservationId,
  pendingMailId,
  isOpen,
  onSendError,
  onSendStart,
  onSendSuccess,
  setIsOpen,
  isEditable,
  setIsEditable,
  onEmpty,
  getUrl = 'pending-email',
  sendUrl = 'pending-email',
  type,
}) => {
  const [mailTemplateData, setMailTemplateData]: any = useState(null);
  const [formValues, setFormValues] = useState({
    toMails: mailTemplateData?.mailToList || [],
    subject: mailTemplateData?.mailSubject || '',
    ccMails: mailTemplateData?.mailCcList || [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [htmlBody, sethtmlBody] = useState('');

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [form] = BaseForm.useForm();

  useEffect(() => {
    if (isEditable) {
      form.setFieldsValue(formValues);
    }
  }, [isEditable, formValues, form]);

  useEffect(() => {
    // setIsEditable(false);
    const fetchEmailData = async () => {
      try {
        setIsLoading(true);
        const response = await getMailTemplate(getUrl, pendingMailId);
        const data = response.result.pendingEmail;
        sethtmlBody(data.htmlBody);
        setMailTemplateData(data);
        setFormValues({
          toMails: data?.mailToList || [],
          subject: data?.mailSubject || '',
          ccMails: data?.mailCcList || [],
        });
        form.setFieldsValue({
          subject: data?.mailSubject,
        });
      } catch (error) {
        console.error('Error fetching email data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchEmailData();
  }, [form, isOpen, pendingMailId]);

  useEffect(() => {
    const updateIframeHeight = () => {
      if (iframeRef.current && iframeRef.current.contentDocument) {
        const iframeDoc = iframeRef.current.contentDocument;
        const body = iframeDoc.body;
        const html = iframeDoc.documentElement;
        const height = Math.max(body.scrollHeight, body.offsetHeight, html.scrollHeight, html.offsetHeight);
        iframeRef.current.style.height = `${height}px`;
      }
    };

    if (iframeRef.current && htmlBody) {
      const iframeDoc = iframeRef.current.contentDocument;

      if (iframeDoc) {
        iframeDoc.open();
        iframeDoc.write(htmlBody);
        iframeDoc.close();

        iframeDoc.body.innerHTML = htmlBody;

        const body = iframeDoc.body;
        body.contentEditable = isEditable ? 'true' : 'false';

        iframeDoc.documentElement.style.overflow = 'hidden';
        body.style.overflow = 'hidden';

        if (isEditable) {
          const nonEditableElements = body.querySelectorAll('.non-editable');
          nonEditableElements.forEach(el => {
            el.setAttribute('contenteditable', 'false');
          });
        }

        updateIframeHeight();

        const observer = new MutationObserver(() => updateIframeHeight());
        observer.observe(body, {attributes: true, childList: true, subtree: true});

        return () => {
          observer.disconnect();
        };
      }
    }
  }, [htmlBody, isEditable]);

  const handleFormChange = (_: any, allValues: any) => {
    setFormValues(allValues);
  };

  const handleUpdateContent = () => {
    if (iframeRef.current?.contentDocument) {
      const iframeDoc = iframeRef.current.contentDocument;
      const updatedHtml = iframeDoc.body.innerHTML;

      setMailTemplateData((prevData: any) => ({
        ...prevData,
        htmlBody: updatedHtml,
      }));
      sethtmlBody(updatedHtml);
      iframeDoc.body.innerHTML = updatedHtml;
    }
  };

  const handleSend = async () => {
    if (!mailTemplateData) {
      alert('Email data is not available.');
      return;
    }
    if (isEmpty(formValues?.toMails)) {
      notificationController.warning({message: 'Please add mail receiver'});
      return;
    }

    const iframeDoc = iframeRef.current?.contentDocument;
    if (iframeDoc) {
      const updatedHtml = iframeDoc.documentElement.outerHTML;

      let payload: any = {};
      if (type === 'EVENT') {
        payload = {
          additionalActivityPendingEmailId: mailTemplateData.id,
          additionalActivityId: reservationId,
          additionalActivityPendingEmailType: mailTemplateData.additionalActivityPendingEmailType,
          mailSubject: form.getFieldValue('subject') || mailTemplateData.mailSubject,
          mailtoList: formValues?.toMails,
          mailCcList: formValues?.ccMails || [],
          htmlBody: updatedHtml,
        };
      } else {
        payload = {
          pendingEmailId: mailTemplateData.id,
          reservationId,
          pendingEmailType: mailTemplateData.pendingEmailType,
          mailSubject: form.getFieldValue('subject') || mailTemplateData.mailSubject,
          mailtoList: formValues?.toMails,
          mailCcList: formValues?.ccMails || [],
          htmlBody: updatedHtml,
        };
      }

      try {
        onSendStart();
        const response = await sendMailTemplate(sendUrl, payload);
        if (response.statusCode === '20000') {
          notificationController.success({message: response.message});
        }
        onSendSuccess();
      } catch (error) {
        onSendError();
      }
    }
  };

  const toggleEditMode = () => {
    setIsEditable((prev: any) => !prev);
  };

  const handleRevert = () => {
    setIsEditable((prev: any) => !prev);
    setFormValues({
      toMails: mailTemplateData?.mailToList || [],
      subject: mailTemplateData?.mailSubject || '',
      ccMails: mailTemplateData?.mailCcList || [],
    });
    form.setFieldsValue({
      toMails: mailTemplateData?.mailToList || [],
      subject: mailTemplateData?.mailSubject || '',
      ccMails: mailTemplateData?.mailCcList || [],
    });
  };

  const handleUpdateForm = async () => {
    await form.validateFields();
    handleUpdateContent();
    setIsEditable((prev: any) => !prev);
  };

  function isValidEmail(email: string) {
    const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
    return emailRegex.test(email);
  }

  const handleClose = () => {
    setIsOpen(false);
    setFormValues({
      toMails: mailTemplateData?.mailToList || [],
      subject: mailTemplateData?.mailSubject || '',
      ccMails: mailTemplateData?.mailCcList || [],
    });
    sethtmlBody(mailTemplateData.htmlBody);
    if (onEmpty) {
      onEmpty();
    }
  };

  return (
    <div className="email-editor">
      <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
        <div
          style={{
            display: 'flex',
            // justifyContent: 'flex-end',
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: '10px',
            marginBottom: '10px',
            gap: '10px',
          }}>
          {isEditable && (
            <>
              <Button type="primary" onClick={handleRevert}>
                Revert
              </Button>
              <Button type="primary" onClick={handleUpdateForm}>
                Update
              </Button>
            </>
          )}
          {!isEditable && (
            <Button icon={<EditOutlined />} danger type="link" onClick={toggleEditMode}>
              Customize
            </Button>
          )}
        </div>

        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: '10px',
            marginBottom: '10px',
            gap: '10px',
          }}>
          {!isEditable && (
            <>
              <Button type="link" onClick={handleClose}>
                Skip Now
              </Button>
              <Button icon={<SendOutlined />} type="primary" onClick={() => form.submit()}>
                Send Mail
              </Button>
            </>
          )}
        </div>
      </div>

      {isLoading ? (
        <Skeleton active />
      ) : (
        <BaseForm
          initialValues={formValues}
          onValuesChange={handleFormChange}
          name="stepForm"
          form={form}
          onFinish={(values: any) => handleSend()}>
          <Row gutter={{xs: 10, md: 15, xl: 30}}>
            <Col xs={24} md={24}>
              {!isEditable ? (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: '#f5f5f5',
                    padding: '10px 15px',
                    borderRadius: '8px',
                    marginBottom: '10px',
                    // boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                  }}>
                  <span
                    style={{
                      color: BASE_COLORS.primary,
                      fontWeight: 600,
                      marginRight: '10px',
                      minWidth: '80px',
                    }}>
                    To:
                  </span>
                  {formValues?.toMails.map((mail: string, index: number) => {
                    return (
                      <>
                        <span
                          key={index}
                          style={{
                            color: '#333',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                          }}>
                          {mail}
                        </span>
                        {formValues?.toMails.length > 1 && (
                          <span style={{marginRight: '5px', marginLeft: '5px'}}>;</span>
                        )}
                      </>
                    );
                  })}
                </div>
              ) : (
                <BaseForm.Item
                  name="toMails"
                  label="To Email (Enter email and press Enter to add)"
                  rules={[
                    {
                      required: true,
                      message: 'At least one recipient is required',
                      type: 'array',
                    },
                    {
                      validator: (_, value) => {
                        if (value && value.length > 0) {
                          for (const email of value) {
                            if (!isValidEmail(email)) {
                              return Promise.reject(new Error(`${email} is not a valid email`));
                            }
                          }
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}>
                  <Select
                    onBlur={() => form.validateFields(['toMails'])}
                    mode="tags"
                    placeholder="Enter Email and press Enter"
                  />
                </BaseForm.Item>
              )}
            </Col>

            <Col xs={24} md={24}>
              {!isEditable ? (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: '#f5f5f5',
                    padding: '10px 15px',
                    borderRadius: '8px',
                    marginBottom: '10px',
                    // boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                  }}>
                  <span
                    style={{
                      color: BASE_COLORS.primary,
                      fontWeight: 600,
                      marginRight: '10px',
                      minWidth: '80px',
                    }}>
                    CC:
                  </span>
                  {mailTemplateData &&
                    formValues?.ccMails.map((mail: string, index: number) => {
                      return (
                        <>
                          <span
                            key={index}
                            style={{
                              color: '#333',
                              // flex: 1,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}>
                            {mail}
                          </span>
                          <span style={{margin: '0px 5px 0px 0px'}}>;</span>
                        </>
                      );
                    })}
                </div>
              ) : (
                <BaseForm.Item
                  name="ccMails"
                  label="CC Email ( Enter Email and enter to add )"
                  rules={[
                    {
                      required: false,
                      message: 'Email is required',
                      type: 'array',
                    },
                    {
                      validator: (_, value) => {
                        if (value && value.length > 0) {
                          for (const email of value) {
                            if (!isValidEmail(email)) {
                              return Promise.reject(new Error(`${email} is not a valid email`));
                            }
                          }
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}>
                  <Select
                    onBlur={() => form.validateFields(['ccMails'])}
                    mode="tags"
                    placeholder="Enter Email and enter to add"
                    options={[]}
                  />
                </BaseForm.Item>
              )}
            </Col>

            <Col xs={24} md={24}>
              {!isEditable ? (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: '#f5f5f5',
                    padding: '10px 15px',
                    borderRadius: '8px',
                    // boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                  }}>
                  <span
                    style={{
                      color: BASE_COLORS.primary,
                      fontWeight: 600,
                      marginRight: '10px',
                      minWidth: '80px',
                    }}>
                    Subject:
                  </span>
                  <span
                    style={{
                      color: '#333',
                      flex: 1,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}>
                    {formValues?.subject}
                  </span>
                </div>
              ) : (
                <BaseForm.Item
                  name="subject"
                  label="Mail Subject"
                  rules={[{required: true, message: 'Mail subject is required'}]}>
                  <Input placeholder="Enter subject" />
                </BaseForm.Item>
              )}
            </Col>
          </Row>
        </BaseForm>
      )}

      <div className="form-group">
        <iframe
          ref={iframeRef}
          style={{
            width: '100%',
            height: '500px',
            backgroundColor: '#f5f5f5',
            padding: '10px',
            borderRadius: '8px',
            marginBottom: '10px',
            // boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
            border: 'none',
            marginTop: '10px',
          }}
          title="Email Template Editor"
        />
      </div>
    </div>
  );
};

export default EmailEditor;
