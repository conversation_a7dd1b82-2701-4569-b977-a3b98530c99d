/* eslint-disable @typescript-eslint/no-explicit-any */
import instance, {HOTEL_SERVICE} from '@app/api/instance';

export type VatRegistryType = 'RESERVATION' | 'RESTAURANT' | 'ADDITIONAL_SERVICE' | 'ADDITIONAL_ACTIVITY';

export interface VatRegistryRequest {
  id?: number;
  name: string;
  address: string;
  vatNumber: string;
  email: string;
  hotelId: number;
  registryType: VatRegistryType[];
  groupId: number;
}

export interface VatRegistryResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export const createVatRegistry = (payload: VatRegistryRequest): Promise<VatRegistryResponse> =>
  instance.post<VatRegistryResponse>(HOTEL_SERVICE + 'vat-registry', payload).then(({data}) => data);

export const getAllVatRegistry = (
  hotelId: number,
  groupId: number,
  searchQuery: any,
  pageSize: number | undefined,
  current: number,
  status: string[],
): Promise<VatRegistryResponse> => {
  const joinedStatuses = status.join(',');
  return instance
    .get<VatRegistryResponse>(
      HOTEL_SERVICE +
        `vat-registry/search?page=${current}&size=${pageSize}&sortField=id&direction=ASC&id=&name=${
          searchQuery?.name ? searchQuery?.name : ''
        }&address=${searchQuery?.address ? searchQuery?.address : ''}&vatNumber=${
          searchQuery?.vatNumber ? searchQuery?.vatNumber : ''
        }&email=${searchQuery?.email ? searchQuery?.email : ''}&groupId=${groupId}&registryType=${joinedStatuses}`,
    )
    .then(({data}) => data);
};
export const updateVatRegistry = (payload: VatRegistryRequest): Promise<VatRegistryResponse> =>
  instance.put<VatRegistryResponse>(HOTEL_SERVICE + 'vat-registry', payload).then(({data}) => data);

export const deleteVatRegistry = (id: number): Promise<VatRegistryResponse> =>
  instance.delete<VatRegistryResponse>(HOTEL_SERVICE + `vat-registry/${id}`).then(({data}) => data);

export const searchVatRegistryUser = (email: string, vatNo: string): Promise<VatRegistryResponse> =>
  instance
    .get<VatRegistryResponse>(HOTEL_SERVICE + `vat-registry?email=${email}&vatNumber=${vatNo}`)
    .then(({data}) => data);

export const getSearchedVatDetails = (
  startDate: string,
  endDate: string,
  dateType: string,
  vatNumber: string,
  hotelId: number,
): Promise<VatRegistryResponse> =>
  instance
    .get<VatRegistryResponse>(
      HOTEL_SERVICE +
        `vat-register-guest-report?startDate=${startDate}&dateType=${dateType}&hotelId=${hotelId}&typeOfReport=VAT_REGISTRY_GUEST_REPORT&endDate=${endDate}&vatNumber=${vatNumber}`,
    )
    .then(({data}) => data);

export const getAllVatPortalDetails = (
  {vatNumber, invoiceNumber, invoiceDate, purchaser, guestFirstName}: any,
  pageSize: number | undefined,
  current: number,
  hotelId: number,
): Promise<VatRegistryResponse> =>
  instance
    .get<VatRegistryResponse>(
      HOTEL_SERVICE +
        `vat-registry/pagination/search?hotelId=${hotelId}&vatNumber=${
          vatNumber ? vatNumber : ''
        }&page=${current}&size=${pageSize}&sortField=id&direction=ASC&invoiceNumber=${
          invoiceNumber ? invoiceNumber : ''
        }&invoiceDate=${invoiceDate ? invoiceDate : ''}&purchaser=${purchaser ? purchaser : ''}&guestFirstName=${
          guestFirstName ? guestFirstName : ''
        }`,
    )
    .then(({data}) => data);
