/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-05-03 16:53:46
 * @modify date 2023-05-03 16:53:46
 * @desc [room page]
 */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Tables} from '@app/components/tables/Tables/Tables';
import {PageTitle} from '@app/components/common/PageTitle/PageTitle';
import * as S from './Room.style';
import {Space, UploadFile} from 'antd';
import {EditOutlined} from '@ant-design/icons';
import {BASE_COLORS} from '@app/styles/themes/constants';
import {ColumnsType} from 'antd/lib/table';
import {RoomStatusContent} from './RoomStatusContent';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {setIsClear, setIsEditAction, setLoading, setModalVisible} from '@app/store/slices/commonSlice';
import {TablePaginationConfig} from 'antd/es/table';
import {Button} from '@app/components/common/buttons/Button/Button';
import {FilterProps, getAllRoomStatus} from '@app/api/hotel/room.api';
import {IRoomData} from './interface';
import {HOTEL_SERVICE_MODULE_NAME, modulePermission} from '@app/utils/permissions';
import _ from 'lodash';

const RoomStatusPage: React.FC = () => {
  const {t} = useTranslation();
  const [form] = BaseForm.useForm();
  const loading = useAppSelector(state => state.commonSlice.loading);
  const isEditAction = useAppSelector(state => state.commonSlice.isEditAction);
  const isTouch = useAppSelector(state => state.commonSlice.isTouch);
  const hotelConfig = useAppSelector(state => state.hotelSlice.hotelConfig);

  //get permission
  const userPermission = useAppSelector(state => state.user.permissions);
  const permissions = modulePermission(userPermission, HOTEL_SERVICE_MODULE_NAME.ROOM);

  const [fileList, setFileList]: any = React.useState<UploadFile[]>([]);
  const [rowData, setRowData] = useState<any>({});
  const [searchPayload, setsearchPayload]: any = useState({});
  const [rooms, setrooms] = useState([]);
  const [pagination, setPagination] = React.useState<TablePaginationConfig>({current: 0, pageSize: 10, total: 0});

  const dispatch = useAppDispatch();

  const columns: ColumnsType<IRoomData> = [
    {
      title: 'Room No',
      // dataIndex: 'roomNumber',
      align: 'center',
      render: (
        // text: string,
        record: IRoomData,
      ) => {
        return <span>{record.roomNumber === 'VILLA' ? '-' : record.roomNumber}</span>;
      },
    },
    {
      title: 'Room Name',
      dataIndex: 'roomName',
      align: 'center',
      render: (text: string) => <span>{text}</span>,
    },

    {
      title: 'Room Type',
      dataIndex: 'roomType',
      align: 'center',
    },

    {
      title: 'Status',
      dataIndex: 'roomStatus',
      align: 'center',
    },
    {
      title: t('tables.actions'),
      dataIndex: 'actions',
      render: (_text: string, record: IRoomData) => {
        return (
          <Space>
            <div hidden={!permissions.EDIT}>
              <EditOutlined
                style={{color: BASE_COLORS.blue}}
                onClick={async () => {
                  dispatch(setModalVisible(true));
                  dispatch(setIsEditAction(true));
                  try {
                    const editData = {
                      id: record?.id,
                      roomStatus: record?.roomStatus,
                      onDemandStatus: record?.onDemandStatus,
                      houseKeepingStatus: record?.houseKeepingStatus,
                    };

                    form.setFieldsValue(editData);
                    setRowData(editData);
                  } catch (error) {}
                }}
              />
            </div>
          </Space>
        );
      },
      align: 'center',
    },
  ];

  const listRooms = async (searchQuery: FilterProps, pageSize: number | undefined, current: number) => {
    try {
      const result: any = await getAllRoomStatus(hotelConfig.hotelId, searchQuery, pageSize, current);
      setrooms(result?.result?.rooms);
      setPagination({
        pageSize: pageSize,
        current: result.pagination.pageNumber + 1,
        total: result.pagination.totalRecords,
      });
    } catch (error) {}
  };

  useEffect(() => {
    listRooms(searchPayload, pagination.pageSize, 0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const resetForm = () => {
    form.resetFields();
    dispatch(setLoading(false));
    setFileList([]);
  };
  const handlePagination = (pagination: TablePaginationConfig) => {
    setPagination({
      pageSize: pagination.pageSize,
      current: pagination.current ? pagination.current - 1 : 0,
      total: pagination.total,
    });
    listRooms(searchPayload, pagination.pageSize, pagination.current ? pagination.current - 1 : 0);
  };

  const onChangeTableSearch = (values: any) => {
    const obj: any = {...searchPayload, ...values};
    setsearchPayload(obj);
    listRooms(obj, pagination.pageSize, 0);
  };

  !permissions.DELETE && !permissions.EDIT && _.remove(columns, (col: any) => col.dataIndex === 'actions');

  return (
    <>
      <PageTitle>Room</PageTitle>
      <S.CurrencyWrapper>
        <S.TableWrapper>
          <Tables
            title="Room Status"
            tableData={rooms}
            columns={columns}
            searchFields={['roomNumber', 'phoneExtention', 'roomType', 'viewType', 'unitCode', 'roomName']}
            onChangeFilter={handlePagination}
            onChangeSearch={onChangeTableSearch}
            modalChildren={
              <RoomStatusContent
                fileList={fileList}
                setFileList={setFileList}
                form={form}
                rowData={rowData}
                reloadData={() =>
                  listRooms(searchPayload, pagination.pageSize, pagination.current ? pagination.current - 1 : 0)
                }
              />
            }
            isCreate={false}
            modalSize="small"
            onCancelModal={() => resetForm()}
            modalTitle={isEditAction ? 'Update Room Status' : 'Create Room Status'}
            pagination={{
              defaultPageSize: 10,
              defaultCurrent: 0,
              current: pagination.current,
              total: pagination.total,
              showSizeChanger: true,
              pageSizeOptions: ['10', '20'],
            }}
            modalFooter={
              <Space>
                {isEditAction ? (
                  <Button
                    danger
                    title="Cancel"
                    type="ghost"
                    onClick={() => {
                      dispatch(setIsEditAction(false));
                      dispatch(setModalVisible(false));
                    }}>
                    Cancel
                  </Button>
                ) : (
                  <Button
                    danger
                    title="Clear"
                    type="ghost"
                    onClick={() => {
                      resetForm();
                      dispatch(setIsClear(true));
                    }}>
                    Clear
                  </Button>
                )}
                <Button
                  title=""
                  disabled={isEditAction ? !isTouch : false}
                  type="primary"
                  loading={loading}
                  // eslint-disable-next-line @typescript-eslint/no-unused-vars
                  onClick={_e => {
                    form.submit();
                  }}>
                  {isEditAction ? 'Update' : 'Save'}
                </Button>
              </Space>
            }
          />
        </S.TableWrapper>
      </S.CurrencyWrapper>
    </>
  );
};

export default RoomStatusPage;
