/* .input-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.label {
  color: white;
  font-size: 12px;
  text-align: left;
  width: 100%;
}
.inner-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  border-bottom: 1px solid #505050;
  align-items: center;
  justify-content: space-between;
}
.date {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.date-in-number {
  color: #a06253;
  font-size: 45px;
  font-weight: bold;
  font-family: none;
}
.month-year {
  display: flex;
  flex-direction: column;
  line-height: 15px;
  margin-left: 5px;
  align-items: center;
}
.month {
  margin-bottom: 0px;
  padding: 0;
  color: #a06253;
  font-size: 15px;
  font-weight: bold;
}
.year {
  margin-top: 0px;
  padding: 0;
  color: #a06253;
  font-size: 15px;
  font-weight: bold;
}
.icon {
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2Ij4gPHBhdGggZD0iTTAgNEw4IDExTDE2IDQiIGZpbGw9IndoaXRlIi8+PC9zdmc+');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 12px;
  cursor: pointer;
  position: relative;
}
.dropdown {
  max-height: 20vh;
  overflow-y: auto;
  background-color: rgb(255, 255, 255);
  text-align: center;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.dropdown-option {
  color: black;
  background-color: #e9e9e9;
  border-bottom: 1px solid #bdbdbd;
}
.selected-number {
  color: #a06253;
  font-size: 45px;
  font-weight: bold;
  font-family: none;
} */

/* -------Responsive------ */

.input-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.label {
  color: white;
  font-size: 1vw;
  text-align: left;
  width: 100%;
  font-weight: 500;
  font-family: 'barlow';
}
.inner-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  border-bottom: 1px solid #505050;
  align-items: center;
  gap: 3vw;
  justify-content: space-between;
}
.date {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.date-in-number {
  color: #a06253;
  font-size: 3vw;
  /* font-weight: regular; */
  font-family: none;
}
.month-year {
  display: flex;
  flex-direction: column;
  line-height: 2.5vh;
  margin-left: 1vw;
  align-items: center;
  font-family: 'barlow';
}
.month {
  color: #a06253;
  font-size: 1vw;
  font-weight: 600;

  /* font-weight: bold; */
}
.year {
  color: #a06253;
  font-size: 1vw;
  font-weight: 600;

  /* font-weight: bold; */
}
.icon {
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2Ij4gPHBhdGggZD0iTTAgNEw4IDExTDE2IDQiIGZpbGw9IndoaXRlIi8+PC9zdmc+');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1vw;
  cursor: pointer;
  position: relative;
}

@media (max-width: 768px) {
  .input-container {
    width: 100%;
  }
  .label {
    font-size: 1.5vw;
    width: 100%;
  }
  .inner-container {
    width: 100%;
    gap: 8vw;
  }
  .date-in-number {
    font-size: 4vw;
  }
  .month-year {
    line-height: 2vh;
    margin-left: 0.5vw;
  }
  .month {
    font-size: 1.5vw;
  }
  .year {
    font-size: 1.5vw;
  }
  .icon {
    background-size: 1.5vw;
  }
}
@media (max-width: 480px) {
  .input-container {
    width: 100%;
  }
  .label {
    font-size: 2.5vw;
    width: 100%;
  }
  .inner-container {
    width: 100%;
    gap: 14.5vw;
  }
  .date-in-number {
    font-size: 6vw;
  }
  .month-year {
    line-height: 2vh;
    margin-left: 1vw;
  }
  .month {
    font-size: 2vw;
  }
  .year {
    font-size: 2vw;
  }
  .icon {
    background-size: 2vw;
  }
}
