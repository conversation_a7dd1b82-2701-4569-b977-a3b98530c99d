import {FormInstance, UploadFile} from 'antd';

export interface IRoomData {
  id: number;
  roomNumber: string;
  unitCode: number;
  phoneExtention: number;
  roomStatus: 'READY' | string;
  viewType: string;
  roomType: string;
  imageUrl?: any;
  roomTypeId: number;
  viewTypeId: number;
  maxAdults: number;
  maxChildren: number;
  roomName: string;
  roomWidth: number;
  roomLength: number;
  bedsId?: number;
  unitId?: string;
  onDemandStatus?: string;
  houseKeepingStatus?: string;
}

export interface FieldData {
  name: string | number;
  value?: string;
}

export interface Props {
  form: FormInstance;
  reloadData: () => void;
  rowData: IRoomData;
  fileList?: UploadFile[];
  setFileList?: string[];
}
