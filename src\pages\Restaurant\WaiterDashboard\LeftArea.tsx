/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable react/jsx-key */
/* eslint-disable prefer-const */
import * as React from 'react';
import * as S from './DashboardPage.styles';
import {Image, Tooltip} from 'antd';
import {IOrderItem} from '@app/api/getOrders.api';
import {Card} from 'antd';
import styled from 'styled-components';
import {Text} from '@app/components/Error/Error.styles';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {CategoryItem} from '../CategoryDatas';
import {FadeIn} from '../FoodCatagoryFoodLevel';
import TopLevelCard from '@app/components/cards/TopLevelCard';
import {ProductsContainer} from '../FoodCatagoryTop';
import {getCategoryStuctureByID} from '@app/api/resturant/tablecategory/categoryStucture.api';
import {setSelectedCategoryId} from '../slices/waiterDasboardSlice';
import {setCategoryData, setCategoryStatus} from '../slices/categoryNavSlice';
import {QRCodeSVG} from 'qrcode.react';
import './TopLevelFilter.style.css';
import {red} from '@mui/material/colors';
import {BASE_COLORS} from '@app/styles/themes/constants';
import {useParams} from 'react-router-dom';

const {Meta} = Card;

const CustomImage = styled(Image)`
  img {
    object-fit: cover;
  }
`;

interface ILeftArea {
  orderFilterItems: any;
  getAllFoodItemsByCategory: (id: any, name?: string) => void;
  onChangeItem: (item: IOrderItem | any, index?: any, quantity?: number) => void;
}

const LeftArea: React.FC<ILeftArea> = ({orderFilterItems, onChangeItem, getAllFoodItemsByCategory}) => {
  const dispatch = useAppDispatch();
  const categoryLists = useAppSelector(state => state.categoryNav.categoryLists);
  const categoryStatus = useAppSelector(state => state.categoryNav.categoryStatus);
  const hotelServiceConfig = useAppSelector(state => state.hotelSlice.hotelServiceConfig);
  const [previewImage, setPreviewImage] = React.useState<string>('');

  // Convert number format
  const convertNumberFormat = (number: any) => {
    const num = number.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    });
    return num;
  };

  const onChangeCard = async (data: CategoryItem) => {
    if (data.expanded) {
      // await getCategoryStuctureByID(data.id, hotelServiceConfig.serviceId);
      // dispatch(setSelectedCategoryId({selectedCategoryId: null}));
      await getCategoryById(data.id);
      data?.children.length > 0 && dispatch(setCategoryStatus({status: true}));
    } else {
      localStorage.setItem('categoryId', data.id.toString());
      await getAllFoodItemsByCategory(data.id);
      dispatch(setCategoryStatus({status: false}));
    }
  };

  const getCategoryById = async (id: number, layerIndex?: number, index?: number) => {
    const result = await getCategoryStuctureByID(id, hotelServiceConfig.serviceId);

    const data: any[] = [];

    result.result.category &&
      result.result.category.map((post: any, i: any) => {
        data.push({
          id: post.id,
          backgroundImage: post.categoryImage,
          title: post.categoryTitle,
          children: [],
          expanded: post.expanded,
          categoryId: post.categoryId,
        });
      });
    dispatch(setCategoryData({items: data}));

    // if (layerIndex === 1) {
    //   setfirstCatLayer(data);
    // } else if (layerIndex === 2) {
    // } else {
    //   dispatch(setCategoryData({items: data}));
    // }
  };

  return (
    <div>
      {!categoryStatus ? (
        <S.CardWrapper
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '20px',
          }}>
          {orderFilterItems.length === 0 ? (
            <div style={{fontFamily: '"Poppins", serif', margin: 'auto', paddingTop: '2rem', paddingBottom: '2rem'}}>
              Select food Category
            </div>
          ) : (
            orderFilterItems.map((item: IOrderItem, index: number) => {
              return (
                <div className="food-card-push">
                  <Card
                    key={index}
                    className={item.active ? 'food-card-front' : 'food-card-front-disable'}
                    onClick={() => item.active && onChangeItem(item)}
                    bodyStyle={{padding: '10px'}}
                    style={{
                      backgroundColor: item.active ? 'white' : '#c9c1c1',
                      cursor: item.active ? 'pointer' : 'default',
                      borderRadius: '12px',
                      boxShadow: '3px 6px 3px rgba(148, 143, 143, 0.3)',
                      zIndex: 10,
                    }}
                    cover={
                      <div
                        style={{
                          display: 'flex',
                          alignContent: 'center',
                          justifyContent: 'center',
                        }}>
                        <CustomImage
                          preview={false}
                          onMouseOver={() => setPreviewImage(item.itemImage)}
                          style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            objectFit: 'cover',
                            cursor: item.active ? 'pointer' : 'default',
                            opacity: !item.active ? 0.5 : 1,
                            borderRadius: '10% 10% 0 0',
                          }}
                          height={150}
                          width={'50rem'}
                          src={item.itemImage}
                        />
                        {!item.active && (
                          <span
                            style={{
                              position: 'absolute',
                              bottom: '60%',
                              paddingLeft: '23%',
                              color: 'white',
                              fontWeight: 'bolder',
                              fontSize: '16px',
                            }}>
                            Not available
                          </span>
                        )}
                      </div>
                    }>
                    {/* Item Name */}
                    {/* <Tooltip placement="bottomLeft" title={item.item}>
                      <Meta title={<Text style={{textAlign: 'center', fontSize: '1rem'}}>{item.item}</Text>} />
                    </Tooltip> */}
                    {/* Item Price */}
                    {/* <Text style={{textAlign: 'center', fontSize: '14px'}} type={item.active ? 'danger' : 'secondary'}>
                      {item.currency} {convertNumberFormat(Number(item.price).toFixed(2))}
                    </Text> */}
                    {/* <br />
                    <Text style={{textAlign: 'center', fontSize: '14px'}} type={'danger'}>
                      {!item.active ? 'Not available' : ''}
                    </Text> */}
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        flexDirection: 'column',
                      }}>
                      <h3 style={{fontSize: '16px'}}>{item.item}</h3>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'space-between',
                          flexDirection: 'row',
                        }}>
                        <p style={{color: BASE_COLORS.rmsBasicColor, fontSize: '15px'}}>
                          {item.currency} {convertNumberFormat(Number(item.price).toFixed(2))}
                        </p>
                        {/* <button>Add</button> */}
                      </div>
                    </div>
                    {/* <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        flexDirection: 'row',
                      }}>
                      <button
                        style={{
                          backgroundColor: item.active ? 'green' : 'red',
                          color: 'white',
                          fontSize: '14px',
                          padding: '6px',
                          borderRadius: '50%',
                          border: 'none',
                          cursor: 'pointer',
                        }}
                      />
                      <button
                        style={{
                          // backgroundColor: item.active ? 'green' : 'red',
                          color: 'white',
                          fontSize: '12px',
                          padding: '6px 24px',
                          borderRadius: '4px',
                          border: BASE_COLORS.rmsBasicColor,
                          cursor: 'pointer',
                          backgroundColor: 'none',
                        }}>
                        Add
                      </button>
                    </div> */}
                  </Card>
                </div>
              );
            })
          )}
        </S.CardWrapper>
      ) : (
        <ProductsContainer>
          {categoryLists.map((post: CategoryItem, i: number) => {
            return (
              <FadeIn onClick={() => onChangeCard(post)}>
                <TopLevelCard
                  scale={'0.85'}
                  key={i}
                  bgUrl={post.backgroundImage}
                  logo={null}
                  name={post.title}
                  info={''}
                />
              </FadeIn>
            );
          })}
        </ProductsContainer>
      )}
    </div>
  );
};

export default React.memo(LeftArea);
