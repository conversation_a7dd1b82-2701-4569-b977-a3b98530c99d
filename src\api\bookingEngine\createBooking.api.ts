import instance, {HOTEL_SERVICE} from '../instance';

export interface CreateBooking {
  channelId: number;
  checkInDate: string;
  checkOutDate: string;
  checkInTime: string;
  checkOutTime: string;
  hotelId: number;
  reservedRooms: ReservedRoom[];
  resident: boolean;
  taxList: Tax[];
  hotelType?: string;
  searchAdultCount: number;
  searchChildCount: number;
}

export interface ReservedRoom {
  extraChildCount: number;
  noOfAdults: number;
  noOfChildren: number;
  roomTypeId: number;
  keyHandOver: boolean;
  stayTypeId: number;
  roomPaidStatus: string;
  reservedRoomDayPriceResponseList: ReservedRoomDayPriceResponse[];
  roomIdList: number[];
}

export interface ReservedRoomDayPriceResponse {
  reservedDate: string;
  stayTypeId: number;
  usdPrice: number;
  lkrPrice: number;
  extraChildLkrPrice: number;
  extraChildUsdPrice: number;
  noOfExtraChild: number;
}

export interface Tax {
  id: number;
  name: string;
  rate: number;
  formula: string | null;
  sequence: number;
  serviceCharge: boolean;
  element: string;
  active: boolean;
  hotelId: number;
  socialSecurityContributionLevy: boolean;
  vat: boolean;
  useReservation: boolean;
}

export interface UpdateBooking {
  channelId: number;
  id: number;
  guideId?: number | null;
  countryId: number;
  checkInDate: string;
  checkOutDate: string;
  checkInTime: string;
  checkOutTime: string;
  arrivalTime: string;
  internalRemarks?: string;
  reservationExpiredDate?: string | null;
  reservationExpiredTime?: string | null;
  paymentRequest: boolean;
  resident: boolean;
  paymentMethod: string;
  mainGuest: {
    firstName: string;
    lastName: string;
    idNumber: string;
    nicNumber: boolean;
    email: string;
    phoneNumber: string;
    countryId: number;
    applicable: boolean;
    emailGuest: boolean;
    detailGuest: boolean;
  };
  hotelId: number;
  totalAmount: number;
  totalTax: number;
  reservedRooms: ReservedRoom[];
  needToPayAdvance?: number;
  taxList: Tax[];
}

export const CreateBooking = (payload: CreateBooking): Promise<BookingResponse> => {
  return instance
    .post<BookingResponse>(HOTEL_SERVICE + 'booking/reservation/reserved-room', payload)
    .then(({data}) => data);
};

export const UpdateBooking = (payload: UpdateBooking): Promise<BookingResponse> => {
  return instance
    .put<BookingResponse>(HOTEL_SERVICE + 'booking/reservation/reserved-room', payload)
    .then(({data}) => data);
};

export interface BookingResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}
