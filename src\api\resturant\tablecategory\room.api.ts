import instance from '@app/api/instance';

export interface StayTypeRequest {
  id?: number;
  stayType: string;
  key: number;

  maxAdults: number;
  maxChildren: number;
  isValidationCombination?: boolean;
  allowToBookingEng?: boolean;
  allowToBookingWidjet?: boolean;
  description: string;
  meal: 'BREAKFAST' | 'LUNCH' | 'DINNER' | string;
  hotelId: number;
}

export interface StayTypeResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export const CreateRoom = (tableCategoryPayload: StayTypeRequest): Promise<StayTypeResponse> =>
  instance.post<StayTypeResponse>('room', {...tableCategoryPayload}).then(({data}) => data);

export const getAllRoom = (): Promise<StayTypeResponse> =>
  instance
    .get<StayTypeResponse>(
      'room/search?page=0&size=10&sortField=id&direction=DESC&roomNumber=&unitCode=&viewType=&roomType=&phoneExtention=',
    )
    .then(({data}) => data);

export const UpdateRoom = (tableCategoryPayload: StayTypeRequest): Promise<StayTypeResponse> =>
  instance.put<StayTypeResponse>('table-category', {...tableCategoryPayload}).then(({data}) => data);

export const DeleteRoom = (id: number): Promise<StayTypeResponse> =>
  instance.delete<StayTypeResponse>(`table-category/${id}`).then(({data}) => data);
