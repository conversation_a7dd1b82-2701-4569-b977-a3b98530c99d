import React, {useEffect, useMemo, useState} from 'react';
// import { TopLevelCardWrap } from './TopLevelCard.style';
import * as S from './RoomCard.style';
import {Avatar, Col, Row, Tooltip} from 'antd';
import {Popover} from '../Popover/Popover';
import {CalendarSwitch} from '../CalendarSwitch/CalendarSwitch';
import {useLanguage} from '@app/hooks/useLanguage';
import deDE from 'antd/es/calendar/locale/de_DE';
import enUS from 'antd/lib/calendar/locale/en_US';
import {Dates} from '@app/constants/Dates';
import {CalendarEvent, getUserCalendar} from '@app/api/calendar.api';
import {useAppSelector} from '@app/hooks/reduxHooks';

export interface TopLevelCardProps {
  name: string;
  logo: string;
  bgUrl: string;
  onClick?: () => void;
  info?: string;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

const RoomCard: React.FC<TopLevelCardProps> = ({name, bgUrl, logo, onClick, info, onMouseEnter, onMouseLeave}) => {
  const {language} = useLanguage();
  const [calendar, setCalendar] = useState<CalendarEvent[]>([]);
  const user = useAppSelector(state => state.user.user);

  useEffect(() => {
    user && getUserCalendar(user?.id).then(res => setCalendar(res));
  }, [user]);

  const locale = useMemo(() => (language === 'de' ? deDE : enUS), [language]);

  const content = (
    <Row gutter={[20, 20]}>
      <Col span={24}>
        {/* <CalendarSwitch
        dateFormatted={dateFormatted}
        onDecrease={handleDecreaseMonth}
        onIncrease={handleIncreaseMonth}
        onToday={handleToday}
      /> */}
      </Col>

      <Col span={6}>
        <S.Calendar
          locale={locale}
          dateCellRender={value => {
            const today = Dates.getToday();

            return calendar.map(event => {
              const calendarDate = Dates.getDate(event.date);

              if (
                calendarDate.isSame(value, 'date') &&
                calendarDate.isSame(value, 'month') &&
                calendarDate.isSame(value, 'year')
              ) {
                const isPast = today.isAfter(calendarDate);

                return (
                  <S.Event key={event.date} $isPast={isPast}>
                    {calendarDate.format('DD')}
                  </S.Event>
                );
              }
            });
          }}
          // value={date}
          fullscreen={false}
          // onSelect={handleSelect}
        />
      </Col>
    </Row>
  );

  return (
    <Popover overlayStyle={{height: '17rem', width: '17rem'}} placement="topLeft" content={content} trigger="hover">
      <S.TopLevelCardWrap onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave} onClick={() => onClick} bgUrl={bgUrl}>
        <S.Wrapper>
          <Row justify={'center'}>
            <Col>
              <S.TopLevelCardTitle>{name.toUpperCase()}</S.TopLevelCardTitle>
            </Col>
          </Row>
        </S.Wrapper>
        <S.TopLevelCardInfo>{info}</S.TopLevelCardInfo>
      </S.TopLevelCardWrap>
    </Popover>
  );
};

export default RoomCard;
