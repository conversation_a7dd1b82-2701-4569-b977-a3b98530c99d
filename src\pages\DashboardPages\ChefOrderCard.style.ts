import React from 'react';
import {<PERSON>, <PERSON><PERSON>, Tag} from 'antd';
import styled from 'styled-components';

interface OrderCardProps {
  index?: number;
  isOdd?: boolean;
}

interface ActionButtonProps {
  status?: string;
}

interface ReadyToServeButtonProps {
  disabled?: boolean;
}
interface ItemStatusProps {
  status?: 'PREPARING' | 'READY' | 'SERVED' | string;
  backgroundImage?: string;
}

const OrderCard = styled(Card)<OrderCardProps>`
  width: 100%;
  // box-shadow: 0px 0px 8px #ccc;
  margin-top: ${props => (props.index === 0 ? 0 : 20)}px;
  background-color: ${props => (!props.isOdd ? '#ffff' : '#ffff')};
  .ant-card-body {
    padding: 10px 20px;
  }
`;

const CardHeader = styled.div`
  padding: 5px;
  border-bottom: 1px dashed #ccc;
`;

const BillsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  padding: 15px;
  gap: 20px;
  max-height: 430px;
  overflow-y: auto;
  background-color: #fafafa;

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
`;

const ItemBill = styled.div<ItemStatusProps>`
  width: 200px;
  min-height: 180px;
  max-height: 290px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  position: relative;
  padding-bottom: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-family: 'Inter', sans-serif;

  @media (max-width: 1200px) {
    width: 185px;
  }

  @media (max-width: 1024px) {
    width: 150px;
  }

  // background-image: ${props => (props.backgroundImage ? `url(${props.backgroundImage})` : 'none')};

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: ${props => {
      switch (props.status) {
        case 'ACCEPTED':
          return 'rgba(255, 251, 230, 0.4)';
        case 'BEING_PREPARED':
          return 'rgba(230, 247, 255, 0.4)';
        case 'READY_TO_SERVE':
          return 'rgba(246, 255, 237, 0.5)';
        default:
          return 'rgba(255, 255, 255, 0.4)';
      }
    }};
    border-radius: 7px;
    z-index: 1;
  }

  & > * {
    position: relative;
    z-index: 2;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 5%;
    width: 90%;
    height: 5px;
    background: repeating-linear-gradient(
      90deg,
      #fff,
      #fff 5px,
      rgba(255, 255, 255, 0.4) 5px,
      rgba(255, 255, 255, 0.4) 10px
    );
    z-index: 3;
  }
`;

const ImageContainer = styled.div<{color: string}>`
  width: 100%;
  height: 100px;
  overflow: hidden;
  border-top-left-radius: 7px;
  border-top-right-radius: 7px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const ItemDetailsContainer = styled.div`
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 5px;
`;

const ItemRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1px;
`;

const ItemName = styled.h3`
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin: 0 0 4px 0;
`;

const ItemQuantity = styled.div<{statusColor: string}>`
  background-color: ${({statusColor}) => statusColor};
  color: #ffffff;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 14px;
  border: 1px solid ${({statusColor}) => statusColor};
`;

const ItemRemarks = styled.div`
  font-size: 12px;
  color: #000;
  border-top: 1px solid #eee;
  padding-top: 5px;
  flex-wrap: wrap;
  display: flex;
  word-break: break-word;
  overflow-wrap: break-word;
`;

const StatusWrap = styled.div<{color: string}>`
  width: 100%;
  height: 120px;
  position: absolute;
  top: -8px;
  left: -7px;
  overflow: hidden;
  z-index: 3;

  &:before,
  &:after {
    content: '';
    position: absolute;
    background: #4d6530;
  }

  &:before {
    width: 40px;
    height: 8px;
    left: 67px;
    background: ${({color}) => color};
    border-radius: 8px 8px 0px 0px;
  }

  &:after {
    width: 8px;
    height: 40px;
    left: -1px;
    top: 63px;
    background: ${({color}) => color};
    border-radius: 0px 8px 8px 0px;
  }
`;

const StatusStamp = styled.div<{color: string}>`
  width: 150px;
  height: 34px;
  line-height: 33px;
  position: absolute;
  top: 18px;
  left: -38px;
  z-index: 3;
  color: white;
  letter-spacing: 1px;
  overflow: hidden;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  text-align: center;
  font-size: small;
  color: white;
  background: ${({color}) => color};
  border: 1px dashed white;
  box-shadow: 0 0 0 3px ${({color}) => color}, 0px 21px 5px -18px rgba(0, 0, 0, 0.6);
`;

const SpinnerContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding-top: 5px;
  padding-bottom: 5px;
`;

const ActionButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 5px;
  padding-bottom: 5px;
`;

const ActionButton = styled(Button)<ActionButtonProps>`
  width: 80%;
  font-size: 0.8rem;
  height: 30px;
  text-align: center;
  font-family: 'Inter', sans-serif;
  word-spacing: 2px;

  ${props =>
    props.status === 'READY_TO_SERVE' &&
    `
      background: #52c41a;
      border: none;

      &:hover {
        background:rgb(81, 190, 27);
      }
    `}
`;

const ReadyToServeButton = styled(Button)<ReadyToServeButtonProps>`
  width: 25%;
  font-size: 15px;
  height: 35px;
  border: none;
  margin: 0.5rem;
  font-family: 'Poppins', sans-serif;
  background-color: ${props => (props.disabled ? 'gray' : 'green')};
  &:hover {
    background-color: ${props => (props.disabled ? 'gray' : '#52c41a')};
  }
`;

const ReadyToServeAllButton = styled(Button)<ReadyToServeButtonProps>`
  width: 25%;
  font-size: 15px;
  height: 35px;
  border: none;
  margin: 0.5rem;
  font-family: 'Poppins', sans-serif;
  background-color: ${props => (props.disabled ? 'gray' : 'green')};
  &:hover {
    background-color: ${props => (props.disabled ? 'gray' : '#52c41a')};
  }
`;

const PrepareAllButton = styled(Button)<ReadyToServeButtonProps>`
  width: 25%;
  font-size: 15px;
  height: 35px;
  border: none;
  margin: 0.5rem;
  font-family: 'Poppins', sans-serif;
  background-color: ${props => (props.disabled ? 'gray' : 'green')};
  &:hover {
    background-color: ${props => (props.disabled ? 'gray' : '#52c41a')};
  }
`;

// Export components
export {
  OrderCard,
  StatusStamp,
  ItemBill,
  ItemName,
  BillsContainer,
  ItemDetailsContainer,
  ButtonContainer,
  ReadyToServeButton,
  ReadyToServeAllButton,
  PrepareAllButton,
  ItemQuantity,
  ImageContainer,
  ActionButton,
  SpinnerContainer,
  CardHeader,
  StatusWrap,
  ItemRemarks,
  ItemRow,
  ActionButtonContainer,
};
