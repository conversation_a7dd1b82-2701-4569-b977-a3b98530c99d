import {RReceiptData} from '@app/pages/Restaurant/RestReceipt/RestReceiptPage';
import restInstance, {RESTAURANT_SERVICE} from '../resturantInstance';

import instance, {HOTEL_SERVICE} from '../instance';

export const getAllRestInvoives = (hotelServiceId: number, searchQuery: FilterSearchInvResProps): Promise<any> =>
  restInstance
    .get<any>(
      RESTAURANT_SERVICE +
        `invoice/hotel/${hotelServiceId}?invoiceNo=${
          searchQuery.invoiceNo ? searchQuery.invoiceNo : ''
        }&reservationNo=${searchQuery.reservationNo ? searchQuery.reservationNo : ''}
`,
    )
    .then(({data}) => data);

export const printInvoice = (id: number, invoiceType: string): Promise<any> =>
  restInstance.get<any>(RESTAURANT_SERVICE + `invoice/print/${id}?invoiceType=${invoiceType}`).then(({data}) => data);

export const makeRestInvoicePayment = (id: any, amount: number): Promise<any> =>
  restInstance.put<any>(RESTAURANT_SERVICE + `invoice/payment/${id}?amount=${amount}`).then(({data}) => data);

export const updateInvoicePaymentType = (id: number, type: 'CASH' | 'CREDITCARD'): Promise<any> =>
  restInstance
    .put<any>(RESTAURANT_SERVICE + `invoice/payment-paid-method/update/${id}?paymentPaidMethod=${type}`)
    .then(({data}) => data);

// Restorent receipt
export const getAllRestReceipt = (
  hotelId: number,
  startDate: any,
  endDate: any,
  pageSize: number | undefined,
  current: number,
  registryType: string,
  searchQuery: FilterSearchReProps,
): Promise<any> =>
  instance
    .get<any>(
      HOTEL_SERVICE +
        `payment/receipts?page=${current}&size=${pageSize}&sortField=id&direction=DESC&hotelId=${hotelId}&registryType=${registryType}&proformaInvoiceNo=${
          searchQuery.proformaInvoiceNumber ? searchQuery.proformaInvoiceNumber : ''
        }&receiptRefNo=${searchQuery.receiptNumber ? searchQuery.receiptNumber : ''}`,
    )
    .then(({data}) => {
      return data;
    });

// Restorent PDF view
export const getPDFReceipt = (record: any): Promise<any> =>
  instance
    .get<any>(HOTEL_SERVICE + `reservation/receipt/openpdf?hotelId=${record?.hotelId}&receiptId=${record?.id}`)
    .then(async response => {
      const pdfFile = new Blob([response.data], {type: 'application/pdf'});
      const pdfURL = URL.createObjectURL(pdfFile);

      const file = new File([response.data], 'file_name.pdf');

      const reader = new FileReader();

      reader.onload = () => {
        const fileData = reader.result;
        // setInvoicePdf(fileData);
      };

      reader.readAsDataURL(file);
      return pdfURL;
    });

export const viewReceptFiles = (record: RReceiptData): Promise<any> =>
  instance.get<any>(HOTEL_SERVICE + `payment/receipts/download/${record.id}`).then(({data}) => {
    return data;
  });

export interface FilterSearchReProps {
  proformaInvoiceNumber?: string;
  receiptNumber?: string;
}

export interface FilterSearchInvResProps {
  invoiceNo?: string;
  reservationNo?: string;
}
