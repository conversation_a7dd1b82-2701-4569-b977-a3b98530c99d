import {Tables} from '@app/components/tables/Tables/Tables';
import {Modal} from 'antd';
import {ColumnsType} from 'antd/lib/table';
import {useState} from 'react';
import {useTranslation} from 'react-i18next';
import './Payment.css';
import CreditCard from '@app/assets/restaurant/credit.png';
import PayPal from '@app/assets/restaurant/paypal.png';
import MasterCard from '@app/assets/restaurant/master-card.png';

export interface IRestaurantPayment {
  id: string;
  invoiceNo: string;
  invoiceDate: string;
  amount: number;
  status: boolean;
}

const ManagePayment = () => {
  const {t} = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedPayment, setSelectedPayment] = useState<IRestaurantPayment | null>(null);

  const showModal = (record: IRestaurantPayment) => {
    setSelectedPayment(record);
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
    setSelectedPayment(null);
    console.log('Proceeding to checkout for payment:', selectedPayment);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectedPayment(null);
  };

  const columns: ColumnsType<IRestaurantPayment> = [
    {
      title: 'Invoice No',
      dataIndex: 'invoiceNo',
      align: 'center',
    },
    {
      title: 'Invoice Date',
      dataIndex: 'invoiceDate',
      align: 'center',
    },
    {
      title: 'Payment Amount',
      dataIndex: 'amount',
      align: 'center',
      render: (amount: number) => `$${amount.toFixed(2)}`,
    },
    {
      title: 'Payment Status',
      dataIndex: 'status',
      align: 'center',
      render: (status: boolean) =>
        status ? (
          <span
            style={{
              backgroundColor: '#dcfce7',
              color: '#16a34a',
              padding: '2px 12px',
              borderRadius: '16px',
              fontSize: '14px',
            }}>
            Paid
          </span>
        ) : (
          <span
            style={{
              backgroundColor: '#fee2e2',
              color: '#dc2626',
              padding: '2px 5px',
              borderRadius: '16px',
              fontSize: '14px',
            }}>
            Pending
          </span>
        ),
    },
    {
      title: 'Action',
      dataIndex: 'actions',
      align: 'center',
      render: (_, record) =>
        record.status ? (
          <button
            disabled
            style={{
              backgroundColor: '#fee2e2',
              color: 'white',
              padding: '5px 12px',
              borderRadius: '16px',
              cursor: 'not-allowed',
              border: 'none',
            }}>
            Paid
          </button>
        ) : (
          <button
            onClick={() => showModal(record)}
            style={{
              backgroundColor: '#2CA062',
              color: 'white',
              padding: '5px 12px',
              borderRadius: '16px',
              cursor: 'pointer',
              border: 'none',
            }}>
            Pay Now
          </button>
        ),
    },
  ];

  const tableData: IRestaurantPayment[] = [
    {id: '1', invoiceNo: 'IN-123456', invoiceDate: '23-10-2023', amount: 267.89, status: false},
    {id: '2', invoiceNo: 'IN-123457', invoiceDate: '24-10-2023', amount: 29.99, status: true},
    {id: '3', invoiceNo: 'IN-123458', invoiceDate: '25-10-2023', amount: 49.99, status: true},
    {id: '4', invoiceNo: 'IN-123459', invoiceDate: '26-10-2023', amount: 99.99, status: true},
    {id: '5', invoiceNo: 'IN-123460', invoiceDate: '27-10-2023', amount: 75.0, status: true},
  ];

  return (
    <div>
      <Tables
        title={'Payment'}
        columns={columns}
        tableData={tableData}
        isCreate={false}
        // showPagination={false}
      />
      <Modal
        title={
          <div className="header">
            <div className="contestTitle">Payment Details</div>
            <div className="totalPriceLabel">TOTAL PRICE</div>
            <div className="priceAmount">${selectedPayment ? selectedPayment.amount.toFixed(2) : '0.00'}</div>
            <div className="standardPackage">{selectedPayment?.invoiceNo}</div>
          </div>
        }
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={[
          <button key="submit" className="payButton" onClick={handleOk}>
            Proceed To Checkout
          </button>,
        ]}
        // bodyStyle={{padding: '24px 16px'}}
        width={360}>
        <div style={{display: 'flex', justifyContent: 'space-between', marginBottom: '16px'}}>
          <img src={CreditCard} alt="Credit Card" height={50} width={120} style={{objectFit: 'contain'}} />
          <img src={PayPal} alt="PayPal" height={50} width={120} style={{objectFit: 'contain'}} />
          <img src={MasterCard} alt="MasterCard" height={50} width={120} style={{objectFit: 'contain'}} />
        </div>
        <div className="securePayment">
          <span className="lockIcon">🔒</span> Secure payment options
        </div>
      </Modal>
    </div>
  );
};

export default ManagePayment;
