/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/rules-of-hooks */
import {InfoModal, Modal} from '@app/components/common/Modal/Modal';
import React, {useEffect, useRef, useState} from 'react';
import {
  IAvailableRooms,
  IChangeRoomPayload,
  IChangeStayTypePayload,
  ICheckInModal,
  ICheckOutPayload,
} from '../../interface/interface';
import * as S from './Modals.style';
import {
  Badge,
  Col,
  Descriptions,
  Empty,
  FormInstance,
  Row,
  Space,
  Tag,
  TimePicker,
  UploadFile,
  UploadProps,
} from 'antd';
import {MODAL_DATA} from '../data';
import dayjs from 'dayjs';
import {StayTypeTitle} from '@app/components/common/StayTypeTitle/StayTypeTitle';
import {BASE_COLORS, FONT_SIZE} from '@app/styles/themes/constants';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {Input} from '@app/components/common/inputs/Input/Input';
import {Select} from '@app/components/forms/StepForm/StepForm.styles';
import {Option} from '@app/components/common/selects/Select/Select';
import {
  IReservationUpdateGuest,
  searchedExactVatUsersApi,
  searchedVatUsersApi,
  updateGuest,
  updateIDImage,
  updateVatUserApi,
} from '@app/api/hotel/guest/guest.api';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {
  checkVATAvailability,
  getCountries,
  isPositiveNumber,
  validateEmail,
  validateEmailWithoutRequired,
} from '@app/utils/functions';
import {Button} from '@app/components/common/buttons/Button/Button';
import {Checkbox} from '@app/components/common/Checkbox/Checkbox';
import styled from 'styled-components';
import {
  changeStayTypes,
  checkOutReservedRoom,
  getAvailableRoomsById,
  updateRoom,
  updateReservationRemarks,
  uploadbankSlip,
} from '@app/api/hotel/reservation/reservation.api';
import {notificationController} from '@app/controllers/notificationController';
import {getReservedRoomModalData} from '@app/store/slices/reservationSlice';
import {Popconfirm} from '@app/components/common/Popconfirm/Popconfirm';
// import moment from 'moment';
import MainGuestPassport from '@app/components/common/PassportUpload/MainGuestPassport';
import OtherGuestPassport from '@app/components/common/PassportUpload/OtherGuestPassport';
import {IInvoiceData, MakeReservationPayment} from './MakeReservationPayment';
import {CreditCardFilled, EyeOutlined, FlagFilled, InfoCircleOutlined, MailOutlined} from '@ant-design/icons';
import {commonNames, convertNumberFormat, formatNumberToDecimal} from '@app/utils/utils';
import Tooltip from '@app/components/common/Tooltip/Tooltip';
import {Collapse} from 'react-collapse';
import {searchMealTypesTypes} from '@app/api/resturant/tablecategory/stayType.api';
import {find, isEmpty, set} from 'lodash';
import {IoPersonSharp} from 'react-icons/io5';
import {useMediaQuery} from 'react-responsive';
import {UpdateLeadType} from './UpdateLeadType';
import {getAllLeadTypes} from '@app/api/hotel/leadType/leadType.api';
import ApplyResDiscount from './ApplyResDiscount';
import {HOTEL_SERVICE_MODULE_NAME, modulePermission} from '@app/utils/permissions';
import {AutoComplete} from '@app/components/common/AutoComplete/AutoComplete';
import ExtendRoomDays from './ExtendRoomDays';
import {Spinner} from '@app/components/common/Spinner/Spinner';
import {UpdateBookingType} from './UpdateBookingType';
import Paragraph from 'antd/lib/typography/Paragraph';
import {useTranslation} from 'react-i18next';
import RefundModal from './refundModal/RefundModal';
import EmailCustomizer from '@app/components/common/EmailCustomizer/EmailCustomizer';
import {EMAIL_TEMPLATE_MESSAGES, EMAIL_TYPES} from '@app/shared/constants';
import {Popover} from '@app/components/common/Popover/Popover';
import {Edit} from 'lucide-react';
import CreditPayment from '@app/components/common/CreditNote/CreditNote';

const Picker = styled(TimePicker)`
  width: 100%;
`;

const CheckOut: React.FC<ICheckInModal> = ({open, close, reloadTable, setopenCheckOut}) => {
  const [guestImages, setguestImages]: any = useState([]);
  const [countries, setcountries] = useState([]);
  const [checkboxStates, setCheckboxStates]: any = useState({});
  const [checkOutTimeStates, setcheckOutTimeStates]: any = useState({});
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [isChangeMainGuestDetails, setisChangeMainGuestDetails]: any = useState({});
  const [isChangeOtherGuestDetails, setisChangeOtherGuestDetails]: any = useState({});
  const [bankSlip, setbankSlip]: any = useState(null);
  const [loadBankSlip, setloadBankSlip] = useState(false);
  const [isMealTypeExpanded, setisMealTypeExpanded]: any = useState(false);
  const [mealTypes, setMealTypes] = useState([]);
  const [selectedStayTypeId, setselectedStayTypeId] = useState<number | undefined>(undefined);
  const [selectedStayType, setselectedStayType]: any = useState();
  const [loadUpdateStayType, setloadUpdateStayType] = useState(false);
  const [leadTypes, setLeadTypes] = useState([]);
  const [payableAmount, setpayableAmount] = useState(0);
  const {modalData} = useAppSelector(state => state.reservationSlice);
  const [reservationId, setreservationId]: any = useState(null);
  const {hotelId, groupId} = useAppSelector(state => state.hotelSlice.hotelConfig);

  //make payment
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isVisibleLeadTypeModal, setIsVisibleLeadTypeModal] = useState(false);
  const [paymentData, setPaymentData] = useState<IInvoiceData>({
    currencyId: 0,
    currencyPrefix: 'LKR',
    dueAmount: 0,
    id: 0,
    reservationId: 0,
  });

  const [searchedVatUsers, setsearchedVatUsers] = useState([]);
  const [isExistingVatUser, setisExistingVatUser] = useState(false);
  const [vatUserFormData, setvatUserFormData]: any = useState({
    vatPersonName: null,
    vatNumber: null,
    vatPersonEmail: null,
    vatPersonAddress: null,
  });
  const [isVatDetailsAvailable, setisVatDetailsAvailable] = useState(false);
  const [isVatActive, setisVatActive] = useState(false);
  const [paymentType, setPaymentType] = useState<'CASH' | 'CREDITCARD' | 'ONLINE_PAYMENT'>('CASH');
  const [isExpanded, setisExpanded]: any = useState(false);
  const [loadRooms, setloadRooms] = useState(false);
  const [availableRoom, setavailableRoom] = useState([]);
  const [selectedRoomId, setselectedRoomId] = useState(0);
  const [loadUpdateRoom, setloadUpdateRoom] = useState(false);
  const {t} = useTranslation();
  const [form] = BaseForm.useForm();
  const [changeLeadTypeForm] = BaseForm.useForm();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [editableStr, setEditableStr] = useState('');

  const [visiblePendingMailModal, setvisiblePendingMailModal] = useState(false);
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [templateMessage, settemplateMessage] = useState('');
  const [selectedPendingMailId, setSelectedPendingMailId] = useState<number | null>(null);
  const [popoverVisible, setPopoverVisible] = useState(false);

  const userPermission = useAppSelector(state => state.user.permissions);
  const permissions = modulePermission(userPermission, HOTEL_SERVICE_MODULE_NAME.DISCOUNT);
  const [isFullyPaid, setIsFullyPaid] = useState<boolean>(true);
  const [originalText, setOriginalText] = useState(editableStr);
  const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef<any>(null);

  //get permission
  const permissionGuestMail = modulePermission(userPermission, HOTEL_SERVICE_MODULE_NAME.EDIT_GUEST_MAIL);

  // Media Queries
  const isTabletOrMobile = useMediaQuery({query: '(max-width: 1224px)'});

  const [changeBookingTypeForm] = BaseForm.useForm();
  const [isVisibleBookigTypeModal, setIsVisibleBookingTypeModal] = useState(false);

  const dispatch = useAppDispatch();

  const handleClickMailIcon = (pendingEmailId: number) => {
    setSelectedPendingMailId(pendingEmailId);
    setvisiblePendingMailModal(true);
  };

  const handleSend = (pendingEmailId: number) => {
    setSelectedPendingMailId(pendingEmailId);
    setvisiblePendingMailModal(true);
    setPopoverVisible(false);
  };

  const pendingPaymentMailContent = (
    <div>
      {modalData.reservationListResponse?.pendingEmailResponse
        ?.filter(email => email.pendingEmailType === 'PAYMENT')
        ?.map(email => (
          <div
            key={email.pendingEmailId}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              borderBottom: '1px solid #ccc',
              padding: '10px 0',
              gap: '20px',
            }}>
            {/* Displaying date and time */}
            <div>
              <p style={{margin: 0, fontSize: '14px', color: '#333'}}>
                {new Date(email.updatedAt).toLocaleDateString()}
              </p>
              <p style={{margin: 0, fontSize: '12px', color: '#666'}}>
                {new Date(email.updatedAt).toLocaleTimeString()}
              </p>
            </div>
            {/* "Send" button */}
            <Button
              type="primary"
              onClick={() => handleSend(email.pendingEmailId)}
              style={{
                color: '#fff',
                border: 'none',
                padding: '5px 8px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
              }}>
              Re Send
            </Button>
          </div>
        ))}
    </div>
  );

  const handleChangeMainGuest = (roomIndex: number) => {
    setisChangeMainGuestDetails((preData: any) => ({
      ...preData,
      [roomIndex]: true,
    }));
  };

  const handleRoomSelect = (roomId: number) => {
    setselectedRoomId(roomId);
  };

  const handleChangeOtherGuest = (roomIndex: number, guestIndex: number) => {
    setisChangeOtherGuestDetails((preData: any) => ({
      ...preData,
      [`${roomIndex}-${guestIndex}`]: true,
    }));
  };

  const getAvailableRooms = async (
    stayTypeId: number | undefined,
    checkInDate: string,
    checkOutDate: string,
    roomId: any,
    roomHotelType: string,
  ) => {
    setloadRooms(true);
    try {
      const response = await getAvailableRoomsById(stayTypeId, checkInDate, checkOutDate, roomId);
      const roomData = response.result.reservedRoom.availableRoomResponseList;
      setavailableRoom(roomData);
      if (roomData && roomData?.length === 1 && !isEmpty(roomData)) {
        setselectedRoomId(roomData[0]?.roomId);
      }
      setloadRooms(false);
    } catch (error) {
      setloadRooms(false);
    }
  };

  const handleExpand = (
    stayTypeId: number,
    checkInDate: string,
    checkOutDate: string,
    roomIndex: number,
    roomHotelType: string,
  ) => {
    setisExpanded((prevState: any) => {
      const newState = {...prevState};
      const isOpen = newState[roomIndex];

      for (const index in newState) {
        newState[index] = false;
      }
      if (!isOpen) {
        newState[roomIndex] = true;
      }

      return newState;
    });

    getAvailableRooms(stayTypeId, checkInDate, checkOutDate, '', roomHotelType);
  };

  const handleCloseIcon = () => {
    setisExpanded(true);
  };

  const changeRoom = async (reservedRoomId: number) => {
    setloadUpdateRoom(true);
    const status = ['CHECKEDIN', 'CHECKEDOUT', 'BOOKED'];
    const payload: IChangeRoomPayload = {
      id: reservedRoomId,
      roomId: selectedRoomId,
    };
    try {
      const response = await updateRoom(payload);
      if (response.statusCode === '20000') {
        notificationController.success({message: 'Room updated successfully'});
        setloadUpdateRoom(false);
        setavailableRoom([]);
        await dispatch(
          getReservedRoomModalData({reservationId: modalData.reservationListResponse.reservationId, status: status}),
        );
      } else {
        notificationController.error({message: response.message});
      }
      setisExpanded(false);
    } catch (error) {
      setloadUpdateRoom(false);
    }
  };

  const handleCheckboxChange = async (roomIndex: number) => {
    setCheckboxStates((prevState: any) => ({
      ...prevState,
      [roomIndex]: !prevState[roomIndex],
    }));

    const roomForm = roomForms[roomIndex];
    const checkInTime = roomForm.getFieldValue(`checkInTime-${roomIndex}`);

    if (checkInTime === undefined) {
      await roomForm.validateFields();
    }
  };

  const handleCheckoutPickerChange = (value: any, roomIndex: number) => {
    const date = value === undefined ? false : true;

    setcheckOutTimeStates({
      ...checkOutTimeStates,
      [roomIndex]: date,
    });
  };

  const roomForms: Array<FormInstance<any>> =
    // eslint-disable-next-line react-hooks/rules-of-hooks
    Array.from({length: 16}, (_, index) => index).map(() => BaseForm.useForm()[0]) || [];
  const [vatForm] = BaseForm.useForm();

  const handleRoomSubmit = async (roomIndex: number) => {
    // if (
    //   modalData?.reservationListResponse.leadType === null &&
    //   modalData.reservedRoomDetailsResponseList.filter(k => k.reservedRoomStatus === 'CHECKEDIN').length === 1
    // ) {
    //   return notificationController.warning({message: 'Please select lead type before checkout'});
    // } else {
    const status = ['CHECKEDIN', 'CHECKEDOUT'];
    try {
      const roomForm = roomForms[roomIndex];
      // await roomForm.validateFields();
      const values = roomForm.getFieldsValue();
      const payload: ICheckOutPayload[] = [
        {
          time: dayjs(values[`checkInTime-${roomIndex}`]).format('hh:mm:ss'),
          keyRetrieval: values[`keyHandOver-${roomIndex}`],
          reservationId: modalData?.reservationListResponse.reservationId,
          reservedRoomId: modalData?.reservedRoomDetailsResponseList.filter(k => k.reservedRoomStatus === 'CHECKEDIN')[
            roomIndex
          ].reservedRoomId,
          paymentDone: true,
        },
      ];
      const response = await checkOutReservedRoom(payload);
      if (response.statusCode === '20000') {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        notificationController.success({message: response.message});
        await dispatch(
          getReservedRoomModalData({reservationId: modalData.reservationListResponse.reservationId, status: status}),
        );
        setCheckboxStates({});
        roomForms.filter((form, index) => {
          form.resetFields([`keyHandOver-${index}`, `checkInTime-${index}`]);
        });
        reloadTable();
        setopenCheckOut(false);
      } else {
        notificationController.error({message: response.message});
      }
    } catch (error) {
      console.error(`Room ${roomIndex} form validation failed.`, error);
    }
    // }
  };

  const handleSubmit = (values: any) => {
    //
  };

  const handleAllLeadTypes = async () => {
    try {
      const response = await getAllLeadTypes(hotelId);
      setLeadTypes(response.result.leadType);
    } catch (error) {}
  };

  useEffect(() => {
    (async () => {
      const countries = await getCountries('');
      setcountries(countries);
    })();
  }, []);

  useEffect(() => {
    populateData();

    setbankSlip(modalData.reservationListResponse.bankSlipImage);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalData]);

  useEffect(() => {
    if (open) {
      handleAllLeadTypes();
    }
  }, [open]);

  useEffect(() => {
    setpayableAmount(paymentData.dueAmount);
  }, [paymentData]);

  const handlePressCheckOut = async () => {
    const status = ['CHECKEDIN', 'CHECKEDOUT'];
    await dispatch(
      getReservedRoomModalData({reservationId: modalData.reservationListResponse.reservationId, status: status}),
    );
  };

  const populateData = () => {
    if (modalData) {
      setEditableStr(modalData.reservationListResponse.internalRemarks);
      if (modalData.reservationListResponse.vatNumber !== null) {
        setisVatDetailsAvailable(true);
      } else {
        setisVatDetailsAvailable(false);
      }
      const isVatActive = checkVATAvailability(modalData.reservationListResponse.taxResponseList);
      setisVatActive(isVatActive);
      setPaymentData({
        currencyId: modalData?.reservationListResponse?.currencyId,
        currencyPrefix: modalData?.reservationListResponse?.reservationCurrencyPrefix,
        dueAmount: modalData?.reservationListResponse?.outStanding,
        id: modalData?.reservationListResponse?.invoiceId,
        reservationId: modalData?.reservationListResponse?.reservationId,
      });

      const vatDetails = {
        vatPersonEmail: modalData.reservationListResponse.vatRegistryEmail,
        vatNumber: modalData.reservationListResponse.vatNumber,
        vatPersonName: modalData.reservationListResponse.vatRegistryName,
        vatPersonAddress: modalData.reservationListResponse.vatRegistryAddress,
      };

      vatForm.setFieldsValue(vatDetails);
      setvatUserFormData(vatDetails);
      if (modalData.reservationListResponse.vatNumber !== null) setisExistingVatUser(true);

      modalData.reservedRoomDetailsResponseList &&
        modalData?.reservedRoomDetailsResponseList
          .filter(k => k.reservedRoomStatus === 'CHECKEDIN')
          .map((room, roomIndex) => {
            if (roomForms) {
              roomForms.map(form => {
                form.setFieldsValue({
                  [`guestNic-${roomIndex}`]: room.mainGuest.idNumber,
                  [`guestName-${roomIndex}`]: room.mainGuest.firstName,
                  [`guestLastName-${roomIndex}`]: room.mainGuest.lastName,
                  [`email-${roomIndex}`]: room.mainGuest.email,
                  [`countryId-${roomIndex}`]: room.mainGuest.countryId,
                  [`city-${roomIndex}`]: room.mainGuest.city,
                  [`address-${roomIndex}`]: room.mainGuest.address,
                  [`id-${roomIndex}`]: room.mainGuest.id,
                  [`guestType-${roomIndex}`]: room.mainGuest.guestType,
                });
                if (room.otherGuestList) {
                  room.otherGuestList.forEach((guest, idx) => {
                    form.setFieldsValue({
                      [`idNumber-${roomIndex}-${idx}`]: guest.idNumber,
                      [`firstName-${roomIndex}-${idx}`]: guest.firstName,
                      [`lastName-${roomIndex}-${idx}`]: guest.lastName,
                      [`email-${roomIndex}-${idx}`]: guest.email,
                      [`countryId-${roomIndex}-${idx}`]: guest.countryId,
                      [`city-${roomIndex}-${idx}`]: guest.city,
                      [`address-${roomIndex}-${idx}`]: guest.address,
                      [`id-${roomIndex}-${idx}`]: guest.id,
                      [`guestType-${roomIndex}-${idx}`]: guest.guestType,
                    });
                  });
                }
              });
            }
          });
    }
  };

  const resetForm = () => {
    setCheckboxStates({});
    setisChangeMainGuestDetails({});
    setisChangeOtherGuestDetails({});
    roomForms.filter((form, index) => {
      form.resetFields([`keyHandOver-${index}`, `checkInTime-${index}`]);
    });
    vatForm.resetFields();
    setisVatDetailsAvailable(false);
    setisVatActive(false);
  };

  const handleCancelModal = () => {
    reloadTable();
    resetForm();
    close();
    setFileList([]);
    setguestImages([]);
    setbankSlip(null);
  };

  const guestUpdate = async (roomIndex: number, image: any, guestIndex: number | undefined) => {
    try {
      const roomForm = roomForms[roomIndex];
      if (guestIndex === undefined) {
        const keyIndex = `${roomIndex}`;
        await roomForm.validateFields([`passportImage-${keyIndex}`, `guestNic-${keyIndex}`]);
        const values = roomForm.getFieldsValue();
        const details = {
          id: values[`id-${keyIndex}`],
          firstName: values[`guestName-${keyIndex}`],
          lastName: values[`guestLastName-${keyIndex}`],
          idNumber: values[`guestNic-${keyIndex}`],
          nicNumber: true,
          email: values[`email-${keyIndex}`],
          countryId: values[`countryId-${keyIndex}`],
          address: values[`address-${keyIndex}`],
          guestType: values[`guestType-${keyIndex}`],
        };
        const payload: IReservationUpdateGuest = {
          profile: details,
          passportImage: image,
        };
        const response = await updateGuest(payload);
        if (response.statusCode === '20000') {
          notificationController.success({message: response.message});
          setisChangeMainGuestDetails((preData: any) => ({
            ...preData,
            [roomIndex]: false,
          }));
        } else {
          notificationController.error({message: response.message});
        }
      } else {
        const keyIndex = `${roomIndex}-${guestIndex}`;
        await roomForm.validateFields([`passportImage-${keyIndex}`, `idNumber-${keyIndex}`]);
        const values = roomForm.getFieldsValue();
        const details = {
          id: values[`id-${keyIndex}`],
          firstName: values[`firstName-${keyIndex}`],
          lastName: values[`lastName-${keyIndex}`],
          idNumber: values[`idNumber-${keyIndex}`],
          nicNumber: true,
          email: values[`email-${keyIndex}`],
          countryId: values[`countryId-${keyIndex}`],
          address: values[`address-${keyIndex}`],
          guestType: values[`guestType-${keyIndex}`],
        };
        const payload: IReservationUpdateGuest = {
          profile: details,
          passportImage: image,
        };
        const response = await updateGuest(payload);
        if (response.statusCode === '20000') {
          notificationController.success({message: response.message});
          setisChangeOtherGuestDetails((preData: any) => ({
            ...preData,
            [`${roomIndex}-${guestIndex}`]: false,
          }));
        } else {
          notificationController.error({message: response.message});
        }
      }
    } catch (error) {
      console.error(`Room ${roomIndex} form validation failed.`, error);
    }
  };

  const onMainGuestImageChange = (event: {target: {files: any[]}}, roomIndex: number) => {
    const file = event.target.files[0];
    if (file) {
      setguestImages((prevUploadImages: any) => ({
        ...prevUploadImages,
        [roomIndex]: file,
      }));
      const keyIndex = `${roomIndex}`;
      const form = roomForms[roomIndex];
      form.setFieldValue(`passportImage-${keyIndex}`, file);
      setisChangeMainGuestDetails((preData: any) => ({
        ...preData,
        [roomIndex]: true,
      }));
    }
  };

  const handleUploadOtherGuestImage = (event: {target: {files: any[]}}, roomIndex: number, guestIndex: number) => {
    const file = event.target.files[0];
    if (file) {
      setguestImages((prevUploadImages: any) => ({
        ...prevUploadImages,
        [`${roomIndex}${guestIndex}`]: file,
      }));
      const keyIndex = `${roomIndex}-${guestIndex}`;
      const form = roomForms[roomIndex];
      form.setFieldValue(`passportImage-${keyIndex}`, file);
    }
  };

  const onModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setIsFullyPaid(true);
  };

  const handleBankSlipButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current?.click();
    }
  };

  const handleFileUpload = async (event: {target: {files: any[]}}) => {
    try {
      const selectedFile = event.target.files[0];
      if (!selectedFile) {
        return;
      }
      setloadBankSlip(true);
      const formData = new FormData();
      formData.append('bankSlipImage', selectedFile);
      const response = await uploadbankSlip(modalData.reservationListResponse.reservationId, formData);
      if (response.statusCode === '20000') {
        notificationController.success({message: 'Proof of Payment updated successfully'});

        setbankSlip(URL.createObjectURL(selectedFile));
        setloadBankSlip(false);
      } else {
        notificationController.error({message: response.message});
        setloadBankSlip(false);
      }
    } catch (error) {
      setloadBankSlip(false);
    }
  };

  const handleExpandMealType = (
    reservedRoomId: number,
    checkInDate: string,
    checkOutDate: string,
    roomIndex: number,
  ) => {
    setisMealTypeExpanded((prevState: any) => {
      const newState = {...prevState};
      const isOpen = newState[roomIndex];

      for (const index in newState) {
        newState[index] = false;
      }
      if (!isOpen) {
        newState[roomIndex] = true;
      }
      return newState;
    });

    listMealTypes(reservedRoomId);
  };

  const listMealTypes = async (reservedRoomId: number) => {
    try {
      const result = await searchMealTypesTypes(reservedRoomId);
      const dataList: any = [];
      const dataIds: number[] = [];
      result?.result?.reservation?.map(
        (type: {
          stayTypeName: string;
          stayTypeId: number;
          numberOfAdult: number;
          numberOfChildren: number;
          roomTypeName: string;
          meal: string;
          lkrPrice: string;
          reservedRoomDayPriceResponseList: any;
          numberOfAdults: number;
          id: number;
          usdPrice: string;
        }) => {
          dataIds.push(type.stayTypeId);
          dataList.push({
            stayTypeName: type.stayTypeName,
            stayTypeId: type.id,
            numberOfAdult: type.numberOfAdults,
            numberOfChildren: type.numberOfChildren,
            roomTypeName: type.roomTypeName,
            meal: type.meal,
            lkrPrice: type.lkrPrice,
            usdPrice: type.usdPrice,
            reservedRoomDayPriceResponseList: type.reservedRoomDayPriceResponseList,
          });
        },
      );
      setMealTypes(dataList);
    } catch (error) {}
  };

  const handleCloseMealType = () => {
    setisMealTypeExpanded(true);
  };

  const handleClickMealType = (
    stayType: {stayTypeId: number},
    checkInDate: string,
    checkOutDate: string,
    roomId: number,
  ) => {
    setselectedStayTypeId(stayType.stayTypeId);
    setselectedStayType(stayType);
  };

  const renderTitle = (
    data: {
      name: string;
      numberOfAdult: number;
      numberOfChildren: number;
      meal: string;
      lkrPrice: string;
      usdPrice: string;
    },
    currencySuffix: string,
    selected: boolean,
  ) => {
    const {meal, lkrPrice, numberOfAdult, numberOfChildren, usdPrice} = data;

    return (
      <S.StayTitle $isSelected={selected}>
        {}
        <S.TitleIconWrapper>
          {numberOfAdult > 3 ? (
            <>
              {numberOfAdult} <IoPersonSharp size={12} />
            </>
          ) : (
            Array.from({length: numberOfAdult}, (_, index) => index + 1).map(number => {
              return <IoPersonSharp size={12} key={number} />;
            })
          )}

          <div hidden={numberOfChildren === 0 ? true : false}>
            {' + '}{' '}
            {numberOfChildren > 3 ? (
              <>
                {numberOfChildren} <IoPersonSharp size={8} />
              </>
            ) : (
              Array.from({length: numberOfChildren}, (_, index) => index + 1).map(number => {
                return <IoPersonSharp size={8} key={number} />;
              })
            )}
          </div>
          {' - '}
        </S.TitleIconWrapper>
        {meal === 'ROOM_ONLY' ? 'RO' : meal} {' - '}{' '}
        {convertNumberFormat(Number(currencySuffix === 'LKR' ? lkrPrice : usdPrice))} {currencySuffix}
      </S.StayTitle>
    );
  };

  const changeStayType = async (id: number, roomId: number, stayTypeId: number, currency: string, type: string) => {
    setloadUpdateStayType(true);
    try {
      const status = ['CHECKEDIN', 'CHECKEDOUT', 'BOOKED'];
      const payload: IChangeStayTypePayload = {
        id: id,
        roomId: roomId,
        stayTypeId: stayTypeId,
        reservedRoomDayPriceResponseList: selectedStayType?.reservedRoomDayPriceResponseList,
        totalAmount: currency === 'LKR' ? selectedStayType?.lkrPrice : selectedStayType?.usdPrice,
        type: type,
      };
      const response = await changeStayTypes(payload);
      if (response.statusCode === '20000') {
        notificationController.success({message: 'Stay type updated successfully'});
        setSelectedPendingMailId(response.result.pendingEmailId);
        setvisiblePendingMailModal(true);
        setisMealTypeExpanded(false);
        setloadUpdateStayType(false);
        await dispatch(
          getReservedRoomModalData({reservationId: modalData.reservationListResponse.reservationId, status: status}),
        );
        setselectedStayTypeId(undefined);
      } else {
        notificationController.error({message: response.message});
        setloadUpdateStayType(false);
      }
    } catch (error) {
      setloadUpdateStayType(false);
    }
  };

  const onLeadTypeModalClose = () => {
    setIsVisibleLeadTypeModal(false);
    form.resetFields();
  };

  const reloadModalData = async () => {
    const status = ['CHECKEDIN', 'CHECKEDOUT', 'BOOKED'];
    await dispatch(
      getReservedRoomModalData({reservationId: modalData.reservationListResponse.reservationId, status: status}),
    );
  };
  const isDisabledFormItem = (userHotelId: number, onlyThisHotelView: boolean, currentValue: any) => {
    if (userHotelId === hotelId && permissionGuestMail.VIEW) {
      return false;
    } else if (onlyThisHotelView && permissionGuestMail.VIEW) {
      return false;
    } else if (currentValue === '' || currentValue === null) {
      return false;
    } else {
      return true;
    }
  };

  const handleSearchVatUsers = async (value: string, searchField: string) => {
    let searchParams = {};
    if (searchField === 'vatPersonEmail') {
      searchParams = {email: value};
    } else if (searchField === 'vatNumber') {
      searchParams = {vatNumber: value};
    }

    const result = await searchedVatUsersApi(groupId, searchParams, 20, 0);

    const data = result?.result?.Guests?.map(
      (item: {id: number; vatNumber: number; address: string; name: string; email: string}) => ({
        id: item.id,
        vatNumber: item.vatNumber,
        address: item.address,
        name: item.name,
        email: item.email,
        value: item.id,
        label: searchField === 'vatPersonEmail' ? item.email : item.vatNumber,
      }),
    );

    setsearchedVatUsers(data);
  };

  const handleUpdateVatRegistry = async () => {
    await vatForm.validateFields();
    const formData = vatForm.getFieldsValue();

    const payload = {
      vatRegistryApplicable: true,
      reservationId: modalData.reservationListResponse.reservationId,
      email: formData?.vatPersonEmail,
      registryType: ['RESERVATION'],
      hotelId: hotelId,
      vatNumber: formData?.vatNumber,
      onlyThisHotelView: false,
      name: formData?.vatPersonName,
      groupId: groupId,
      address: formData?.vatPersonAddress,
    };

    try {
      const response = await updateVatUserApi(payload);
      if (response.statusCode === '20000') {
        notificationController.success({message: response.message});
      } else {
        notificationController.error({message: response.message});
      }
    } catch (error) {}
  };

  const handlePopulateVatInformation = (vatDetails: any) => {
    const {name, address, vatNumber, email} = vatDetails;
    const fieldData = {
      vatPersonName: name,
      vatNumber: vatNumber,
      vatPersonEmail: email,
      vatPersonAddress: address,
    };
    vatForm.setFieldsValue(fieldData);
    setvatUserFormData(fieldData);
    setisExistingVatUser(true);
  };

  const handleChangeVatInput = (changedFieldName: string) => {
    const fields = ['vatPersonName', 'vatNumber', 'vatPersonEmail', 'vatPersonAddress'];
    const fieldsForReset = fields.filter(o => o !== changedFieldName);

    if (isExistingVatUser && vatUserFormData[changedFieldName] !== null) {
      vatForm.resetFields(fieldsForReset);
      setvatUserFormData({
        vatPersonName: null,
        vatNumber: null,
        vatPersonEmail: null,
        vatPersonAddress: null,
      });
      setisExistingVatUser(false);
    }
  };

  const resetVatSearchedData = () => {
    setsearchedVatUsers([]);
  };

  const handleBlurPopulate = async (value: string, searchField: string) => {
    let searchParams = {};
    if (searchField === 'vatPersonEmail') {
      searchParams = {email: value};
    } else if (searchField === 'vatNumber') {
      searchParams = {vatNumber: value};
    }
    const result = await searchedExactVatUsersApi(groupId, searchParams, 20, 0);
    const data = result?.result?.Guests?.map(
      (item: {id: number; vatNumber: number; address: string; name: string; email: string}) => ({
        id: item.id,
        vatNumber: item.vatNumber,
        address: item.address,
        name: item.name,
        email: item.email,
        value: item.id,
        label: searchField === 'vatPersonEmail' ? item.email : item.vatNumber,
      }),
    );
    if (!isEmpty(data)) handlePopulateVatInformation(data[0]);
  };

  const onBookingTypeModalClose = () => {
    setIsVisibleBookingTypeModal(false);
    changeBookingTypeForm.resetFields();
  };
  const handleUpdateRemarks = async (value: string) => {
    try {
      const payload = {
        reservationId: modalData.reservationListResponse.reservationId,
        remark: value,
      };
      const response = await updateReservationRemarks(payload);
      if (response.statusCode === '20000') {
        notificationController.success({message: response.message});
        setIsEditing(false);
        reloadModalData();
      } else {
        notificationController.error({message: response.message});
      }
    } catch (error) {}
  };

  const hasPaymentHistory = modalData?.reservationListResponse?.pendingEmailResponse?.find(
    email => email.pendingEmailType === 'PAYMENT',
  );
  const handleTextChange = (e: {target: {value: any}}) => {
    const value = e.target.value;
    setEditableStr(value);
    setIsEditing(value !== originalText);
  };

  const outstanding = modalData?.reservationListResponse?.outStanding;

  return (
    <div>
      <Modal footer={null} title="Room Check-out" size="large" open={open} onCancel={handleCancelModal}>
        {/* {!loadingModal ? ( */}
        <React.Fragment>
          <S.BlurCardWrapper>
            <S.BlueCard>
              <S.Padding>
                <S.CardTitle>Reservation Information</S.CardTitle>
                <Descriptions>
                  <Descriptions.Item span={isTabletOrMobile ? 4 : 0} label="Reservation Number">
                    {modalData?.reservationListResponse?.refNumber}
                  </Descriptions.Item>
                  <Descriptions.Item span={isTabletOrMobile ? 4 : 0} label="Channel Name">
                    {modalData?.reservationListResponse?.channelName}
                  </Descriptions.Item>
                  <Descriptions.Item span={isTabletOrMobile ? 4 : 0} label="Channel Email">
                    {modalData?.reservationListResponse?.channelEmail}
                  </Descriptions.Item>
                  <Descriptions.Item span={isTabletOrMobile ? 4 : 0} label="Payment Status">
                    <Tag color={modalData?.reservationListResponse?.paymentDone ? 'green' : 'red'}>
                      {modalData?.reservationListResponse?.paymentDone ? 'PAID' : 'PENDING'}
                    </Tag>
                  </Descriptions.Item>
                  {modalData?.reservationListResponse?.reservedPaymentStatus && (
                    <Descriptions.Item label="Booking Status">
                      <Tag>{modalData?.reservationListResponse?.reservedPaymentStatus}</Tag>
                    </Descriptions.Item>
                  )}
                  {modalData?.reservationListResponse?.leadType !== null ? (
                    <Descriptions.Item span={isTabletOrMobile ? 4 : 0} label="Lead Type">
                      {modalData?.reservationListResponse?.leadType}
                    </Descriptions.Item>
                  ) : null}
                  {modalData?.reservationListResponse?.bookingType !== null ? (
                    <Descriptions.Item span={isTabletOrMobile ? 4 : 0} label="Booking Type">
                      {modalData?.reservationListResponse?.bookingType === 'WEB_SITE'
                        ? 'WEBSITE'
                        : modalData?.reservationListResponse?.bookingType}
                    </Descriptions.Item>
                  ) : null}
                </Descriptions>

                <S.ReserveInfoWrapper>
                  {bankSlip !== null && (
                    <S.PayButtonWrapper style={{gap: '1rem'}}>
                      <Tooltip title="Show slip">
                        <EyeOutlined
                          onClick={() => {
                            InfoModal({
                              width: 800,
                              icon: null,
                              closable: true,
                              content: (
                                <div>
                                  <img style={{width: 750, height: 320}} src={bankSlip} />
                                </div>
                              ),
                              okText: 'Close',
                            });
                          }}
                          style={{color: BASE_COLORS.primary, cursor: 'pointer'}}
                        />
                      </Tooltip>
                      {/* <Tooltip title="Remove slip">
                        <DeleteOutlined style={{color: BASE_COLORS.red, cursor: 'pointer'}} />
                      </Tooltip> */}
                    </S.PayButtonWrapper>
                  )}
                  <S.PayButtonWrapper>
                    <CreditPayment
                      reloadData={reloadModalData}
                      onSuccess={(pendingEmailId: number, reservationId) => {
                        setSelectedPendingMailId(pendingEmailId);
                        setreservationId(reservationId);
                        setvisiblePendingMailModal(true);
                      }}
                      // @ts-ignore
                      rowData={modalData.reservationListResponse}
                      onClose={() => {
                        // setSelectedPendingMailId(null);
                      }}
                    />
                  </S.PayButtonWrapper>
                  <S.PayButtonWrapper>
                    <Button
                      disabled={loadBankSlip}
                      loading={loadBankSlip}
                      type="primary"
                      onClick={handleBankSlipButtonClick}>
                      {t('reservation.paymentProof')}
                    </Button>
                    {/* Hidden file input */}
                    <input
                      type="file"
                      ref={fileInputRef}
                      style={{display: 'none'}}
                      onChange={(event: any) => handleFileUpload(event)}
                    />
                  </S.PayButtonWrapper>

                  {/* {!modalData?.reservationListResponse?.paymentDone &&
                    ( */}
                  {!modalData?.reservationListResponse?.paymentDone &&
                  !isPositiveNumber(modalData.reservationListResponse.outStanding) ? (
                    <S.PayButtonWrapper>
                      <RefundModal
                        invoiceId={modalData?.reservationListResponse?.invoiceId}
                        paymentData={{
                          currencyId: modalData?.reservationListResponse?.currencyId,
                          currencyPrefix: modalData?.reservationListResponse?.reservationCurrencyPrefix,
                          dueAmount: modalData?.reservationListResponse?.outStanding,
                          id: modalData?.reservationListResponse?.invoiceId,
                          reservationId: modalData?.reservationListResponse?.reservationId,
                        }}
                        onSuccess={(pendingEmailId: number) => {
                          setStatus('success');
                          setSelectedPendingMailId(pendingEmailId);
                          setvisiblePendingMailModal(true);
                          settemplateMessage(EMAIL_TEMPLATE_MESSAGES.PAYMENT.SUCCESS);
                        }}
                        reloadData={async () => {
                          const status = ['CHECKEDIN', 'CHECKEDOUT'];
                          await dispatch(
                            getReservedRoomModalData({
                              reservationId: modalData.reservationListResponse.reservationId,
                              status: status,
                            }),
                          );
                        }}
                      />
                      {/* <Button
                        danger
                        type="primary"
                        icon={<CreditCardFilled />}
                        onClick={() => {
                          setIsModalVisible(true);
                        }}>
                        Refund
                      </Button> */}
                    </S.PayButtonWrapper>
                  ) : (
                    <S.RoomChangeButton isDisabled={modalData?.reservationListResponse?.paymentDone || outstanding < 1}>
                      <S.PayButtonWrapper>
                        <Button
                          disabled={modalData?.reservationListResponse?.paymentDone || outstanding < 1}
                          type="primary"
                          icon={<CreditCardFilled />}
                          onClick={() => {
                            setIsModalVisible(true);
                          }}>
                          Pay Now
                        </Button>
                        {hasPaymentHistory && (
                          <Popover
                            open={popoverVisible}
                            onOpenChange={visible => setPopoverVisible(visible)}
                            content={pendingPaymentMailContent}
                            title="Payments"
                            trigger="click">
                            <Tooltip title="Resend mail">
                              <MailOutlined
                                style={{
                                  marginLeft: '5px',
                                  fontSize: '16px',
                                  cursor: 'pointer',
                                  paddingRight: '5px',
                                }}
                                onClick={() => {
                                  setPopoverVisible(!popoverVisible);
                                }}
                              />
                            </Tooltip>
                          </Popover>
                        )}
                      </S.PayButtonWrapper>
                    </S.RoomChangeButton>
                  )}
                  <S.PayButtonWrapper>
                    <Button
                      type="primary"
                      icon={<CreditCardFilled />}
                      onClick={() => {
                        setIsVisibleLeadTypeModal(true);
                      }}>
                      Lead Type
                    </Button>
                  </S.PayButtonWrapper>
                  <S.PayButtonWrapper>
                    <Button
                      type="primary"
                      icon={<FlagFilled />}
                      onClick={() => {
                        setIsVisibleBookingTypeModal(true);
                      }}>
                      Booking Type
                    </Button>
                  </S.PayButtonWrapper>
                </S.ReserveInfoWrapper>
              </S.Padding>
            </S.BlueCard>
          </S.BlurCardWrapper>
          <S.BlurCardWrapper>
            <S.BlueCard>
              <S.Padding>
                <S.CardTitle>{t('commonNames.remarks')}</S.CardTitle>

                <S.RemarkContainer>
                  {modalData.reservationListResponse.internalRemarks}
                  <S.EditIcon
                    onClick={() => {
                      setIsEditing(true);
                      setTimeout(() => {
                        inputRef.current?.focus();
                      }, 200);
                    }}>
                    <Edit size={16} />
                  </S.EditIcon>
                </S.RemarkContainer>

                {isEditing && (
                  <Input
                    ref={inputRef}
                    value={editableStr}
                    onFocus={() => setOriginalText(editableStr)}
                    onChange={handleTextChange}
                    placeholder={`${t('commonNames.remarks')} `}
                  />
                )}

                <Space style={{marginTop: 5}}>
                  {isEditing && (
                    <Button
                      onClick={() => {
                        setIsEditing(false);
                        setEditableStr(modalData.reservationListResponse.internalRemarks);
                      }}
                      type="ghost">
                      Cancel
                    </Button>
                  )}

                  <Popconfirm
                    placement="topRight"
                    title="Are you sure to update the remarks?"
                    onConfirm={() => {
                      handleUpdateRemarks(editableStr);
                    }}
                    okText="Yes"
                    cancelText="No">
                    <Button
                      type="primary"
                      disabled={!isEditing || editableStr === modalData.reservationListResponse.internalRemarks}>
                      Update
                    </Button>
                  </Popconfirm>
                </Space>
              </S.Padding>
            </S.BlueCard>
          </S.BlurCardWrapper>
          {isVatActive ? (
            <BaseForm key={`vat-details`} form={vatForm} size="middle">
              <S.BlurCardWrapper>
                <S.BlueCard>
                  <S.Padding>
                    <S.CardTitle>VAT Details</S.CardTitle>
                    <Row style={{gap: 10}} justify="start">
                      <BaseForm.Item label="">
                        <Checkbox
                          value={isVatDetailsAvailable}
                          checked={isVatDetailsAvailable}
                          onChange={e => setisVatDetailsAvailable(e.target.checked)}>
                          <S.CheckboxLabel>Add VAT Details</S.CheckboxLabel>
                        </Checkbox>
                      </BaseForm.Item>
                    </Row>
                    {isVatDetailsAvailable ? (
                      <>
                        <Row gutter={{xs: 10, md: 15, xl: 30}}>
                          <Col xs={24} md={8}>
                            <BaseForm.Item
                              name="vatPersonEmail"
                              label={t('commonNames.vatEmail')}
                              rules={[
                                {
                                  required: isVatDetailsAvailable,
                                  validator: isVatDetailsAvailable ? validateEmail : validateEmailWithoutRequired,
                                },
                              ]}>
                              <AutoComplete
                                disabled={!isVatDetailsAvailable}
                                onBlur={(event: any) => {
                                  const isValidEmail = /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/g.test(event.target.value);
                                  if (event.target.value.trim() !== '' && isValidEmail) {
                                    handleBlurPopulate(event.target.value, 'vatPersonEmail');
                                  }
                                  resetVatSearchedData();
                                }}
                                options={searchedVatUsers}
                                onSearch={value => handleSearchVatUsers(value, 'vatPersonEmail')}
                                onSelect={value => {
                                  const selectedGuest = find(
                                    searchedVatUsers,
                                    (o: {value: number}) => o.value === value,
                                  );

                                  if (selectedGuest !== undefined) handlePopulateVatInformation(selectedGuest);
                                }}>
                                <Input
                                  disabled={!isVatDetailsAvailable}
                                  onChange={() => handleChangeVatInput('vatPersonEmail')}
                                />
                              </AutoComplete>
                            </BaseForm.Item>
                          </Col>
                          <Col xs={24} md={8}>
                            <BaseForm.Item
                              name="vatNumber"
                              label="VAT Number"
                              rules={[
                                {
                                  required: isVatDetailsAvailable,
                                  message: 'Required field',
                                },
                              ]}>
                              <AutoComplete
                                disabled={!isVatDetailsAvailable}
                                onBlur={(event: any) => {
                                  if (event.target.value.trim() !== '') {
                                    handleBlurPopulate(event.target.value, 'vatNumber');
                                  }
                                  resetVatSearchedData();
                                }}
                                options={searchedVatUsers}
                                onSearch={value => handleSearchVatUsers(value, 'vatNumber')}
                                onSelect={value => {
                                  const selectedGuest = find(
                                    searchedVatUsers,
                                    (o: {value: number}) => o.value === value,
                                  );

                                  if (selectedGuest !== undefined) handlePopulateVatInformation(selectedGuest);
                                }}>
                                <Input
                                  disabled={!isVatDetailsAvailable}
                                  onChange={() => handleChangeVatInput('vatNumber')}
                                />
                              </AutoComplete>
                            </BaseForm.Item>
                          </Col>
                          <Col xs={24} md={8}>
                            <BaseForm.Item
                              name="vatPersonName"
                              label={t('commonNames.vatPersonName')}
                              rules={[
                                {
                                  required: isVatDetailsAvailable,
                                  message: 'Required field',
                                },
                              ]}>
                              <Input
                                disabled={!isVatDetailsAvailable}
                                onChange={() => handleChangeVatInput('vatPersonName')}
                              />
                            </BaseForm.Item>
                          </Col>
                          <Col xs={24} md={8}>
                            <BaseForm.Item
                              name="vatPersonAddress"
                              label={t('commonNames.vatAddress')}
                              rules={[
                                {
                                  required: isVatDetailsAvailable,
                                  message: 'Required field',
                                },
                              ]}>
                              <Input
                                disabled={!isVatDetailsAvailable}
                                onChange={() => handleChangeVatInput('vatPersonAddress')}
                              />
                            </BaseForm.Item>
                          </Col>
                        </Row>
                        <Popconfirm
                          placement="topRight"
                          title="Are you sure to update the vat details?"
                          onConfirm={handleUpdateVatRegistry}
                          okText="Yes"
                          cancelText="No">
                          <Button disabled={!isVatDetailsAvailable} type="primary">
                            Update
                          </Button>
                        </Popconfirm>
                      </>
                    ) : null}
                  </S.Padding>
                </S.BlueCard>
              </S.BlurCardWrapper>
            </BaseForm>
          ) : null}
          {modalData &&
            modalData?.reservedRoomDetailsResponseList
              .filter(k => k.reservedRoomStatus === 'CHECKEDIN')
              .map((room, roomIndex) => {
                const isLastRoomWithoutPayment =
                  modalData &&
                  !modalData.reservationListResponse?.paymentDone &&
                  modalData.reservedRoomDetailsResponseList.filter(k => k.reservedRoomStatus === 'CHECKEDIN').length ===
                    1;

                const isKeyhandover = checkboxStates[roomIndex];
                const hasMealTypeChange = room?.pendingEmailResponse?.find(
                  email => email.pendingEmailType === 'MEAL_TYPE_CHANGE',
                );
                const hasExtendDaysChange = room?.pendingEmailResponse?.find(
                  email => email.pendingEmailType === 'EXTENDING_RESERVATION',
                );

                return (
                  <BaseForm
                    key={`reserved-room${roomIndex}`}
                    form={roomForms[roomIndex]}
                    size="middle"
                    onFinish={handleSubmit}>
                    <S.BlurCardWrapper>
                      <S.PrimaryOutlineCard>
                        <S.Padding>
                          <S.TitleWrapper>
                            <S.LeftTopWrapper>
                              <S.RoomNameWrapper>
                                <S.RoomName>{`${room?.reservedRoomName} - ${room?.roomNumber}`}</S.RoomName>
                              </S.RoomNameWrapper>
                              <S.RoomNameWrapper>
                                <StayTypeTitle
                                  adultCount={room?.noOfAdults ? room?.noOfAdults : 0}
                                  childCount={room?.noOfChildren ? room?.noOfChildren : 0}
                                  isBold={false}
                                  meal={room?.meal ? room?.meal : ''}
                                  name={room?.roomTypeName ? room?.roomTypeName : ''}
                                  size={FONT_SIZE.md}
                                />
                              </S.RoomNameWrapper>
                            </S.LeftTopWrapper>
                            <S.CheckInWrapper>
                              <S.CheckinTitle>Check-in: {room?.checkInDate} </S.CheckinTitle>
                              <S.CheckinTitle>Check-out: {room?.checkOutDate} </S.CheckinTitle>
                            </S.CheckInWrapper>
                          </S.TitleWrapper>
                        </S.Padding>
                        <S.Padding>
                          <S.CheckInCard>
                            <div>
                              <S.CardTitle>{t('reservation.guestDetails')}</S.CardTitle>
                              <BaseForm.Item name={`id-${roomIndex}`} noStyle>
                                <Input type="hidden" />
                              </BaseForm.Item>
                              {/* for hidden form item */}
                              <Row hidden gutter={{xs: 10, md: 15, xl: 30}}>
                                <Col xs={24} md={8}>
                                  <BaseForm.Item name={`guestType-${roomIndex}`}>
                                    <Input />
                                  </BaseForm.Item>
                                </Col>
                              </Row>
                              <Row gutter={{xs: 10, md: 15, xl: 30}}>
                                <Col xs={24} md={8}>
                                  <BaseForm.Item
                                    name={`guestName-${roomIndex}`}
                                    label="First Name"
                                    rules={[
                                      {
                                        required: false,
                                        message: 'Required field',
                                      },
                                    ]}>
                                    <Input onChange={() => handleChangeMainGuest(roomIndex)} disabled={true} />
                                  </BaseForm.Item>
                                </Col>
                                <Col xs={24} md={8}>
                                  <BaseForm.Item
                                    name={`guestLastName-${roomIndex}`}
                                    label="Last Name"
                                    rules={[
                                      {
                                        required: false,
                                        message: 'Required field',
                                      },
                                    ]}>
                                    <Input onChange={() => handleChangeMainGuest(roomIndex)} disabled={true} />
                                  </BaseForm.Item>
                                </Col>
                                <Col xs={24} md={8}>
                                  <BaseForm.Item
                                    name={`email-${roomIndex}`}
                                    label="Email Address"
                                    rules={[
                                      {
                                        type: 'email',
                                        required: false,
                                        message: 'Required field',
                                      },
                                    ]}>
                                    <Input onChange={() => handleChangeMainGuest(roomIndex)} disabled={true} />
                                  </BaseForm.Item>
                                </Col>
                                <Col xs={24} md={8}>
                                  <BaseForm.Item
                                    name={`countryId-${roomIndex}`}
                                    label="Country"
                                    rules={[{required: false, message: 'Required field'}]}>
                                    <Select
                                      onSelect={() => handleChangeMainGuest(roomIndex)}
                                      disabled={true}
                                      showSearch
                                      filterOption={false}
                                      onSearch={async (name: string) => {
                                        const countries = await getCountries(name);
                                        setcountries(countries);
                                      }}
                                      placeholder="Select Country">
                                      {countries?.map((post: {title: string; value: number}, key) => {
                                        return (
                                          <Option key={key} value={post.value}>
                                            {post.title}
                                          </Option>
                                        );
                                      })}
                                    </Select>
                                  </BaseForm.Item>
                                </Col>
                                <Col xs={24} md={8}>
                                  <BaseForm.Item
                                    name={`guestNic-${roomIndex}`}
                                    label="NIC/Passport No"
                                    rules={[
                                      {
                                        required: roomIndex === 0 ? true : false,
                                        message: 'Required field',
                                      },
                                    ]}>
                                    <Input
                                      disabled={isDisabledFormItem(
                                        room.mainGuest.hotelId,
                                        room.mainGuest.onlyThisHotelView,
                                        room.mainGuest.idNumber,
                                      )}
                                      onChange={() => handleChangeMainGuest(roomIndex)}
                                    />
                                  </BaseForm.Item>
                                </Col>
                                <Col xs={24} md={8}>
                                  <BaseForm.Item
                                    name={`passportImage-${roomIndex}`}
                                    label={`NIC/Passport Image`}
                                    rules={[
                                      {
                                        required:
                                          roomIndex === 0 && room.mainGuest.passportImageUrl === null ? true : false,
                                        message: 'Required field',
                                      },
                                    ]}>
                                    <MainGuestPassport
                                      roomIndex={roomIndex}
                                      uploadImages={guestImages}
                                      savedImage={room.mainGuest.passportImageUrl}
                                      onMainGuestImageChange={onMainGuestImageChange}
                                    />
                                  </BaseForm.Item>
                                </Col>
                              </Row>
                              <S.UpdateGuestWrapper>
                                <Col xs={24} md={4}>
                                  <S.ButtonWrapper>
                                    {room.reservedRoomStatus === 'CHECKEDIN' && (
                                      <Popconfirm
                                        placement="topRight"
                                        title="Are you sure to update the guest details?"
                                        onConfirm={() => guestUpdate(roomIndex, guestImages[roomIndex], undefined)}
                                        okText="Yes"
                                        cancelText="No">
                                        <Button type="primary">{t('commonNames.updateGuest')}</Button>
                                      </Popconfirm>
                                    )}
                                  </S.ButtonWrapper>
                                </Col>
                              </S.UpdateGuestWrapper>
                              {isChangeMainGuestDetails[roomIndex] && (
                                <S.ValidationMessageWrapper>
                                  <InfoCircleOutlined style={{color: BASE_COLORS.red}} />
                                  <S.ValidationMessage>
                                    This guest information has been modified please update guest.
                                  </S.ValidationMessage>
                                </S.ValidationMessageWrapper>
                              )}
                            </div>
                          </S.CheckInCard>
                          <S.EmptyFooterSpace />
                          <S.CheckInCard>
                            {room &&
                              room.otherGuestList.map((guest, idx) => {
                                const keyValue = `${roomIndex}${idx}`;
                                return (
                                  <div key={idx}>
                                    <S.CardTitle> Guest No - {idx + 1}</S.CardTitle>
                                    <BaseForm.Item name={`id-${roomIndex}-${idx}`} noStyle>
                                      <Input disabled={true} type="hidden" />
                                    </BaseForm.Item>
                                    {/* for hidden form item */}
                                    <Row hidden gutter={{xs: 10, md: 15, xl: 30}}>
                                      <Col xs={24} md={8}>
                                        <BaseForm.Item name={`guestType-${roomIndex}-${idx}`}>
                                          <Input />
                                        </BaseForm.Item>
                                      </Col>
                                    </Row>
                                    <Row gutter={{xs: 10, md: 15, xl: 30}}>
                                      <Col xs={24} md={8}>
                                        <BaseForm.Item
                                          name={`firstName-${roomIndex}-${idx}`}
                                          label={`First Name `}
                                          rules={[
                                            {
                                              required: false,
                                              message: 'Required field',
                                            },
                                          ]}>
                                          <Input
                                            onChange={() => handleChangeOtherGuest(roomIndex, idx)}
                                            disabled={true}
                                          />
                                        </BaseForm.Item>
                                      </Col>
                                      <Col xs={24} md={8}>
                                        <BaseForm.Item
                                          name={`lastName-${roomIndex}-${idx}`}
                                          label={`Last Name `}
                                          rules={[
                                            {
                                              required: false,
                                              message: 'Required field',
                                            },
                                          ]}>
                                          <Input
                                            onChange={() => handleChangeOtherGuest(roomIndex, idx)}
                                            disabled={true}
                                          />
                                        </BaseForm.Item>
                                      </Col>
                                      <Col xs={24} md={8}>
                                        <BaseForm.Item
                                          name={`email-${roomIndex}-${idx}`}
                                          label="Email Address"
                                          rules={[
                                            {
                                              type: 'email',
                                              required: false,
                                              message: 'Required field',
                                            },
                                          ]}>
                                          <Input
                                            onChange={() => handleChangeOtherGuest(roomIndex, idx)}
                                            disabled={true}
                                          />
                                        </BaseForm.Item>
                                      </Col>
                                      <Col xs={24} md={8}>
                                        <BaseForm.Item
                                          name={`countryId-${roomIndex}-${idx}`}
                                          label="Country"
                                          rules={[{required: false, message: 'Required field'}]}>
                                          <Select
                                            onSelect={() => handleChangeOtherGuest(roomIndex, idx)}
                                            disabled={true}
                                            showSearch
                                            filterOption={false}
                                            onSearch={async (name: string) => {
                                              const countries = await getCountries(name);
                                              setcountries(countries);
                                            }}
                                            placeholder="Select Country">
                                            {countries?.map((post: {title: string; value: number}, key) => {
                                              return (
                                                <Option key={key} value={post.value}>
                                                  {post.title}
                                                </Option>
                                              );
                                            })}
                                          </Select>
                                        </BaseForm.Item>
                                      </Col>
                                      <Col xs={24} md={8}>
                                        <BaseForm.Item
                                          name={`idNumber-${roomIndex}-${idx}`}
                                          label={`NIC/Passport No`}
                                          rules={[
                                            {
                                              required: false,
                                              message: 'Required field',
                                            },
                                          ]}>
                                          <Input onChange={() => handleChangeOtherGuest(roomIndex, idx)} />
                                        </BaseForm.Item>
                                      </Col>
                                      <Col xs={24} md={8}>
                                        <BaseForm.Item
                                          name={`passportImage-${roomIndex}-${idx}`}
                                          label={`NIC/Passport Image`}
                                          rules={[
                                            {
                                              required: false,
                                              message: 'Required field',
                                            },
                                          ]}>
                                          <OtherGuestPassport
                                            guestIndex={idx}
                                            roomIndex={roomIndex}
                                            uploadImages={guestImages}
                                            savedImage={guest.passportImageUrl}
                                            onMainGuestImageChange={handleUploadOtherGuestImage}
                                          />
                                        </BaseForm.Item>
                                      </Col>
                                    </Row>
                                    <S.UpdateGuestWrapper>
                                      <Col xs={24} md={4}>
                                        <S.ButtonWrapper>
                                          {room.reservedRoomStatus === 'CHECKEDIN' && (
                                            <Popconfirm
                                              placement="topRight"
                                              title="Are you sure to update the guest details?"
                                              onConfirm={() => guestUpdate(roomIndex, guestImages[keyValue], idx)}
                                              okText="Yes"
                                              cancelText="No">
                                              <Button type="primary"> {t('commonNames.updateGuest')}</Button>
                                            </Popconfirm>
                                          )}
                                        </S.ButtonWrapper>
                                      </Col>
                                    </S.UpdateGuestWrapper>
                                    {isChangeOtherGuestDetails[`${roomIndex}-${idx}`] && (
                                      <S.ValidationMessageWrapper>
                                        <InfoCircleOutlined style={{color: BASE_COLORS.red}} />
                                        <S.ValidationMessage>
                                          This guest information has been modified please update guest.
                                        </S.ValidationMessage>
                                      </S.ValidationMessageWrapper>
                                    )}
                                  </div>
                                );
                              })}
                          </S.CheckInCard>
                          <S.EmptyFooterSpace />
                          <S.CheckInCard>
                            <Row align="middle" gutter={{xs: 10, md: 15, xl: 30}}>
                              <Col xs={24} md={6}>
                                <BaseForm.Item
                                  name={`checkInTime-${roomIndex}`}
                                  label="Check-out Time"
                                  rules={[
                                    {
                                      required: true,
                                      message: 'Required field',
                                    },
                                  ]}>
                                  <Picker
                                    onChange={value => handleCheckoutPickerChange(value, roomIndex)}
                                    allowClear={false}
                                    inputReadOnly
                                  />
                                </BaseForm.Item>
                              </Col>
                              <Col xs={24} md={isTabletOrMobile ? 8 : 4}>
                                <BaseForm.Item
                                  label="Key Retrieval"
                                  name={`keyHandOver-${roomIndex}`}
                                  valuePropName="checked"
                                  rules={[{required: true, message: 'Required field'}]}>
                                  <Checkbox onChange={e => handleCheckboxChange(roomIndex)} />
                                </BaseForm.Item>
                              </Col>

                              <Col xs={24} md={6}>
                                <S.ButtonWrapper style={{marginRight: '6.4rem'}}>
                                  {room.reservedRoomStatus === 'CHECKEDIN' && (
                                    <Popconfirm
                                      placement="topLeft"
                                      title="Are you sure to Check-out this room?"
                                      onConfirm={() => handleRoomSubmit(roomIndex)}
                                      okText="Yes"
                                      cancelText="No">
                                      <Button
                                        disabled={
                                          !checkboxStates[roomIndex] ||
                                          !checkOutTimeStates[roomIndex] ||
                                          isLastRoomWithoutPayment
                                        }
                                        type="primary"
                                        danger>
                                        Check-out
                                      </Button>
                                    </Popconfirm>
                                  )}
                                </S.ButtonWrapper>
                              </Col>
                            </Row>
                          </S.CheckInCard>
                          <S.StayTypeOutline>
                            <S.Padding>
                              <S.TitleWrapper>
                                <S.LeftTopWrapper>
                                  <S.RoomNameWrapper>
                                    <S.RoomName>{`${room?.reservedRoomName} - ${room?.roomNumber}`}</S.RoomName>

                                    {room?.roomTypeHotelType !== 'VILLA' && (
                                      <S.RoomChangeWrapper>
                                        <S.RoomChangeOutline
                                          $isKeyhandover={isKeyhandover || isExpanded[roomIndex]}
                                          onClick={() =>
                                            handleExpand(
                                              room.stayTypeId,
                                              room.checkInDate,
                                              room.checkOutDate,
                                              roomIndex,
                                              room.roomTypeHotelType,
                                            )
                                          }>
                                          Change Room
                                        </S.RoomChangeOutline>
                                      </S.RoomChangeWrapper>
                                    )}
                                    <S.RoomChangeButton>
                                      <ExtendRoomDays
                                        isCheckOut={true}
                                        isDisable={isKeyhandover || isExpanded[roomIndex]}
                                        roomData={room}
                                        paymentData={modalData}
                                        reloadModalData={reloadModalData}
                                        onSuccess={(data: number) => {
                                          setStatus('success');
                                          setSelectedPendingMailId(data);
                                          setvisiblePendingMailModal(true);
                                        }}
                                      />
                                      {hasExtendDaysChange !== undefined && (
                                        <Tooltip title="Resend mail">
                                          <MailOutlined
                                            style={{paddingRight: '5px', cursor: 'pointer'}}
                                            onClick={() => handleClickMailIcon(hasExtendDaysChange.pendingEmailId)}
                                          />
                                        </Tooltip>
                                      )}
                                    </S.RoomChangeButton>
                                  </S.RoomNameWrapper>

                                  <S.RoomNameWrapper>
                                    <StayTypeTitle
                                      adultCount={room?.noOfAdults ? room?.noOfAdults : 0}
                                      childCount={room?.noOfChildren ? room?.noOfChildren : 0}
                                      isBold={false}
                                      meal={room?.meal ? room?.meal : ''}
                                      name={room?.roomTypeName ? room?.roomTypeName : ''}
                                      size={FONT_SIZE.md}
                                    />

                                    <S.RoomChangeWrapper
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        gap: 5,
                                      }}>
                                      <S.RoomChangeButton>
                                        <S.RoomChangeOutline
                                          // $isKeyhandover={
                                          //   isKeyhandover || isExpanded[roomIndex] || isStayTypeExpanded[roomIndex]
                                          // }
                                          onClick={() =>
                                            handleExpandMealType(
                                              room.reservedRoomId,
                                              room.checkInDate,
                                              room.checkOutDate,
                                              roomIndex,
                                            )
                                          }>
                                          {t('reservation.changeMealPlan')}
                                        </S.RoomChangeOutline>
                                        {hasMealTypeChange !== undefined && (
                                          <Tooltip title="Resend mail">
                                            <MailOutlined
                                              style={{paddingRight: '5px', cursor: 'pointer'}}
                                              onClick={() => handleClickMailIcon(hasMealTypeChange.pendingEmailId)}
                                            />
                                          </Tooltip>
                                        )}
                                      </S.RoomChangeButton>
                                    </S.RoomChangeWrapper>
                                    {/* {!modalData?.reservationListResponse?.paymentDone && ( */}
                                    {permissions.VIEW && (
                                      <div>
                                        <ApplyResDiscount
                                          isDisable={false}
                                          roomData={room}
                                          paymentData={modalData}
                                          reloadModalData={reloadModalData}
                                        />
                                      </div>
                                    )}
                                    {/* )} */}
                                  </S.RoomNameWrapper>
                                  {room?.extraChildCount > 0 ? (
                                    <S.ExtrachildWrapper>
                                      <S.ExtraChildText>{t('commonNames.numberOfExtraBed')} : </S.ExtraChildText>
                                      <S.ExtraChildText>{room?.extraChildCount}</S.ExtraChildText>
                                    </S.ExtrachildWrapper>
                                  ) : null}
                                </S.LeftTopWrapper>
                              </S.TitleWrapper>

                              <Collapse isOpened={isExpanded[roomIndex]}>
                                <div
                                  ref={ref => {
                                    if (isExpanded[roomIndex]) {
                                      ref && ref.scrollIntoView({behavior: 'smooth', block: 'start'});
                                    }
                                  }}
                                  style={{overflowY: 'scroll'}}>
                                  <S.GreenCardWrapper>
                                    <S.BlueCard>
                                      <S.Padding>
                                        <Spinner tip="Loading" spinning={loadRooms}>
                                          <S.ExpandableSection>
                                            <S.TitleWrapper>
                                              <S.AvailableRoomTitle>Available Alternative ooms</S.AvailableRoomTitle>
                                              <S.RightButtonWrapper>
                                                <Button
                                                  onClick={() => changeRoom(room.reservedRoomId)}
                                                  loading={loadUpdateRoom}
                                                  disabled={selectedRoomId === 0 ? true : false}
                                                  type="primary">
                                                  Update
                                                </Button>
                                                <S.CloseIcon onClick={handleCloseIcon} />
                                              </S.RightButtonWrapper>
                                            </S.TitleWrapper>
                                            {isEmpty(availableRoom) && !loadRooms ? (
                                              <Empty description="Rooms not available" />
                                            ) : (
                                              <S.RoomContainer
                                                style={{
                                                  maxWidth: 904,
                                                  overflowX: 'scroll',
                                                }}>
                                                {availableRoom.map((availablerooms: IAvailableRooms, index: number) => {
                                                  const imageData =
                                                    availablerooms.roomImageResponseList.length > 0
                                                      ? availablerooms.roomImageResponseList[0]
                                                      : '';
                                                  return (
                                                    <S.CardWrapper key={availablerooms.roomId}>
                                                      <Badge.Ribbon color="#4c9a50" text={availablerooms.roomNumber}>
                                                        <S.CustomCard
                                                          style={{
                                                            width: 170,
                                                          }}
                                                          hoverable
                                                          cover={
                                                            <img
                                                              style={{height: 100, width: 170, objectFit: 'cover'}}
                                                              src={imageData.imageUrl}
                                                              alt=""
                                                            />
                                                          }
                                                          onClick={() => handleRoomSelect(availablerooms.roomId)}>
                                                          <S.RoomName>{availablerooms.roomName}</S.RoomName>
                                                          <S.ViewTypeName>{availablerooms.viewTypeName}</S.ViewTypeName>
                                                          {selectedRoomId === availablerooms.roomId ? (
                                                            <S.SelectedText>Selected</S.SelectedText>
                                                          ) : null}
                                                        </S.CustomCard>
                                                      </Badge.Ribbon>
                                                    </S.CardWrapper>
                                                  );
                                                })}
                                              </S.RoomContainer>
                                            )}
                                          </S.ExpandableSection>
                                        </Spinner>
                                      </S.Padding>
                                    </S.BlueCard>
                                  </S.GreenCardWrapper>
                                </div>
                              </Collapse>

                              <Collapse isOpened={isMealTypeExpanded[roomIndex]}>
                                <div
                                  ref={ref => {
                                    if (isMealTypeExpanded[roomIndex]) {
                                      ref && ref.scrollIntoView({behavior: 'smooth', block: 'start'});
                                    }
                                  }}
                                  style={{overflowY: 'scroll'}}>
                                  <S.GreenCardWrapper>
                                    <S.BlueCard>
                                      <S.StayExpandableSection>
                                        <S.RightButtonWrapper>
                                          <S.CloseIcon
                                            style={{marginTop: 10, marginRight: 10}}
                                            onClick={handleCloseMealType}
                                          />
                                        </S.RightButtonWrapper>

                                        {isEmpty(mealTypes) ? (
                                          <div
                                            style={{
                                              display: 'flex',
                                              flexDirection: 'row',
                                              justifyContent: 'center',
                                              alignItems: 'center',
                                              alignSelf: 'center',
                                            }}>
                                            Rooms not available
                                          </div>
                                        ) : (
                                          <div>
                                            {mealTypes.map((meal: any, index) => {
                                              return (
                                                <S.StayTypeName
                                                  style={{
                                                    width: 'fit-content',
                                                    paddingLeft: '15px',
                                                    paddingRight: '15px',
                                                  }}
                                                  key={index}
                                                  onClick={() =>
                                                    handleClickMealType(
                                                      meal,
                                                      room.checkInDate,
                                                      room.checkOutDate,
                                                      room.roomId,
                                                    )
                                                  }
                                                  $isSelected={selectedStayTypeId === meal?.stayTypeId}>
                                                  {renderTitle(
                                                    meal,
                                                    room.reservationCurrencyPrefix,
                                                    selectedStayTypeId === meal?.stayTypeId,
                                                  )}
                                                </S.StayTypeName>
                                              );
                                            })}
                                            <S.TitleWrapper
                                              style={{
                                                display: 'flex',
                                                marginRight: '10px',
                                                marginBottom: '10px',
                                                justifyContent: 'flex-end',
                                              }}>
                                              <Button
                                                onClick={() =>
                                                  changeStayType(
                                                    room.reservedRoomId,
                                                    room.roomId,
                                                    // @ts-ignore
                                                    selectedStayTypeId,
                                                    room.reservationCurrencyPrefix,
                                                    'MEAL_TYPE_CHANGE',
                                                  )
                                                }
                                                loading={loadUpdateStayType}
                                                disabled={selectedStayTypeId === undefined ? true : false}
                                                type="primary">
                                                Update
                                              </Button>
                                            </S.TitleWrapper>
                                          </div>
                                        )}
                                      </S.StayExpandableSection>
                                    </S.BlueCard>
                                  </S.GreenCardWrapper>
                                </div>
                              </Collapse>
                            </S.Padding>
                          </S.StayTypeOutline>
                        </S.Padding>
                      </S.PrimaryOutlineCard>
                    </S.BlurCardWrapper>
                  </BaseForm>
                );
              })}

          {modalData &&
            modalData?.reservedRoomDetailsResponseList.filter(k => k.reservedRoomStatus === 'CHECKEDOUT').length >
              0 && (
              <S.BlurCardWrapper>
                <S.BlueCard>
                  <S.Padding>
                    <S.CardTitle>Checked-out Rooms Information</S.CardTitle>
                    {modalData &&
                      modalData?.reservedRoomDetailsResponseList
                        .filter(k => k.reservedRoomStatus === 'CHECKEDOUT')
                        .map((room, roomIndex) => {
                          const checkOutTime = dayjs(room?.checkOutTimeReservedRoom, 'HH:mm:ss');
                          const checkInTime = dayjs(room?.checkInTimeReservedRoom, 'HH:mm:ss');
                          return (
                            <S.BlurCardWrapper key={roomIndex}>
                              <S.BlueCard>
                                <S.Padding>
                                  <S.TitleWrapper>
                                    <S.CardTitle>
                                      <S.RoomName>{`${room?.reservedRoomName} - ${room?.roomNumber}`}</S.RoomName>
                                      &nbsp;&nbsp; (
                                      <StayTypeTitle
                                        adultCount={room?.noOfAdults ? room?.noOfAdults : 0}
                                        childCount={room?.noOfChildren ? room?.noOfChildren : 0}
                                        isBold={true}
                                        meal={room?.meal ? room?.meal : ''}
                                        name={room?.roomTypeName ? room?.roomTypeName : ''}
                                        size={FONT_SIZE.md}
                                      />
                                      )
                                    </S.CardTitle>
                                  </S.TitleWrapper>
                                  <Descriptions>
                                    <Descriptions.Item label="First Name">
                                      {room?.mainGuest.firstName}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="Last Name">{room?.mainGuest.lastName}</Descriptions.Item>
                                    <Descriptions.Item label="Checked-in Date">{room?.checkInDate}</Descriptions.Item>
                                    <Descriptions.Item label="Checked-in Time">
                                      {checkInTime.format('h:mm A')}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="Checked-out Date">{room?.checkOutDate}</Descriptions.Item>
                                    <Descriptions.Item label="Checked-out Time">
                                      {checkOutTime.format('h:mm A')}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="ID Number">{room?.mainGuest.idNumber}</Descriptions.Item>
                                    <Descriptions.Item label="Email">{room?.mainGuest?.email}</Descriptions.Item>
                                    <Descriptions.Item label="Country">{room?.mainGuest.countryName}</Descriptions.Item>
                                  </Descriptions>
                                  {room?.otherGuestList?.map((g, index) => {
                                    return (
                                      <Descriptions title="Additional Guest Infomation" key={index}>
                                        <Descriptions.Item label="First Name">{g.firstName}</Descriptions.Item>
                                        <Descriptions.Item label="Last Name">{g.lastName}</Descriptions.Item>
                                        {g.email && <Descriptions.Item label="Email">{g.email}</Descriptions.Item>}
                                        {g.idNumber && (
                                          <Descriptions.Item label="NIC/Passport">{g.idNumber}</Descriptions.Item>
                                        )}
                                        {g.countryName && (
                                          <Descriptions.Item label="Country">{g.countryName}</Descriptions.Item>
                                        )}
                                      </Descriptions>
                                    );
                                  })}
                                </S.Padding>
                              </S.BlueCard>
                            </S.BlurCardWrapper>
                          );
                        })}
                  </S.Padding>
                </S.BlueCard>
              </S.BlurCardWrapper>
            )}
        </React.Fragment>
        <S.EmptyFooterSpace />
      </Modal>

      <Modal
        footer={[
          <Space align="end" key="submit-area">
            <Popconfirm
              placement="rightTop"
              title={`Are you sure to pay the total amount of ${paymentData.currencyPrefix} ${formatNumberToDecimal(
                !isNaN(payableAmount) ? payableAmount : 0,
                2,
              )}?`}
              onConfirm={() => {
                form.submit();
              }}
              okText="Yes"
              cancelText="No">
              {paymentType === 'ONLINE_PAYMENT' ? (
                <Button disabled={isNaN(payableAmount) || payableAmount <= 0} key="submit" type="primary">
                  Send Link
                </Button>
              ) : null}
              {paymentType !== 'ONLINE_PAYMENT' ? (
                <Button disabled={isNaN(payableAmount) || payableAmount <= 0} key="submit" type="primary">
                  Pay
                </Button>
              ) : null}
            </Popconfirm>
          </Space>,
        ]}
        title="Make Payment"
        size="small"
        open={isModalVisible}
        onCancel={onModalCancel}>
        <MakeReservationPayment
          isModalVisible={isModalVisible}
          readOnly={true}
          onCancel={onModalCancel}
          form={form}
          isFullyPaid={isFullyPaid}
          setIsFullyPaid={setIsFullyPaid}
          rowData={paymentData}
          reloadData={() => {
            setPaymentData({
              currencyId: 0,
              currencyPrefix: 'LKR',
              dueAmount: 0,
              id: 0,
              reservationId: 0,
            });
            handlePressCheckOut();
          }}
          onChangeAmount={(value: React.SetStateAction<number>) => {
            setpayableAmount(value);
          }}
          isCheckIn={false}
          handleChangePaymentType={type => {
            setPaymentType(type);
            setpayableAmount(paymentData.dueAmount);
          }}
          onStart={() => {
            // setvisiblePendingMailModal(true);
            setStatus('loading');
          }}
          onSuccess={(data: any) => {
            setStatus('success');
            setSelectedPendingMailId(data.result.pendingEmailId);
            setvisiblePendingMailModal(true);
            settemplateMessage(EMAIL_TEMPLATE_MESSAGES.PAYMENT.SUCCESS);
          }}
          onFailure={() => {
            setStatus('error');
          }}
        />
      </Modal>

      <Modal
        footer={[
          <Space align="end" key="submit-area">
            <Button onClick={() => changeLeadTypeForm.submit()} key="submit" type="primary">
              Update
            </Button>
          </Space>,
        ]}
        title="Choose Lead Type"
        size="small"
        open={isVisibleLeadTypeModal}
        onCancel={onLeadTypeModalClose}>
        <UpdateLeadType
          data={leadTypes}
          isModalVisible={isVisibleLeadTypeModal}
          onCancel={onLeadTypeModalClose}
          form={changeLeadTypeForm}
          leadType={modalData.reservationListResponse.leadType}
          reservationId={modalData.reservationListResponse.reservationId}
          reloadData={() => {
            handlePressCheckOut();
          }}
        />
      </Modal>

      <Modal
        footer={[
          <Space align="end" key="submit-area">
            <Button onClick={() => changeBookingTypeForm.submit()} key="submit" type="primary">
              Update
            </Button>
          </Space>,
        ]}
        title="Choose Booking Type"
        size="small"
        open={isVisibleBookigTypeModal}
        onCancel={onBookingTypeModalClose}>
        <UpdateBookingType
          data={[]}
          isModalVisible={isVisibleBookigTypeModal}
          onCancel={onBookingTypeModalClose}
          form={changeBookingTypeForm}
          bookingType={modalData.reservationListResponse?.bookingType}
          reservationId={modalData.reservationListResponse.reservationId}
          reloadData={() => {
            reloadModalData();
          }}
        />
      </Modal>

      {visiblePendingMailModal && (
        <EmailCustomizer
          isOpen={visiblePendingMailModal}
          setIsOpen={setvisiblePendingMailModal}
          status={status}
          setStatus={setStatus}
          onSubmitEmailCustomizer={function (data: any): void {
            setSelectedPendingMailId(null);
          }}
          isResender={false}
          emailType={EMAIL_TYPES.PAYMENT}
          pendingMailId={selectedPendingMailId}
          message={templateMessage}
          reservationId={modalData.reservationListResponse.reservationId}
          onClose={() => {
            setSelectedPendingMailId(null);
          }}
        />
      )}
    </div>
  );
};

export default CheckOut;

// { !modalData?.reservationListResponse?.paymentDone &&
//   !isPositiveNumber(modalData.reservationListResponse.outStanding) ? (
//   <S.PayButtonWrapper>
//     <RefundModal
//       invoiceId={modalData?.reservationListResponse?.invoiceId}
//       paymentData={{
//         currencyId: modalData?.reservationListResponse?.currencyId,
//         currencyPrefix: modalData?.reservationListResponse?.reservationCurrencyPrefix,
//         dueAmount: modalData?.reservationListResponse?.outStanding,
//         id: modalData?.reservationListResponse?.invoiceId,
//         reservationId: modalData?.reservationListResponse?.reservationId,
//       }}
//       reloadData={async () => {
//         const status = ['CHECKEDIN', 'CHECKEDOUT'];
//         await dispatch(
//           getReservedRoomModalData({
//             reservationId: modalData.reservationListResponse.reservationId,
//             status: status,
//           }),
//         );
//       }}
//     />
//   </S.PayButtonWrapper>
// ) : null}
