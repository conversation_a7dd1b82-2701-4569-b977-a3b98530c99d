import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const CreateGuide = (payload: CreateGuideProps): Promise<GuideResponse> => {
  return instance.post<GuideResponse>(HOTEL_SERVICE + 'guide', payload).then(({data}) => data);
};

export const UpdateGuide = (payload: UpdateGuideProps): Promise<GuideResponse> => {
  return instance.put<GuideResponse>(HOTEL_SERVICE + 'guide', payload).then(({data}) => data);
};

export const getAllGuides = (
  {name, email, contactNumber, idNumber, channelName}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<GuideResponse> =>
  instance
    .get<GuideResponse>(
      HOTEL_SERVICE +
        `guide/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${name ? name : ''}&email=${
          email ? email : ''
        }&contactNumber=${contactNumber ? contactNumber : ''}&idNumber=${idNumber ? idNumber : ''}&channelName=${
          channelName ? channelName : ''
        }`,
    )
    .then(({data}) => data);

export const DeleteGuide = (id: number): Promise<GuideResponse> =>
  instance.delete<GuideResponse>(HOTEL_SERVICE + `guide/${id}`).then(({data}) => data);

export const getGuidesByChannel = (channelId: number): Promise<GuideResponse> => {
  return instance.get<GuideResponse>(HOTEL_SERVICE + `guide/${channelId}`).then(({data}) => data);
};

export interface CreateGuideProps {
  name: string;
  email: string;
  contactNumber: string;
  idNumber: string;
  nicNumber: boolean;
  remarks: string;
  channelId: number;
}

export interface UpdateGuideProps {
  id: number;
  name: string;
  email: string;
  contactNumber: string;
  idNumber: string;
  nicNumber: boolean;
  remarks: string;
  channelId: number;
}

export interface GuideResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  name: string;
  email: string;
  contactNumber: string;
  idNumber: string;
  channelName: string;
}
