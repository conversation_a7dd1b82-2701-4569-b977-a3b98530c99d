import React, {useState} from 'react';
import './ProductRegistration.css';
import {ChevronRight, ChevronLeft} from 'lucide-react';
import {useForm, SubmitHandler} from 'react-hook-form';
import TestimonialImage from '../../../assets/restaurant/address.jpg';
import {useNavigate} from 'react-router-dom';

interface UserInfo {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  phoneNumber: string;
  address: string;
}

interface RestaurantInfo {
  restaurantName: string;
  googleLocation: string;
  restaurantLogo: FileList;
  restaurantImage: FileList;
  restaurantAddress: string;
}

const ProductRegistration: React.FC = () => {
  const [step, setStep] = useState(1);
  const [userData, setUserData] = useState<UserInfo | null>(null);
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: {errors},
  } = useForm<UserInfo>();

  const {
    register: registerRestaurant,
    handleSubmit: handleRestaurantSubmit,
    formState: {errors: restaurantErrors},
  } = useForm<RestaurantInfo>();

  const handleNext: SubmitHandler<UserInfo> = userInfo => {
    console.log('User Info:', userInfo);
    setUserData(userInfo);
    setStep(2);
  };

  const handleBack = () => setStep(1);

  const handleRestaurantSubmitForm: SubmitHandler<RestaurantInfo> = restaurantData => {
    console.log('Restaurant Info:', restaurantData);
    const payload = {
      firstName: userData?.firstName,
      lastName: userData?.lastName,
      username: userData?.username,
      email: userData?.email,
      phoneNumber: userData?.phoneNumber,
      address: userData?.address,
      restaurantName: restaurantData?.restaurantName,
      googleLocation: restaurantData?.googleLocation,
      restaurantAddress: restaurantData?.restaurantAddress,
      restaurantLogo: restaurantData.restaurantLogo[0],
      restaurantImage: restaurantData.restaurantImage[0],
    };

    navigate('/');
    console.log('Payload:', payload);
  };

  return (
    <div className="product-registration">
      <div className="container">
        <div className="content-wrapper">
          {/* Left Column - Marketing Content */}
          <div className="marketing-content">
            <div className="brand-header">
              <div className="logo">
                <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                  <circle cx="12" cy="12" r="11" stroke="white" strokeWidth="2" fill="none" />
                  <circle cx="12" cy="12" r="6" fill="white" />
                </svg>
              </div>
              <span className="brand-name">Taste Zone</span>
            </div>
            <h1 className="hero-title">Discover the Future of Dining with Taste Zone</h1>
            <p className="hero-subtitle">
              No sign-up fees. No hidden costs.
              <br />
              Just incredible food, delivered fast.
              <br />
              See why food lovers cannot stop talking about us.
            </p>

            <div className="rating">
              {[1, 2, 3, 4, 5].map(star => (
                <svg key={star} className="star" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
              <span className="rating-text">Rated 4.9/5.0 stars by 500+ happy customers</span>
            </div>

            <h3 className="section-title">Why People Love Taste Zone</h3>

            <div className="features-grid">
              {/* Feature 1 */}
              <div className="feature">
                <div className="feature-header">
                  <div className="feature-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                    </svg>
                  </div>
                  <h4 className="feature-title">Chef-Curated Menus</h4>
                </div>
                <p className="feature-description">Signature dishes made with fresh, local ingredients.</p>
              </div>

              {/* Feature 2 */}
              <div className="feature">
                <div className="feature-header">
                  <div className="feature-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                    </svg>
                  </div>
                  <h4 className="feature-title">Fast Delivery</h4>
                </div>
                <p className="feature-description">Get hot, delicious meals delivered in under 30 minutes.</p>
              </div>

              {/* Feature 3 */}
              <div className="feature">
                <div className="feature-header">
                  <div className="feature-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                    </svg>
                  </div>
                  <h4 className="feature-title">Exclusive App</h4>
                </div>
                <p className="feature-description">Order and earn rewards through our mobile app.</p>
              </div>

              {/* Feature 4 */}
              <div className="feature">
                <div className="feature-header">
                  <div className="feature-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                    </svg>
                  </div>
                  <h4 className="feature-title">Dine-in Experience</h4>
                </div>
                <p className="feature-description">Modern interiors, warm service, and unforgettable ambiance.</p>
              </div>

              {/* Feature 5 */}
              <div className="feature">
                <div className="feature-header">
                  <div className="feature-icon leader-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z" />
                      <path d="M13 7h-2v6h2V7zm0 8h-2v2h2v-2z" />
                    </svg>
                  </div>
                  <h4 className="feature-title">Top Rated in 2024</h4>
                </div>
              </div>

              {/* Feature 6 */}
              <div className="feature">
                <div className="feature-header">
                  <div className="feature-icon rated-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2L4 6v12l8 4 8-4V6l-8-4zm0 2.236l6 3V16.764l-6 3-6-3V7.236l6-3z" />
                    </svg>
                  </div>
                  <h4 className="feature-title">Customer Favorite</h4>
                </div>
                <p className="feature-description">Over 10,000 orders fulfilled this year alone.</p>
              </div>
            </div>

            {/* Testimonial */}
            <div className="testimonial">
              <img src={TestimonialImage} alt="Customer" className="testimonial-image" />
              <div className="testimonial-content">
                <p className="testimonial-author">David Kim, Food Blogger</p>
                <p className="testimonial-text">
                  Taste Zone has completely changed the way I think about takeout—delicious, fast, and reliable.
                </p>
              </div>
            </div>
          </div>

          {/* Right Column - Form */}
          <div className="form-container">
            <div className="form-wrapper">
              {step === 1 ? (
                <form onSubmit={handleSubmit(handleNext)}>
                  <h2 className="form-title">Taste Zone Registration</h2>
                  <div className="form">
                    <div className="name-fields">
                      <div className="form-group">
                        <label className="form-label">
                          First name<span style={{color: 'red', padding: '0 2px'}}>*</span>
                        </label>
                        <input
                          type="text"
                          placeholder="Pirabaharan"
                          className="form-input"
                          {...register('firstName', {required: 'First name is required'})}
                        />
                        {errors.firstName && <p className="error-text">{errors.firstName.message}</p>}
                      </div>
                      <div className="form-group">
                        <label className="form-label">
                          Last name<span style={{color: 'red', padding: '0 2px'}}>*</span>
                        </label>
                        <input
                          type="text"
                          placeholder="Yathavan"
                          className="form-input"
                          {...register('lastName', {required: 'Last name is required'})}
                        />
                        {errors.lastName && <p className="error-text">{errors.lastName.message}</p>}
                      </div>
                    </div>

                    <div className="form-group">
                      <label className="form-label">
                        Username<span style={{color: 'red', padding: '0 2px'}}>*</span>
                      </label>
                      <input
                        key="user_name"
                        type="text"
                        placeholder="yathavan"
                        className="form-input"
                        {...register('username', {required: 'Username is required'})}
                      />
                      {errors.username && <p className="error-text">{errors.username.message}</p>}
                    </div>

                    <div className="form-group">
                      <label className="form-label">
                        Email<span style={{color: 'red', padding: '0 2px'}}>*</span>
                      </label>
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        className="form-input"
                        {...register('email', {required: 'Email is required', pattern: /^\S+@\S+$/i})}
                      />
                      {errors.email && <p className="error-text">{errors.email.message}</p>}
                    </div>
                    <div className="form-group">
                      <label className="form-label">
                        Phone Number<span style={{color: 'red', padding: '0 2px'}}>*</span>
                      </label>
                      <input
                        type="tel"
                        placeholder="+****************"
                        className="form-input"
                        {...register('phoneNumber', {required: 'Phone number is required'})}
                      />
                      {errors.phoneNumber && <p className="error-text">{errors.phoneNumber.message}</p>}
                    </div>
                    <div className="form-group">
                      <label className="form-label">
                        Address<span style={{color: 'red', padding: '0 2px'}}>*</span>
                      </label>
                      <textarea
                        placeholder="No-20, 1st Floor, Main Street, Colombo 01"
                        className="form-input"
                        {...register('address', {required: 'Address is required'})}
                      />
                      {errors.address && <p className="error-text">{errors.address.message}</p>}
                    </div>
                    <button type="submit" className="submit-button">
                      Next
                      <ChevronRight size={20} className="button-arrow" />
                    </button>
                  </div>
                </form>
              ) : (
                <form onSubmit={handleRestaurantSubmit(handleRestaurantSubmitForm)}>
                  <h2 className="form-title">Restaurant Information</h2>
                  {/* <div className="info-header">
                    <button className="info-header-button">
                      <ChevronLeft size={20} className="button-arrow" />
                    </button>
                    <span className="info-header-text">{userData?.firstName}</span>
                    <span className="info-header-text">{userData?.lastName}</span>
                    <span className="info-header-text">{userData?.phoneNumber}</span>                  
                  </div> */}
                  <div className="form">
                    <div className="form-group">
                      <label className="form-label">
                        Restaurant Name<span style={{color: 'red', padding: '0 2px'}}>*</span>
                      </label>
                      <input
                        type="text"
                        placeholder="Restaurant Name"
                        className="form-input"
                        {...registerRestaurant('restaurantName', {required: 'Restaurant name is required'})}
                      />
                      {restaurantErrors.restaurantName && (
                        <p className="error-text">{restaurantErrors.restaurantName.message}</p>
                      )}
                    </div>
                    <div className="form-group">
                      <label className="form-label">Google Location</label>
                      <input
                        type="url"
                        placeholder="https://maps.google.com"
                        className="form-input"
                        {...registerRestaurant('googleLocation')}
                      />
                    </div>
                    <div className="form-row">
                      <div className="form-group">
                        <label className="form-label">
                          Restaurant Logo<span style={{color: 'red', padding: '0 2px'}}>*</span>
                        </label>
                        <input
                          type="file"
                          accept="image/*"
                          className="form-input"
                          {...registerRestaurant('restaurantLogo', {required: 'Restaurant logo is required'})}
                        />
                        {restaurantErrors.restaurantLogo && (
                          <p className="error-text">{restaurantErrors.restaurantLogo.message}</p>
                        )}
                      </div>
                      <div className="form-group">
                        <label className="form-label">
                          Restaurant Image<span style={{color: 'red', padding: '0 2px'}}>*</span>
                        </label>
                        <input
                          type="file"
                          accept="image/*"
                          className="form-input"
                          {...registerRestaurant('restaurantImage', {required: 'Restaurant image is required'})}
                        />
                        {restaurantErrors.restaurantImage && (
                          <p className="error-text">{restaurantErrors.restaurantImage.message}</p>
                        )}
                      </div>
                    </div>

                    <div className="form-group">
                      <label className="form-label">
                        Restaurant Address<span style={{color: 'red', padding: '0 2px'}}>*</span>
                      </label>
                      <textarea
                        placeholder="123 Main St, City, Country"
                        className="form-input"
                        {...registerRestaurant('restaurantAddress', {required: 'Restaurant address is required'})}
                      />
                      {restaurantErrors.restaurantAddress && (
                        <p className="error-text">{restaurantErrors.restaurantAddress.message}</p>
                      )}
                    </div>

                    <div className="button-group">
                      <button type="button" className="submit-button back-button" onClick={handleBack}>
                        <ChevronLeft size={20} className="button-arrow" />
                        Back
                      </button>
                      <button type="submit" className="submit-button">
                        Submit
                      </button>
                    </div>
                    <p className="terms-text">
                      By providing us with your information you are consenting to our{' '}
                      <a href="#" className="terms-link">
                        Terms of Service
                      </a>{' '}
                      and{' '}
                      <a href="#" className="terms-link">
                        Privacy Policy
                      </a>
                      .
                    </p>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductRegistration;
