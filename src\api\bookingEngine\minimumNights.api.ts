/**
 * <AUTHOR>
 * @email [johnh<PERSON><EMAIL>]
 * @create date 2024-12-11 17:22:21
 * @modify date 2024-12-11 17:22:21
 * @desc [minimum night api]
 */
import instance, {HOTEL_SERVICE} from '../instance';

export interface MinimumNightResponse {
  status: string;
  statusCode: string;
  message: string;
  result: any;
}

export const getMinimumNights = (hotelId: number | undefined, web: boolean): Promise<MinimumNightResponse> =>
  instance
    .get<MinimumNightResponse>(`${HOTEL_SERVICE}minimum-night-config/web/search?hotelId=${hotelId}&web=${web}`)
    .then(({data}) => data);
