import instance, {HOTEL_SERVICE} from '@app/api/instance';
import {
  IAdditionalActivityResponse,
  ICreateAdditionalActivity,
  ICreateAdditionalActivityBooking,
  ICreateAdditionalActivityPack,
  IEventDiscountPayload,
  IRetriveAdditionalActivities,
  IUpdateAdditionalActivityPack,
} from '@app/pages/Hotel/AdditionalActivity/interface/interface';

export const createAdditionalActivity = (payload: ICreateAdditionalActivity): Promise<IAdditionalActivityResponse> => {
  return instance
    .post<IAdditionalActivityResponse>(HOTEL_SERVICE + 'additional-activity', payload)
    .then(({data}) => data);
};

export const updateAdditionalActivity = (payload: ICreateAdditionalActivity): Promise<IAdditionalActivityResponse> => {
  return instance
    .put<IAdditionalActivityResponse>(HOTEL_SERVICE + 'additional-activity', payload)
    .then(({data}) => data);
};

export const getAvailableActivities = (
  checkInDate: string,
  resident: boolean,
  hotelId: number,
): Promise<IAdditionalActivityResponse> => {
  return instance
    .get<IAdditionalActivityResponse>(
      HOTEL_SERVICE +
        `available_additional-activity?checkInDate=${checkInDate}&resident=${resident}&hotelId=${hotelId}`,
    )
    .then(({data}) => data);
};

export const getAllAdditionalActivities = (
  hotelId: number,
  pageSize: number | undefined,
  current: number,
): Promise<IAdditionalActivityResponse> =>
  instance
    .get<IAdditionalActivityResponse>(
      HOTEL_SERVICE +
        `additional-activity/search?hotelId=${hotelId}&size=${pageSize}&page=${current}&direction=DESC&sortField=id`,
    )
    .then(({data}) => data);

export const getAllActivityBookings = (
  hotelId: number,
  pageSize: number | undefined,
  current: number,
  searchQuery: FilterBookingSearchProps,
): Promise<IAdditionalActivityResponse> => {
  return instance
    .get<IAdditionalActivityResponse>(
      HOTEL_SERVICE +
        `additional-activity-reservation/search?hotelId=${hotelId}&size=${pageSize}&page=${current}&ownerName=${searchQuery.ownerName}&refNumber=${searchQuery.refNumber}&direction=DESC&sortField=id`,
    )
    .then(({data}) => data);
};

export const getAllActivityReservations = (
  hotelId: number,
  pageSize: number | undefined,
  current: number,
  searchQuery: FilterBookingSearchProps,
): Promise<IAdditionalActivityResponse> => {
  return instance
    .get<IAdditionalActivityResponse>(
      HOTEL_SERVICE +
        `additional-activity-reservation/search/multi-events?hotelId=${hotelId}&size=${pageSize}&page=${current}&ownerName=${searchQuery.ownerName}&refNumber=${searchQuery.refNumber}&direction=DESC&sortField=id`,
    )
    .then(({data}) => data);
};

export const getEventsByEventId = (eventId: number): Promise<IAdditionalActivityResponse> => {
  return instance
    .get<IAdditionalActivityResponse>(HOTEL_SERVICE + `activity_reservation/${eventId}`)
    .then(({data}) => data);
};

export const makeActivityPayment = (payload: any): Promise<IAdditionalActivityResponse> => {
  return instance
    .post<IAdditionalActivityResponse>(HOTEL_SERVICE + `additional-activity-payment`, payload)
    .then(({data}) => data);
};

export const retriveEventsById = (payload: IRetriveAdditionalActivities): Promise<IAdditionalActivityResponse> => {
  return instance
    .put<IAdditionalActivityResponse>(HOTEL_SERVICE + `additional-activity-reservation`, payload)
    .then(({data}) => data);
};

export const getAllActivityBookingHistory = (
  hotelId: number,
  pageSize: number | undefined,
  current: number,
  searchQuery: FilterHistorySearchProps,
): Promise<IAdditionalActivityResponse> => {
  return instance
    .get<IAdditionalActivityResponse>(
      HOTEL_SERVICE +
        `additional-activity-reservation/search/history?hotelId=${hotelId}&size=${pageSize}&page=${current}&ownerName=${searchQuery.ownerName}&refNumber=${searchQuery.refNumber}&direction=DESC&sortField=id`,
    )
    .then(({data}) => data);
};
export const getAllActivities = (hotelId: number): Promise<IAdditionalActivityResponse> =>
  instance
    .get<IAdditionalActivityResponse>(HOTEL_SERVICE + `additional-activity/searchwithout-pagination?hotelId=${hotelId}`)
    .then(({data}) => data);

export const getAllActivitiesWithoutPagination = (hotelId: number): Promise<IAdditionalActivityResponse> =>
  instance
    .get<IAdditionalActivityResponse>(
      HOTEL_SERVICE + `additional-activity/searchwithout-pagination-activity?hotelId=${hotelId}`,
    )
    .then(({data}) => data);

export const updateAdditionalActivityPack = (
  payload: IUpdateAdditionalActivityPack,
): Promise<IAdditionalActivityResponse> => {
  return instance.put<IAdditionalActivityResponse>(HOTEL_SERVICE + 'additional-pack', payload).then(({data}) => data);
};

export const createAdditionalActivityPack = (
  payload: ICreateAdditionalActivityPack,
): Promise<IAdditionalActivityResponse> => {
  return instance.post<IAdditionalActivityResponse>(HOTEL_SERVICE + 'additional-pack', payload).then(({data}) => data);
};
export const deleteAdditionalActivityPack = (id: number): Promise<IAdditionalActivityResponse> => {
  return instance.delete<IAdditionalActivityResponse>(HOTEL_SERVICE + `additional-pack/${id}`).then(({data}) => data);
};

export const getAllAdditionalActivityPacks = (
  hotelId: number,
  pageSize: number | undefined,
  current: number,
): Promise<IAdditionalActivityResponse> =>
  instance
    .get<IAdditionalActivityResponse>(
      HOTEL_SERVICE +
        `additional-pack/search?hotelId=${hotelId}&size=${pageSize}&page=${current}&sortField=id&direction=DESC`,
    )
    .then(({data}) => data);

export const getPacksByActivityId = (activityId: number): Promise<IAdditionalActivityResponse> =>
  instance
    .get<IAdditionalActivityResponse>(HOTEL_SERVICE + `additional-pack/activity/${activityId}`)
    .then(({data}) => data);

export const createActivityBooking = (
  payload: ICreateAdditionalActivityBooking,
): Promise<IAdditionalActivityResponse> => {
  return instance
    .post<IAdditionalActivityResponse>(HOTEL_SERVICE + `additional-activity-reservation`, payload)
    .then(({data}) => data);
};

export const updateActivityBooking = (
  payload: ICreateAdditionalActivityBooking,
): Promise<IAdditionalActivityResponse> => {
  return instance
    .put<IAdditionalActivityResponse>(HOTEL_SERVICE + `additional-activity-reservation`, payload)
    .then(({data}) => data);
};

export const getAvailableDatesForEventBookings = (
  monthStartDate: string,
  monthEndDate: string,
  hotelId: number,
  eventId: number,
  activityTime: string,
): Promise<IAdditionalActivityResponse> => {
  return instance
    .get<IAdditionalActivityResponse>(
      HOTEL_SERVICE +
        `available_additional-activity/calender?monthStartDate=${monthStartDate}&monthEndDate=${monthEndDate}&hotelId=${hotelId}&eventId=${eventId}&activityTime=${activityTime}`,
    )
    .then(({data}) => data);
};

export const deleteAdditionalActivity = (id: number): Promise<IAdditionalActivityResponse> => {
  return instance
    .delete<IAdditionalActivityResponse>(HOTEL_SERVICE + `additional-activity/${id}`)
    .then(({data}) => data);
};

export const cancelEvent = (payload: any): Promise<IAdditionalActivityResponse> => {
  return instance
    .put<IAdditionalActivityResponse>(HOTEL_SERVICE + `additional-activity-cancel`, payload)
    .then(({data}) => data);
};

export const updateEventDiscount = (payload: IEventDiscountPayload): Promise<IAdditionalActivityResponse> =>
  instance
    .put<IAdditionalActivityResponse>(HOTEL_SERVICE + `additional-activity-discount`, payload)
    .then(({data}) => data);

export const updateEventExtraHour = (payload: IEventDiscountPayload): Promise<IAdditionalActivityResponse> =>
  instance
    .put<IAdditionalActivityResponse>(HOTEL_SERVICE + `additional-activity-extra-hours`, payload)
    .then(({data}) => data);

export const viewEventAttachmentData = (reservationId: number): Promise<IAdditionalActivityResponse> =>
  instance
    .get<IAdditionalActivityResponse>(
      HOTEL_SERVICE + `additional-activity-reservation/download-attachment/${reservationId}`,
    )
    .then(({data}) => data);

export const deleteEventAttachment = (attachmentId: number, fileType: any): Promise<IAdditionalActivityResponse> =>
  instance
    .delete<IAdditionalActivityResponse>(
      HOTEL_SERVICE + `additional-activity-reservation/delete-attachment?value=${fileType}&id=${attachmentId}`,
      fileType,
    )
    .then(({data}) => data);
export interface FilterBookingSearchProps {
  refNumber: string;
  ownerName: string;
}
export interface FilterHistorySearchProps {
  refNumber: string;
  ownerName: string;
}
