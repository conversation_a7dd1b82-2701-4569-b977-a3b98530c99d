import styled from 'styled-components';
import {BASE_COLORS} from '@app/styles/themes/constants';

export const GridContainer = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4px;
  width: 100%;
  font-family: 'Inter', serif;
  background-color: '#DCDCDC';

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
  }
`;

export const LeftContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
`;

export const countButton = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
  height: 1.5rem;
  width: 1.5rem;
  border-radius: 50%;
  background-color: #f5be51;
  color: white;
  margin: 3px;
`;

export const LeftTop = styled.div`
  /* background-color: #ffffff; */
  /* padding: 6px 16px; */
  border-radius: 16px;
  width: 100%;
  overflow: hidden;
`;

export const LeftBottom = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  border-radius: 8px;
  width: 100%;
`;

export const RightContainer = styled.div`
  /* background-color: #ffffff; */
  padding-top: 25px;
  padding-bottom: 30px;
  /* padding-left: 20px; */
  /* padding-right: 20px; */
  border-radius: 16px;
  width: 100%;
  font-family: 'Poppins', serif;

  @media (min-width: 992px) {
    position: fixed;
    right: 8;
    top: 20;
    height: 90vh;
    width: calc(33.33% - 30px);
    overflow-y: scroll;
    padding: 10px 0;

    &::-webkit-scrollbar {
      width: 0px;
      background: transparent;
    }

    scrollbar-width: none;
  }
`;

export const StyledButtonTop = styled.div`
  font-size: 0.8rem;
  padding: 5px 12px;
  border: 0.2px solid #009432;
  border-radius: 20px;
  cursor: pointer;
  margin: 6px;
  transition: background-color 0.3s ease;

  /* &:hover {
    background-color: #45a049;
    color: white;
  }

  &:active {
    background-color: #3e8e41;
    color: white;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  } */
`;

export const StyledButtonBottom = styled.button`
  /* background-color: '#ffffff'; */
  /* color: black; */
  font-size: 0.7rem;
  padding: 6px 18px;
  border: 0.2px solid #009432;
  border-radius: 20px;
  cursor: pointer;
  margin: 6px;
  transition: background-color 0.3s ease;

  /* &:hover {
    background-color: #45a049;
    color: white;
  }

  &:active {
    background-color: #3e8e41;
    color: white;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  } */
`;
