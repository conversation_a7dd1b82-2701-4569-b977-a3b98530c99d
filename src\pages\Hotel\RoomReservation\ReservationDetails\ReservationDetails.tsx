/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {useEffect, useRef, useState} from 'react';
import * as S from './ReservationDetails.style';
import {Col, Divider, Row} from 'antd';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {Input} from '@app/components/common/inputs/Input/Input.styles';
import {Radio, RadioGroup} from '@app/components/common/Radio/Radio';
import {IconPickerItem} from 'react-fa-icon-picker';
import {Option, Select} from '@app/components/common/selects/Select/Select';
import {ArrivalTimeRange} from '../JSON/data';
import {Button} from '@app/components/common/buttons/Button/Button';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {setCurrent, setSelectedRooms} from '@app/store/slices/reservationSlice';
import {
  calculateTotalNights,
  commonNames,
  convertNumberFormatWithDecimal,
  formatDate,
  roundNumberWithDecimal,
} from '@app/utils/utils';
import {Popconfirm} from '@app/components/common/Popconfirm/Popconfirm';
import {getAllGuests, searchedExactVatUsersApi, searchedVatUsersApi} from '@app/api/hotel/guest/guest.api';
import {
  calculateTotalTaxWithSequence,
  checkVATAvailability,
  getCountries,
  validateEmail,
  validateEmailWithoutRequired,
} from '@app/utils/functions';
import {BASE_COLORS, FONT_SIZE} from '@app/styles/themes/constants';
import {StayTypeTitle} from '@app/components/common/StayTypeTitle/StayTypeTitle';
import {filter, find, isEmpty, remove} from 'lodash';
import {BsCashStack} from 'react-icons/bs';
import {AutoComplete} from '@app/components/common/AutoComplete/AutoComplete';
import {TextArea} from '@app/components/common/inputs/Input/Input';
import {ReactI18NextChild, useTranslation} from 'react-i18next';
import {
  cancelBlockedReservation,
  cancelBlockedReservedRoom,
  getCreditNote,
  updateRoom,
} from '@app/api/hotel/reservation/reservation.api';
import {notificationController} from '@app/controllers/notificationController';
import {MdOutlineDiscount} from 'react-icons/md';
import {StayTypeExtraChild} from '@app/components/common/StayTypeTitle/StayTypeExtraChild';
import DiscountUpdateModalContent from './DiscountUpdateModalContent';
import {HOTEL_SERVICE_MODULE_NAME, modulePermission} from '@app/utils/permissions';
import {useMediaQuery} from 'react-responsive';
import {useCallbackPrompt} from '@app/hooks/useCallbackPrompt';
import DialogBox from '@app/components/common/DialogBox/DialogBox';
import {Checkbox} from '@app/components/common/Checkbox/Checkbox';
import {BankOutlined, CheckOutlined, CloseOutlined, EditOutlined} from '@ant-design/icons';
import ChangeRoomModal from './ChangeRoom';
import {IChangeRoomPayload, IExtraChildDetails, UpdatedRoomDetails} from '../interface/interface';
import Tooltip from '@app/components/common/Tooltip/Tooltip';
import {Switch} from '@app/components/common/Switch/Switch';

const DISCOUNT_OPTION = {
  AMOUNT: 'AMOUNT',
  PERCENTAGE: 'PERCENTAGE',
};

export default function ReservationDetails() {
  const {current, selectedRooms} = useAppSelector(state => state.reservationSlice);
  const {hotelId, groupId} = useAppSelector(state => state.hotelSlice.hotelConfig);
  const [form] = BaseForm.useForm();
  const [discountForm] = BaseForm.useForm();
  const dispatch = useAppDispatch();

  const [isMainGuest, setisMainGuest] = useState<number>(1);
  const [isLoading, setIsLoading] = useState(false);
  const [values, setValues]: any = useState({});
  const [data, setData] = useState([]);
  const [country, setCountry] = useState<any[]>();
  const [isExistMain, setisExistMain] = useState(false);
  const [mainguestData, setMainguestData]: any = useState({
    firstName: null,
    lastName: null,
    email: null,
    countryId: null,
    idNumber: null,
    phoneNumber: null,
  });
  const [otherguestData, setotherguestData]: any = useState([]);
  const [extraChildCountInRooms, setextraChildCountInRooms]: any = useState({});
  const [selectedStayIndex, setselectedStayIndex]: any = useState(undefined);
  const [isVisible, setisVisible] = useState(false);
  const [loadCancellingReservation, setloadCancellingReservation] = useState(false);
  const [mainGuestNic, setMainGuestNic] = useState('');
  const [roomGuestNics, setRoomGuestNics] = useState([]);
  const [isExistingGuest, setisExistingGuest]: any = useState([]);
  const [isVisibleAvailableRoomModal, setisVisibleAvailableRoomModal] = useState(false);
  const [selectedReservedRoomData, setselectedReservedRoomData]: any = useState({});
  const [changeRoomLoading, setchangeRoomLoading] = useState(false);
  const [extraChildDetails, setExtraChildDetails] = useState<IExtraChildDetails[]>([]);
  const [selectedExtraChildCount, setSelectedExtraChildCount]: any = useState();
  const {t} = useTranslation();
  const [mainGuestCheckboxes, setMainGuestCheckboxes]: any = useState({
    toSendMail: true,
    toAttachedPersonDetails: true,
  });
  const [roomGuestCheckboxes, setRoomGuestCheckboxes]: any = useState([]);
  const [isVatApplicable, setisVatApplicable] = useState(true);
  const [isVatDetailsAvailable, setisVatDetailsAvailable] = useState(false);
  const [searchedVatUsers, setsearchedVatUsers] = useState([]);
  const [isExistingVatUser, setisExistingVatUser] = useState(false);
  const [vatUserFormData, setvatUserFormData]: any = useState({
    vatPersonName: null,
    vatNumber: null,
    vatPersonEmail: null,
    vatPersonAddress: null,
  });
  const [bookingForSomeone, setBookingForSomeone] = useState(1);
  const [balanceAmounts, setBalanceAmounts]: any = useState([]);

  const handleRoomGuestCheckboxChange = (index: number, name: string, checked: boolean) => {
    setRoomGuestCheckboxes((prevState: any) => {
      const updatedCheckboxes: any = [...prevState];
      updatedCheckboxes.forEach((checkbox: any, idx: number) => {
        if (idx !== index) {
          if (!updatedCheckboxes[idx]) {
            updatedCheckboxes[idx] = {};
          }
          updatedCheckboxes[idx][name] = false;
        }
      });

      updatedCheckboxes[index] = {
        ...updatedCheckboxes[index],
        [name]: checked,
      };
      let updatedState;
      if (checked) {
        updatedState = {
          ...prevState,
          [name]: false,
        };
      }
      handleClickSendMailCheckBox(updatedState, updatedCheckboxes);
      return updatedCheckboxes;
    });

    if (checked) {
      setMainGuestCheckboxes((prevState: any) => ({
        ...prevState,
        [name]: false,
      }));
    }
  };

  const handleMainGuestCheckboxChange = (name: string, checked: boolean) => {
    setMainGuestCheckboxes((prevState: any) => {
      const updatedState = {
        ...prevState,
        [name]: checked,
      };
      handleClickSendMailCheckBox(updatedState, roomGuestCheckboxes);
      return updatedState;
    });

    if (checked) {
      setRoomGuestCheckboxes((prevState: any) =>
        prevState.map((checkboxes: any, index: any) => ({
          ...checkboxes,
          [name]: false,
        })),
      );
    }
  };

  const [selectedExtraChildren, setSelectedExtraChildren]: any = useState([]);
  const userPermission = useAppSelector(state => state.user.permissions);
  const permissions = modulePermission(userPermission, HOTEL_SERVICE_MODULE_NAME.DISCOUNT);

  const [showDialog, setShowDialog] = useState<boolean>(false);

  const [showPrompt, confirmNavigation, cancelNavigation] = useCallbackPrompt(showDialog);

  const currency = selectedRooms && selectedRooms.stayTypes[0].roomDetails.priceType;
  const taxList = selectedRooms && selectedRooms.stayTypes[0].roomDetails.taxList;
  const isVatActive = checkVATAvailability(taxList);

  // Media Queries
  const isTabletOrMobile = useMediaQuery({query: '(max-width: 1224px)'});

  useEffect(() => {
    setShowDialog(true);
  }, []);

  const handleNumberOfGuestsChange = (value: string, index: number) => {
    const adultCount = value && value.split('_')?.[1];
    const stayTypeIndex = index;

    const currentGuestNumber = values[`guestNumber_${stayTypeIndex}`];
    const newGuestNumber = Number(adultCount);

    const updatedValues = {...values};

    if (newGuestNumber > currentGuestNumber) {
      for (let i = currentGuestNumber + 1; i <= newGuestNumber; i++) {
        updatedValues[`idNumber_${stayTypeIndex}_${i}`] = '';
        updatedValues[`firstName_${stayTypeIndex}_${i}`] = '';
        // updatedValues[`isSave_${stayTypeIndex}_${i}`] = 1;
      }
    } else if (newGuestNumber < currentGuestNumber) {
      for (let i = currentGuestNumber; i > newGuestNumber; i--) {
        delete updatedValues[`idNumber_${stayTypeIndex}_${i}`];
        delete updatedValues[`firstName_${stayTypeIndex}_${i}`];
        // delete updatedValues[`isSave_${stayTypeIndex}_${i}`];
      }
    }

    updatedValues[`guestNumber_${stayTypeIndex}`] = newGuestNumber;
    setValues(updatedValues);
  };

  const handleSubmit = async (values: any) => {
    setIsLoading(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const {checkedIn, checkedOut, stayTypes} = selectedRooms || {};
      const vatRegistry = {
        email: values?.vatPersonEmail,
        registryType: ['RESERVATION'],
        hotelId: hotelId,
        vatNumber: values?.vatNumber,
        onlyThisHotelView: false,
        name: values?.vatPersonName,
        address: values?.vatPersonAddress,
      };
      if (stayTypes) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const finalData: any = stayTypes.map((stay, idx) => {
          // const numberOfGuests = values[`guestNumber_${idx}`];
          const adultCount = values[`guestNumber_${idx}`] && values[`guestNumber_${idx}`].split('_')?.[1];
          const childCount = values[`guestNumber_${idx}`] && values[`guestNumber_${idx}`].split('_')?.[3];

          const guestDetails = Array.from({length: adultCount - 1}, (_, index) => index + 1).map(guestIndex => ({
            firstName: values[`firstName_${idx}_${guestIndex}`],
            lastName: values[`lastName${idx}_${guestIndex}`],
            idNumber: values[`idNumber_${idx}_${guestIndex}`],
            nicNumber: true,
            lastVisit: selectedRooms?.checkedIn,
            applicable: false,
          }));

          return {
            ...stay,
            mainGuest: {
              firstName: values?.firstName,
              lastName: values?.lastName,
              idNumber: values?.idNumber,
              nicNumber: true,
              email: values?.email,
              phoneNumber: values?.phoneNumber,
              countryId: values?.countryId,
              applicable: values?.isSave === 1 ? true : false,
              emailGuest: mainGuestCheckboxes.toSendMail,
              detailGuest: mainGuestCheckboxes.toAttachedPersonDetails,
              // mainGuestId: guestId,
            },
            arrivalTime: values?.arrivalTime,
            reservationExpiredDate: values?.reservationExpiredDate,
            reservationExpiredTime: values?.reservationExpiredTime,
            paymentRequest: values?.paymentRequest,

            roomDetails: {
              ...stay.roomDetails,
              noOfAdults: adultCount,
              noOfChildren: childCount,
              extraChildCount: Number(values[`noExtraChildren_${idx}`]),
              mainGuest: {
                stayType: stay.roomDetails.stayType,
                stayTypeId: stay.roomDetails.stayTypeId,
                guestNic: values[`guestNic_${idx}`],
                guestName: values[`guestName_${idx}`],
                guestLastName: values[`guestLastName_${idx}`],
                email: values[`email_${idx}`],
                guestVerification: values[`guest_${idx}`],
                phoneNumber: values[`phoneNumber_${idx}`],
                lastVisit: selectedRooms?.checkedIn,
                applicable: false,
                emailGuest:
                  roomGuestCheckboxes[idx]?.toSendMail === undefined ? false : roomGuestCheckboxes[idx]?.toSendMail,
                detailGuest:
                  roomGuestCheckboxes[idx]?.toAttachedPersonDetails === undefined
                    ? false
                    : roomGuestCheckboxes[idx]?.toAttachedPersonDetails,
              },
              otherGuests: guestDetails,
              // roomId: values[`roomNumber_${idx}`],
            },
            internalRemarks: values?.internalRemarks,
          };
        });

        await dispatch(
          //@ts-ignore
          setSelectedRooms({
            checkedIn,
            checkedOut,
            stayTypes: finalData,
            selectedRoomTypes: selectedRooms.selectedRoomTypes,
            extraChild: selectedRooms.extraChild,
            channelId: selectedRooms.channelId,
            expiredTime: selectedRooms.expiredTime,
            expiredDate: selectedRooms.expiredDate,
            id: selectedRooms.id,
            extraChildCountInRooms: extraChildCountInRooms,
            vatRegistry: vatRegistry,
            isVatApplicable: isVatApplicable,
            isVatDetailsApplicable: isVatDetailsAvailable,
            searchAdultCount: selectedRooms.searchAdultCount,
            searchChildCount: selectedRooms.searchChildCount,
            selectedCreditBalances: selectedBalances,
            allCreditBalanceReponse: balanceAmounts,
          }),
        );
      }

      await dispatch(setCurrent(current + 1));
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleFirstNameChange = (event: {target: {value: any}}) => {
    const {value} = event.target;
    if (isMainGuest === 1) {
      form.setFieldValue('guestName_0', value);
    }
  };

  const handleEmailChange = (event: {target: {value: any}}) => {
    const {value} = event.target;
    if (isMainGuest === 1) {
      form.setFieldValue('email_0', value);
    }
  };

  const handleLastNameChange = (event: {target: {value: any}}) => {
    const {value} = event.target;
    if (isMainGuest === 1) {
      form.setFieldValue('guestLastName_0', value);
    }
  };

  const handleIDNumberChange = (event: {target: {value: any}}) => {
    const {value} = event.target;
    if (isMainGuest === 1) {
      form.setFieldValue('guestNic_0', value);
    }
  };

  const calculateTotalPrice = (): number => {
    let totalPrice = 0;
    const numberOfNights = calculateTotalNights(
      selectedRooms && selectedRooms.checkedIn,
      selectedRooms && selectedRooms.checkedOut,
    );

    if (selectedRooms && selectedRooms.stayTypes) {
      const payableRooms = selectedRooms.stayTypes.filter(reserveRoom => {
        const {roomDetails} = reserveRoom;
        return roomDetails.payable === true;
      });

      const roomsWithDiscount: any = selectedRooms.stayTypes.filter(reserveRoom => {
        const {roomDetails} = reserveRoom;
        return roomDetails.discountAmount > 0;
      });

      totalPrice = payableRooms.reduce((total, reserveRoom) => {
        const {roomDetails} = reserveRoom;

        const roomPricewithExtraChild =
          roomDetails.roomPrice + (isNaN(roomDetails.totalExtraChildAmount) ? 0 : roomDetails.totalExtraChildAmount);

        const roomPriceNumber = parseFloat(roomPricewithExtraChild);
        const discountAmount = parseFloat(roomDetails.discountAmount) || 0;
        const discountedPrice = Math.max(roomPriceNumber - discountAmount, 0);
        return (totalPrice += discountedPrice);
      }, 0);

      // const payableSelectedRooms = {
      //   ...selectedRooms,
      //   stayTypes: selectedRooms.stayTypes.filter(x => x.roomDetails.payable),
      // };

      // console.log({payableSelectedRooms});

      // if (payableRooms.length > 0) {
      //   const payableRoomKeys = payableRooms.map(room => room.roomDetails.key);
      //   const filteredExtraChild = selectedRooms.extraChild.filter(extraChild =>
      //     payableRoomKeys.includes(extraChild.key),
      //   );
      //   console.log({filteredExtraChild});

      //   const extraChildPriceSum = filteredExtraChild.reduce(
      //     (total, extraChild) => total + extraChild.extraChildPrice,
      //     0,
      //   );

      //   for (const stayTypeIndex in extraChildCountInRooms) {
      //     if (extraChildCountInRooms.hasOwnProperty(stayTypeIndex)) {
      //       const discountRoomAtIndex = roomsWithDiscount[stayTypeIndex];
      //       if (discountRoomAtIndex) {
      //         const discountRoomIndex = discountRoomAtIndex.roomDetails.roomKey;
      //         const extraChildCount = extraChildCountInRooms[discountRoomIndex];
      //         const extraChildPrice = Number(
      //           discountRoomAtIndex.roomDetails.priceType === 'LKR'
      //             ? discountRoomAtIndex.roomDetails.lkrExtraPrice
      //             : discountRoomAtIndex.roomDetails.usdExtraPrice,
      //         );
      //         const extraChildDiscount = Number(extraChildCount) * extraChildPrice * numberOfNights;
      //         totalPrice -= extraChildDiscount;
      //       }
      //     }
      //   }
      //   totalPrice += extraChildPriceSum;
      // }
    }
    totalPrice = Math.max(totalPrice, 0);
    return totalPrice;
  };

  const calculateDiscountPrice = (): number => {
    let totalPrice = 0;

    if (selectedRooms && selectedRooms.stayTypes) {
      const payableRooms = selectedRooms.stayTypes.filter(reserveRoom => {
        const {roomDetails} = reserveRoom;
        return roomDetails;
      });

      totalPrice = payableRooms.reduce((total, reserveRoom) => {
        const {roomDetails}: any = reserveRoom;
        const discountAmount = parseFloat(roomDetails.discountAmount || 0);
        return (totalPrice += discountAmount);
      }, 0);
    }
    totalPrice = Math.max(totalPrice, 0);
    return totalPrice;
  };

  const taxDetails = calculateTotalTaxWithSequence(
    Number(roundNumberWithDecimal(calculateTotalPrice(), 2)),
    taxList,
    isVatApplicable,
  );
  const taxArray = taxDetails?.taxArray;
  const taxAmount = taxDetails?.totalTaxAmount;

  const handleBack = async (reservationId: number) => {
    try {
      const response = await cancelBlockedReservation(reservationId);
      if (response.statusCode === '20000') {
        dispatch(setCurrent(current - 1));
      } else {
        notificationController.error({message: response.message});
      }
    } catch (error) {}
  };

  const handleSearch = async (newValue: string, searchField: string) => {
    let searchParams: Record<string, any> = {};
    const guestTypes = ['RESERVATION,BANQUET'];

    if (searchField === 'FIRST_NAME') {
      searchParams = {firstName: newValue};
    } else if (searchField === 'LAST_NAME') {
      searchParams = {lastName: newValue};
    } else if (searchField === 'EMAIL') {
      searchParams = {email: newValue};
    }
    const result = await getAllGuests(hotelId, searchParams, 20, 0, guestTypes);

    const data = result?.result?.Guests?.map(
      (item: {
        id: number;
        idNumber: number;
        firstName: string;
        email: string;
        phoneNumber: string;
        lastName: string;
        countryId: number;
        applicable: boolean;
      }) => ({
        value: item.id,
        label:
          searchField === 'EMAIL'
            ? item.email
            : `${item.firstName} ${item.lastName} ${item.idNumber !== null ? `-${item.idNumber}` : ''}`,
        idNumber: item.idNumber,
        firstName: item.firstName,
        phoneNumber: item.phoneNumber,
        email: item.email,
        lastName: item.lastName,
        countryId: item.countryId,
        applicable: item.applicable,
      }),
    );
    setData(data);
  };

  const handlePopulateMainguest = (guestData: {
    value?: number;
    idNumber?: string;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    email?: string;
    countryId?: number;
    applicable?: boolean;
  }) => {
    const {idNumber, firstName, lastName, phoneNumber, email, countryId, applicable, value} = guestData;
    handleMainGuestNicChange(idNumber);
    fetchCreditNoteForGuest(value);
    if (isMainGuest === 1) {
      form.setFieldValue('guestName_0', firstName);
      form.setFieldValue('guestLastName_0', lastName);
      form.setFieldValue('guestNic_0', idNumber);
      form.setFieldValue('email_0', email);
      form.setFieldValue('phoneNumber_0', phoneNumber);

      const convertData = {
        [`guestNic_0`]: idNumber,
        [`guestName_0`]: firstName,
        [`guestLastName_0`]: lastName,
        [`email_0`]: email,
        [`phoneNumber_0`]: phoneNumber,
      };

      setotherguestData((prevState: any) => ({
        ...prevState,
        [0]: convertData,
      }));
    }
    form.setFieldsValue({
      firstName,
      lastName,
      phoneNumber,
      email,
      countryId,
      idNumber,
      isSave: applicable ? 1 : 0,
    });
    setMainguestData(guestData);
    setData([]);
    setisExistMain(true);
  };

  const formRef = useRef(null);

  const fetchCreditNoteForGuest = async (guestId: any) => {
    try {
      const creditNoteData = await getCreditNote(guestId, selectedRooms.isResident, hotelId);
      setBalanceAmounts(creditNoteData.result.invoiceCreditNote);
      setSelectedBalances([creditNoteData.result.invoiceCreditNote[0].id]);
    } catch (error) {
      console.error('Error fetching credit notes:', error);
    }
  };

  // Will be maitain as comment sometimes needs to revert this function

  // const handleBalanceSelect = (balance: number) => {
  //   if (selectedBalance === balance) {
  //     // Unselect if the same button is clicked
  //     setSelectedBalance(null);
  //     form.setFieldsValue({balanceAmount: undefined});
  //   } else {
  //     // Select the new balance
  //     setSelectedBalance(balance);
  //     form.setFieldsValue({balanceAmount: balance});
  //   }
  // };

  useEffect(() => {
    if (form) {
      form.setFieldValue('isSave', 1);
    }
  }, [form, selectedRooms]);

  const handleChangeDependedGuest = () => {
    const isAlreadyDepended = form.getFieldValue('bookingFor');
    if (isAlreadyDepended === 1) form.setFieldValue('bookingFor', 2);
  };

  const onRemove = async (stayTypeIndex: number) => {
    const data = [...selectedRooms.stayTypes];
    const findObj = data[stayTypeIndex];
    try {
      const response = await cancelBlockedReservedRoom(findObj.roomDetails.id);
      if (response.statusCode === '20000') {
        notificationController.success({message: 'Room removed successfully!'});
        remove(data, (x, idx) => stayTypeIndex === idx);
        const updatedStayTypes = data.map(stayType => {
          if (stayType.roomDetails.stayTypeId === findObj.roomDetails.stayTypeId) {
            return {
              ...stayType,
              roomCount: stayType.roomCount - 1,
            };
          } else {
            return stayType;
          }
        });

        const updatedStayTypesWithoutZeroCount = updatedStayTypes.filter(stayType => stayType.roomCount > 0);
        const updatedStayTypesWithDiscount = updatedStayTypesWithoutZeroCount.map(stayType => ({
          ...stayType,
          roomDetails: {
            ...stayType.roomDetails,
            discountOption: 'AMOUNT',
            discountAmount: 0,
          },
        }));

        const updatedSelectedRooms = {
          ...selectedRooms,
          stayTypes: updatedStayTypesWithDiscount,
        };
        dispatch(setSelectedRooms(updatedSelectedRooms));
        form.resetFields();
      } else {
        notificationController.error({message: response.message});
      }
    } catch (error) {}
  };

  const handleChangeExisting = (event: {target: {id: any; value: any}}, idx: number) => {
    const changedFieldName = event.target.id;

    const fields = [
      `guestName_${idx}`,
      `guestLastName_${idx}`,
      `email_${idx}`,
      `guestNic_${idx}`,
      `guestNumber_${idx}`,
    ];

    const fieldForReset = fields.filter(o => o !== changedFieldName);
    if (otherguestData[idx] && otherguestData[idx][changedFieldName] !== null) {
      form.resetFields(fieldForReset);
      const updatedGuestData = {...otherguestData};
      delete updatedGuestData[idx];
      setotherguestData(updatedGuestData);
    }

    setisExistingGuest((prevState: any) => ({
      ...prevState,
      [idx]: false,
    }));
  };

  const onChangeMainGuest = (changedFieldName: string) => {
    const fields = ['firstName', 'lastName', 'email', 'countryId', 'idNumber', 'phoneNumber'];
    const fieldForReset = fields.filter(o => o !== changedFieldName);
    if (mainguestData[changedFieldName] !== null) {
      form.resetFields(fieldForReset);
      setMainguestData({
        firstName: null,
        lastName: null,
        email: null,
        countryId: null,
        idNumber: null,
        phoneNumber: null,
      });
      if (isMainGuest === 1) {
        const fields = [`guestName_0`, `guestLastName_0`, `email_0`, `guestNic_0`];
        form.resetFields(fields);
      }
    }
    setBalanceAmounts([]);
    setSelectedBalances([]);
    setisExistMain(false);
  };

  const cancelBlockedReservationDetails = async (reservationId: number, isConfirmNavigation: boolean) => {
    try {
      setloadCancellingReservation(true);
      const response = await cancelBlockedReservation(reservationId);
      if (response.statusCode === '20000') {
        setloadCancellingReservation(false);
        //@ts-ignore
        isConfirmNavigation && confirmNavigation();
        notificationController.success({message: 'Your Booking has been cancelled'});
        dispatch(setCurrent(0));
      } else {
        notificationController.error({message: response.message});
        setloadCancellingReservation(false);
      }
    } catch (error) {
      setloadCancellingReservation(false);
    }
  };

  const handleSelectExtraChild = (value: any, stayTypeIndex: number, stayTypeId: number) => {
    const data = {
      stayTypeId: stayTypeId,
      roomIndex: stayTypeIndex,
      count: Number(value),
    };

    const existingRecordIndex = selectedExtraChildren.findIndex(
      (item: {stayTypeId: number; roomIndex: number}) =>
        item.stayTypeId === stayTypeId && item.roomIndex === stayTypeIndex,
    );

    if (existingRecordIndex !== -1) {
      const updatedData = [...selectedExtraChildren];
      updatedData[existingRecordIndex] = {
        ...updatedData[existingRecordIndex],
        count: Number(value),
      };
      setSelectedExtraChildren(updatedData);
    } else {
      const defaultData = [...selectedExtraChildren, data];
      setSelectedExtraChildren(defaultData);
    }

    const pickedRoom = selectedRooms.stayTypes[stayTypeIndex].roomDetails;
    let extraChildDiscount = 0;

    const roomAtIndex = selectedRooms.stayTypes[stayTypeIndex];
    const numberOfNights = calculateTotalNights(
      selectedRooms && selectedRooms.checkedIn,
      selectedRooms && selectedRooms.checkedOut,
    );
    if (roomAtIndex) {
      const extraChildPrice = Number(
        roomAtIndex.roomDetails.priceType === 'LKR'
          ? roomAtIndex.roomDetails.lkrExtraPrice
          : roomAtIndex.roomDetails.usdExtraPrice,
      );

      extraChildDiscount += value * extraChildPrice * numberOfNights;
    }

    const extraChildAmount = isNaN(extraChildDiscount) ? 0 : extraChildDiscount;
    const roomPricewithExtraChild = Number(pickedRoom.roomPrice) + extraChildAmount;
    const updatedSelectedRooms = JSON.parse(JSON.stringify(selectedRooms));
    updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.validateRoomPrice = roomPricewithExtraChild;
    dispatch(setSelectedRooms(updatedSelectedRooms));

    if (pickedRoom.discountAmount > 0 && pickedRoom.payable) {
      const roomDiscount =
        pickedRoom.discountOption === DISCOUNT_OPTION.PERCENTAGE
          ? (roomPricewithExtraChild * pickedRoom.enteredDiscountValue) / 100
          : pickedRoom.enteredDiscountValue;

      const calculatedDiscount = Number(roomDiscount);
      const updatedSelectedRooms = JSON.parse(JSON.stringify(selectedRooms));
      updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.discountOption = pickedRoom.discountOption;
      updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.discountAmount = calculatedDiscount;
      updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.payable = true;
      updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.roomPricewithExtraChild = roomPricewithExtraChild;
      dispatch(setSelectedRooms(updatedSelectedRooms));
    } else if (pickedRoom.discountAmount > 0 && !pickedRoom.payable) {
      let extraChildDiscount = 0;

      const roomAtIndex = selectedRooms.stayTypes[stayTypeIndex];
      if (roomAtIndex) {
        const extraChildPrice = Number(
          roomAtIndex.roomDetails.priceType === 'LKR'
            ? roomAtIndex.roomDetails.lkrExtraPrice
            : roomAtIndex.roomDetails.usdExtraPrice,
        );
        extraChildDiscount += value * extraChildPrice * numberOfNights;
      }

      const calculatedDiscount = pickedRoom.roomPrice + extraChildDiscount;

      const updatedSelectedRooms = JSON.parse(JSON.stringify(selectedRooms));
      updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.discountOption = pickedRoom.discountOption;
      updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.discountAmount = calculatedDiscount;
      updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.payable = false;
      updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.roomPricewithExtraChild = calculatedDiscount;

      dispatch(setSelectedRooms(updatedSelectedRooms));
    }
  };

  useEffect(() => {
    (async () => {
      const countries = await getCountries('');
      setCountry(countries);
    })();
  }, []);

  const dumExtraChildCount = (stayid: number, stayIndex: number) => {
    let countValue = 0;

    const filerData = selectedExtraChildren.filter((res: {stayTypeId: number}) => res.stayTypeId === stayid);

    const findObj = find(selectedExtraChildren, x => x.stayTypeId === stayid && x.roomIndex === stayIndex);
    const roomCount = findObj !== undefined ? findObj.count : 0;
    const filterNotZero = filter(filerData, item => item.count > 0);
    filerData.map((res2: {count: number}) => {
      countValue = countValue + res2.count;
    });

    if (filterNotZero.length === 1) {
      if (filterNotZero[0].roomIndex === stayIndex && filterNotZero[0].stayTypeId === stayid) {
        return 0;
      } else {
        return countValue;
      }
    } else {
      return countValue - roomCount;
    }
  };

  const getChildCount = (extraChildCount: any, stayIndex: number, stayid: number) => {
    return extraChildCount - dumExtraChildCount(stayid, stayIndex);
  };

  // const handleRoomNumberChange = (value: unknown, stayTypeIndex: number) => {
  //   setSelectedRoomNumbers(prevState => {
  //     // eslint-disable-next-line @typescript-eslint/no-explicit-any
  //     const updatedSelectedRoomNumbers: any = [...prevState];
  //     updatedSelectedRoomNumbers[stayTypeIndex] = value;
  //     if (value === null) {
  //       const clearedRoomNumber = prevState[stayTypeIndex];
  //       if (clearedRoomNumber) {
  //         updatedSelectedRoomNumbers.forEach((roomNumber: string, index: number) => {
  //           if (index !== stayTypeIndex && roomNumber === null) {
  //             updatedSelectedRoomNumbers[index] = clearedRoomNumber;
  //           }
  //         });
  //       }
  //     }

  //     return updatedSelectedRoomNumbers;
  //   });
  // };

  // const getAvailableRoomOptions = (stayTypeIndex: number) => {
  //   const selectedRoomsCopy = {...selectedRooms};
  //   // eslint-disable-next-line @typescript-eslint/no-explicit-any
  //   const selectedRoomNumbersForOtherStayTypes: any = selectedRoomNumbers.filter((_, index) => index !== stayTypeIndex);

  //   const availableRoomList = selectedRoomsCopy.stayTypes[stayTypeIndex]?.roomDetails?.roomList.filter(
  //     room => !selectedRoomNumbersForOtherStayTypes.includes(room.roomId),
  //   );

  //   if (!availableRoomList) {
  //     return null;
  //   }

  //   return availableRoomList.map(room => (
  //     <Option key={room.roomId} value={room.roomId}>
  //       {`${room.roomNumber} - ${room.roomName}`}
  //     </Option>
  //   ));
  // };

  const handleMainGuestNicChange = (value: any) => {
    setMainGuestNic(value);
  };

  const handleRoomGuestNicChange = (value: string, index: number) => {
    const newRoomGuestNics: any = [...roomGuestNics];
    newRoomGuestNics[index] = value;
    setRoomGuestNics(newRoomGuestNics);
  };

  const validateUniqueNic = (_: any, value: string) => {
    const allNics = [mainGuestNic, ...roomGuestNics];
    if (isMainGuest === 1 && roomGuestNics.length > 0 && roomGuestNics[0] === mainGuestNic) {
      return Promise.resolve();
    }

    if (allNics.filter(nic => nic === value).length > 1) {
      return Promise.reject(new Error('NIC must be unique'));
    }
    return Promise.resolve();
  };

  const handlePopulateRoomguestDetails = (guestData: any, idx: number) => {
    const {firstName, email, idNumber, lastName, applicable, phoneNumber} = guestData;
    const convertedData = {
      [`guestNic_${idx}`]: guestData.idNumber,
      [`guestName_${idx}`]: guestData.firstName,
      [`guestLastName_${idx}`]: guestData.lastName,
      [`email_${idx}`]: guestData.email,
      [`phoneNumber_${idx}`]: guestData.phoneNumber,
    };
    form.setFieldValue(`guestName_${idx}`, firstName);
    form.setFieldValue(`guestLastName_${idx}`, lastName);
    form.setFieldValue(`email_${idx}`, email);
    form.setFieldValue(`guestNic_${idx}`, idNumber);
    form.setFieldValue(`phoneNumber_${idx}`, phoneNumber);
    form.setFieldValue(`applicable_${idx}`, applicable);
    setotherguestData((prevState: any) => ({
      ...prevState,
      [idx]: convertedData,
    }));
    setData([]);
    setisExistingGuest((prevState: any) => ({
      ...prevState,
      [idx]: true,
    }));
    form.validateFields([`email_${idx}`]);
  };

  const resetAutoCompleteData = () => {
    setData([]);
  };

  const resetVatSearchedData = () => {
    setsearchedVatUsers([]);
  };

  const handleClickEditRoom = (stayType: any, stayTypeIndex: number) => {
    const reservedRoomData = {
      stayTypeId: stayType.roomDetails.stayTypeId,
      checkInDate: selectedRooms.checkedIn,
      checkOutDate: selectedRooms.checkedOut,
      roomId: stayType.roomDetails.roomId,
      reservedRoomId: stayType.roomDetails.id,
      stayTypeIndex: stayTypeIndex,
    };
    setisVisibleAvailableRoomModal(true);
    setselectedReservedRoomData(reservedRoomData);
  };

  const updateSelectedRoomPayload = (updateDetails: UpdatedRoomDetails) => {
    const {
      newRoomId,
      newRoomName,
      newRoomNumber,
      stayTypeIndex,
      newRoomValidatePersonCount,
      newRoomValidateCombination,
    } = updateDetails;

    const updatedRoomDetails = JSON.parse(JSON.stringify(selectedRooms));
    if (updatedRoomDetails.stayTypes && updatedRoomDetails.stayTypes[stayTypeIndex]) {
      const roomDetails = updatedRoomDetails.stayTypes[stayTypeIndex].roomDetails;
      roomDetails.roomId = newRoomId;
      roomDetails.roomNumber = newRoomNumber;
      roomDetails.roomName = newRoomName;
      roomDetails.validatePersonCount = newRoomValidatePersonCount;
      roomDetails.validateCombination = newRoomValidateCombination;
    }
    dispatch(setSelectedRooms(updatedRoomDetails));
  };

  const handleUpdateRoom = async (data: any, roomData: any) => {
    setchangeRoomLoading(true);
    const payload: IChangeRoomPayload = {
      id: roomData.reservedRoomId,
      roomId: data.roomId,
    };
    try {
      const response = await updateRoom(payload);
      const updateDetails: UpdatedRoomDetails = {
        newRoomId: data.roomId,
        newRoomName: data.roomName,
        newRoomNumber: data.roomNumber,
        prevRoomId: roomData.roomId,
        stayTypeId: roomData.stayTypeId,
        stayTypeIndex: roomData.stayTypeIndex,
        newRoomValidatePersonCount: data.validatePersonCount,
        newRoomValidateCombination: data.validateCombination,
      };

      await updateSelectedRoomPayload(updateDetails);
      await setisVisibleAvailableRoomModal(false);
      setchangeRoomLoading(false);
    } catch (error) {
      setchangeRoomLoading(false);
    }
  };

  const handleBlurPopulate = async (value: string, searchField: string) => {
    let searchParams = {};
    if (searchField === 'vatPersonEmail') {
      searchParams = {email: value};
    } else if (searchField === 'vatNumber') {
      searchParams = {vatNumber: value};
    }
    const result = await searchedExactVatUsersApi(groupId, searchParams, 20, 0);
    const data = result?.result?.Guests?.map(
      (item: {id: number; vatNumber: number; address: string; name: string; email: string}) => ({
        id: item.id,
        vatNumber: item.vatNumber,
        address: item.address,
        name: item.name,
        email: item.email,
        value: item.id,
        label: searchField === 'vatPersonEmail' ? item.email : item.vatNumber,
      }),
    );
    if (!isEmpty(data)) handlePopulateVatInformation(data[0]);
  };

  const handleSearchVatUsers = async (value: string, searchField: string) => {
    let searchParams = {};
    if (searchField === 'vatPersonEmail') {
      searchParams = {email: value};
    } else if (searchField === 'vatNumber') {
      searchParams = {vatNumber: value};
    }

    const result = await searchedVatUsersApi(groupId, searchParams, 20, 0);

    const data = result?.result?.Guests?.map(
      (item: {id: number; vatNumber: number; address: string; name: string; email: string}) => ({
        id: item.id,
        vatNumber: item.vatNumber,
        address: item.address,
        name: item.name,
        email: item.email,
        value: item.id,
        label: searchField === 'vatPersonEmail' ? item.email : item.vatNumber,
      }),
    );

    setsearchedVatUsers(data);
  };

  const handlePopulateVatInformation = (vatDetails: any) => {
    const {id, name, address, vatNumber, email} = vatDetails;
    const fieldData = {
      vatPersonName: name,
      vatNumber: vatNumber,
      vatPersonEmail: email,
      vatPersonAddress: address,
    };
    form.setFieldsValue(fieldData);
    setvatUserFormData(fieldData);
    setisExistingVatUser(true);
  };

  const handleChangeVatInput = (changedFieldName: string) => {
    const fields = ['vatPersonName', 'vatNumber', 'vatPersonEmail', 'vatPersonAddress'];
    const fieldsForReset = fields.filter(o => o !== changedFieldName);

    if (isExistingVatUser && vatUserFormData[changedFieldName] !== null) {
      form.resetFields(fieldsForReset);
      setvatUserFormData({
        vatPersonName: null,
        vatNumber: null,
        vatPersonEmail: null,
        vatPersonAddress: null,
      });
      setisExistingVatUser(false);
    }
  };

  const findEmailerMailId = (mainGuestCheckboxes: {toSendMail: any}, roomGuestCheckboxes: {toSendMail: any}[]) => {
    const formValues = form.getFieldsValue();

    if (mainGuestCheckboxes.toSendMail) {
      return form.getFieldValue('email');
    } else {
      const {stayTypes} = selectedRooms || {};
      let result = null;
      stayTypes?.forEach((stayType, index) => {
        if (roomGuestCheckboxes[index]?.toSendMail) {
          result = formValues[`email_${index}`];
        }
      });
      return result;
    }
  };

  const populateVatInformation = async (checked: boolean, mailerEmailId: string) => {
    if (checked && mailerEmailId !== null && mailerEmailId !== undefined) {
      try {
        const response = await searchedVatUsersApi(groupId, {email: mailerEmailId}, 20, 0);
        const guests = response.result.Guests;
        if (guests.length > 0) {
          const {name, address, vatNumber, email} = guests[0];
          const vatDetails = {name, address, vatNumber, email};
          handlePopulateVatInformation(vatDetails);
        } else {
          const vatDetails = {name: null, address: null, vatNumber: null, email: null};
          handlePopulateVatInformation(vatDetails);
        }
      } catch (error) {}
    }
  };

  const handleClickAddVatDetails = async (checked: boolean) => {
    setisVatDetailsAvailable(checked);
    const mailerEmailId = await findEmailerMailId(mainGuestCheckboxes, roomGuestCheckboxes);
    populateVatInformation(checked, mailerEmailId);
    if (!checked) {
      const fieldData = {
        vatPersonName: null,
        vatNumber: null,
        vatPersonEmail: null,
        vatPersonAddress: null,
      };
      form.setFieldsValue(fieldData);
      setvatUserFormData(fieldData);
    }
  };

  const handleClickSendMailCheckBox = async (mainGuestCheckBoxData: any, roomGuestCheckBoxData: any) => {
    const mailerEmailId = await findEmailerMailId(mainGuestCheckBoxData, roomGuestCheckBoxData);
    await populateVatInformation(isVatDetailsAvailable, mailerEmailId);
  };

  const resetDiscountValues = (stayTypeIndex: any, selectedRoomsData: any) => {
    const updatedSelectedRooms = JSON.parse(JSON.stringify(selectedRoomsData));
    updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.discountOption = 'AMOUNT';
    updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.discountAmount = 0;
    updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.enteredDiscountValue = 0;
    updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.discountAmountWithoutExtraChild = 0;
    updatedSelectedRooms.stayTypes[stayTypeIndex].roomDetails.payable = true;
    dispatch(setSelectedRooms(updatedSelectedRooms));
  };

  const handleExtraChildCount = (roomKey: any, extraChildCount: number, roomIndex: number) => {
    const updatedSelectedExtraChildCount = {...selectedExtraChildCount};

    if (!updatedSelectedExtraChildCount[roomIndex]) {
      updatedSelectedExtraChildCount[roomIndex] = [];
    }

    const existingEntryIndex = updatedSelectedExtraChildCount[roomIndex].findIndex((item: any) => item.key === roomKey);
    if (existingEntryIndex !== -1) {
      updatedSelectedExtraChildCount[roomIndex][existingEntryIndex].extraChildCount = extraChildCount;
    } else {
      updatedSelectedExtraChildCount[roomIndex].push({
        key: roomKey,
        extraChildCount: extraChildCount,
      });
    }

    setSelectedExtraChildCount(updatedSelectedExtraChildCount);

    const finalExtraChildCount: any = {};
    for (const index in updatedSelectedExtraChildCount) {
      for (const item of updatedSelectedExtraChildCount[index]) {
        if (finalExtraChildCount[item.key]) {
          finalExtraChildCount[item.key] += item.extraChildCount;
        } else {
          finalExtraChildCount[item.key] = item.extraChildCount;
        }
      }
    }

    let totalExtraChildPrice = 0;
    const numberOfNights = calculateTotalNights(selectedRooms.checkedIn, selectedRooms.checkedOut);

    const updatedExtraChildList = selectedRooms.extraChild.map((item: any) => {
      if (item.key === roomKey) {
        const roomDetails = selectedRooms.stayTypes[roomIndex].roomDetails;
        const extraChildPrice = Number(
          roomDetails.priceType === 'LKR' ? roomDetails.lkrExtraPrice : roomDetails.usdExtraPrice,
        );
        totalExtraChildPrice = finalExtraChildCount[roomKey] * extraChildPrice * numberOfNights;

        return {
          ...item,
          extraChildCount: finalExtraChildCount[roomKey],
          extraChildPrice: totalExtraChildPrice,
        };
      }
      return item;
    });

    if (!updatedExtraChildList.some(item => item.key === roomKey)) {
      const roomDetails = selectedRooms.stayTypes[roomIndex].roomDetails;
      const extraChildPrice = Number(
        roomDetails.priceType === 'LKR' ? roomDetails.lkrExtraPrice : roomDetails.usdExtraPrice,
      );
      totalExtraChildPrice = finalExtraChildCount[roomKey] * extraChildPrice * numberOfNights;

      updatedExtraChildList.push({
        key: roomKey,
        extraChildCount: finalExtraChildCount[roomKey],
        extraChildPrice: totalExtraChildPrice,
        name: roomDetails.stayType,
        numberOfAdult: roomDetails.numberOfAdult,
        numberOfChildren: roomDetails.numberOfChildren,
        roomType: roomDetails.roomType,
        mealType: roomDetails.mealType,
      });
    }

    const extraChildPrice = Number(
      selectedRooms.stayTypes[roomIndex].roomDetails.priceType === 'LKR'
        ? selectedRooms.stayTypes[roomIndex].roomDetails.lkrExtraPrice
        : selectedRooms.stayTypes[roomIndex].roomDetails.usdExtraPrice,
    );

    const totalExtraChildPriceForRoom = extraChildCount * extraChildPrice * numberOfNights;

    const updatedRoomDetails = {
      ...selectedRooms.stayTypes[roomIndex].roomDetails,
      totalExtraChildAmount: Number(roundNumberWithDecimal(totalExtraChildPriceForRoom, 2)),
    };

    const updatedStayTypes = selectedRooms.stayTypes.map((stayType, index) => {
      if (index === roomIndex) {
        return {
          ...stayType,
          roomDetails: updatedRoomDetails,
        };
      }
      return stayType;
    });

    const updatedSelectedRooms = {
      ...selectedRooms,
      stayTypes: updatedStayTypes,
      extraChild: updatedExtraChildList,
    };

    // const updatedSelectedRooms = selectedRooms.stayTypes[roomIndex].roomDetails.validateRoomPriceWithExtraChild = selectedRooms.stayTypes[roomIndex].roomDetails.validateRoomPrice + totalExtraChildPrice

    // const convertedData: ISelectedRooms = {
    //   ...selectedRooms,
    //   extraChild: updatedExtraChildList,
    // };

    resetDiscountValues(roomIndex, updatedSelectedRooms);

    // dispatch(setSelectedRooms(convertedData));
  };

  const [selectedBalances, setSelectedBalances]: any = useState([]);

  // const handleBalanceSelect = (balanceId: any) => {
  //   setSelectedBalances((prevSelected: any[]) => {
  //     if (prevSelected.includes(balanceId)) {
  //       return prevSelected.filter((id: any) => id !== balanceId);
  //     } else {
  //       return [...prevSelected, balanceId];
  //     }
  //   });
  // };

  const calculateTotalDeduction = () => {
    return balanceAmounts
      .filter((balance: any) => selectedBalances.includes(balance.id))
      .reduce((sum: any, balance: any) => sum + balance.amount, 0);
  };

  const calculateGrandTotal = () => {
    const totalPrice = calculateTotalPrice() + taxAmount;
    const totalDeduction = calculateTotalDeduction();
    const adjustedPrice = Math.max(0, totalPrice - totalDeduction);
    return adjustedPrice;
  };

  const creditAmount = balanceAmounts?.find((credit: {id: any}) => selectedBalances[0] === credit.id)?.balanceAmount;

  return (
    <>
      <Row gutter={[20, 20]}>
        <Col md={isTabletOrMobile ? 18 : 8}>
          {isVatActive ? (
            <S.VatCard>
              <S.Title>Applicable VAT</S.Title>
              <div>
                <Switch
                  checkedChildren={<CheckOutlined />}
                  unCheckedChildren={<CloseOutlined />}
                  defaultChecked
                  checked={isVatApplicable}
                  onChange={value => setisVatApplicable(value)}
                />
              </div>
            </S.VatCard>
          ) : null}
          {!isEmpty(selectedBalances) && (
            <S.VatCard style={{marginTop: '15px'}}>
              <S.Title>Credit Note Amount</S.Title>
              <div>
                {' '}
                {currency} {creditAmount}
              </div>
            </S.VatCard>
          )}
          <S.BlurCardWrapper>
            <S.Card>
              <S.Padding>
                <S.Title>{t('commonNames.bookingDeltailsReserve')}</S.Title>
                <S.DateSection>
                  <S.CheckIn>
                    <S.CheckInOutText>Check-in</S.CheckInOutText>
                    <S.DateText>{formatDate(selectedRooms && selectedRooms.checkedIn)}</S.DateText>
                  </S.CheckIn>
                  <S.VerticalLine />
                  <S.CheckOut>
                    <S.CheckInOutText>Check-out</S.CheckInOutText>
                    <S.DateText>{formatDate(selectedRooms && selectedRooms.checkedOut)}</S.DateText>
                  </S.CheckOut>
                </S.DateSection>
                <S.StayNights>
                  <S.RegularText>Total length of stay:</S.RegularText>
                  <S.NightCount>
                    {calculateTotalNights(
                      selectedRooms && selectedRooms.checkedIn,
                      selectedRooms && selectedRooms.checkedOut,
                    )}{' '}
                    {calculateTotalNights(
                      selectedRooms && selectedRooms.checkedIn,
                      selectedRooms && selectedRooms.checkedOut,
                    ) <= 1
                      ? 'Night'
                      : 'Nights'}
                  </S.NightCount>
                </S.StayNights>
                <S.HorizontalLine />
                {/* <S.BoldTitle>You selected:</S.BoldTitle> */}
                {selectedRooms &&
                  selectedRooms?.stayTypes
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    ?.reduce((uniqueStayTypes: any, stayType) => {
                      const stayTypeName = stayType?.roomDetails?.stayType;
                      if (!uniqueStayTypes.includes(stayTypeName)) {
                        uniqueStayTypes.push(stayTypeName);
                      }
                      return uniqueStayTypes;
                    }, [])
                    .map((stayTypeName: string, idx: number) => {
                      const filteredStayTypes = selectedRooms?.stayTypes?.filter(
                        stayType => stayType?.roomDetails?.stayType === stayTypeName,
                      );
                      const roomCount = Number(filteredStayTypes[0]?.roomCount);
                      const roomPrice = Number(filteredStayTypes[0]?.roomDetails?.roomPrice);
                      const totalPrice = roomCount * roomPrice;
                      return (
                        <S.RoomPriceContainer key={idx}>
                          <S.StayWrapper>
                            <S.RoomNameText>{filteredStayTypes[0]?.roomCount} x </S.RoomNameText>&nbsp;
                            <StayTypeTitle
                              adultCount={filteredStayTypes[0].roomDetails.numberOfAdult}
                              childCount={filteredStayTypes[0].roomDetails.numberOfChildren}
                              isBold={false}
                              meal={filteredStayTypes[0].roomDetails.mealType}
                              name={filteredStayTypes[0].roomDetails.roomType}
                              size={FONT_SIZE.xs}
                            />
                          </S.StayWrapper>
                          <S.RoomPriceText>
                            {filteredStayTypes[0]?.roomDetails?.priceType}{' '}
                            {convertNumberFormatWithDecimal(totalPrice, 2)}
                          </S.RoomPriceText>
                        </S.RoomPriceContainer>
                      );
                    })}
                {selectedRooms && selectedRooms?.extraChild?.some(child => child.extraChildCount !== 0) && (
                  <S.ExtraChildTitle>Extra bed summary</S.ExtraChildTitle>
                )}
                {selectedRooms &&
                  selectedRooms?.extraChild?.map((child, idx) => {
                    return (
                      child.extraChildCount !== 0 && (
                        <S.RoomPriceContainer key={idx}>
                          <S.StayWrapper>
                            <S.RoomNameText>{child?.extraChildCount} x</S.RoomNameText>
                            &nbsp;
                            <StayTypeExtraChild
                              adultCount={child.numberOfAdult}
                              childCount={1}
                              isBold={false}
                              meal={child.mealType}
                              name={child.roomType}
                              size={FONT_SIZE.xs}
                            />
                          </S.StayWrapper>
                          <S.RoomPriceText>
                            {selectedRooms?.stayTypes[0].roomDetails.priceType}{' '}
                            {convertNumberFormatWithDecimal(child.extraChildPrice, 2)}
                          </S.RoomPriceText>
                        </S.RoomPriceContainer>
                      )
                    );
                  })}
                <S.ChangeSelectionBtn onClick={() => handleBack(selectedRooms.id)}>
                  <S.ChangeSelection>Change Your Selection</S.ChangeSelection>
                </S.ChangeSelectionBtn>
              </S.Padding>
            </S.Card>
          </S.BlurCardWrapper>
          <br />
          <S.Card>
            <S.Padding>
              <S.Title>{t('reservation.priceSummary')}</S.Title>
            </S.Padding>
            <S.PriceSection>
              <S.PriceWrapper>
                <S.PriceHeader $hasCredit={!isEmpty(selectedBalances)}>Grand Total</S.PriceHeader>
                <S.PriceHeader $crossLine={!isEmpty(selectedBalances)} $hasCredit={!isEmpty(selectedBalances)}>
                  {selectedRooms && selectedRooms.stayTypes[0]?.roomDetails?.priceType}{' '}
                  {convertNumberFormatWithDecimal(calculateTotalPrice() + taxAmount, 2)}
                  {/* {convertNumberFormatWithDecimal(calculateGrandTotal(), 2)} */}
                </S.PriceHeader>
              </S.PriceWrapper>
              {!isEmpty(selectedBalances) && (
                <S.PriceWrapper>
                  <S.PriceHeader $hasCredit={false}>Total Payable</S.PriceHeader>
                  <S.PriceHeader $hasCredit={false}>
                    {selectedRooms && selectedRooms.stayTypes[0]?.roomDetails?.priceType}{' '}
                    {/* {convertNumberFormatWithDecimal(calculateTotalPrice() + taxAmount, 2)} */}
                    {convertNumberFormatWithDecimal(calculateGrandTotal(), 2)}
                  </S.PriceHeader>
                </S.PriceWrapper>
              )}
              <S.TaxWrapper>
                <S.TaxSubHeader>(Include Taxes and Charges)</S.TaxSubHeader>
              </S.TaxWrapper>
            </S.PriceSection>
            {calculateDiscountPrice() > 0 || !isEmpty(taxList) ? (
              <S.Padding>
                <S.Title>{t('reservation.priceBreakdown')}</S.Title>

                <S.ListDiscountRow>
                  <S.DiscountIconWrapper>
                    <BankOutlined color={BASE_COLORS.opacityOne} />
                    <S.TaxInfoText>Room Price</S.TaxInfoText>
                  </S.DiscountIconWrapper>
                  <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                    calculateTotalPrice(),
                    2,
                  )}`}</S.TaxInfoText>
                </S.ListDiscountRow>

                <S.ListDiscountRow>
                  <S.DiscountIconWrapper>
                    <MdOutlineDiscount color={BASE_COLORS.opacityOne} />
                    <S.TaxInfoText>Discount</S.TaxInfoText>
                  </S.DiscountIconWrapper>
                  <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                    calculateDiscountPrice(),
                    2,
                  )}`}</S.TaxInfoText>
                </S.ListDiscountRow>

                {!isEmpty(taxList) ? (
                  <S.TaxInformationWapper>
                    <S.TaxLeftWrapper>
                      <BsCashStack color={BASE_COLORS.opacityOne} />
                    </S.TaxLeftWrapper>
                    <S.TaxRightWrapper>
                      <S.TaxInfoText>{`Excludes ${currency} ${convertNumberFormatWithDecimal(
                        taxAmount,
                        2,
                      )} in taxes and charges`}</S.TaxInfoText>
                      <S.ListTaxWrapper>
                        {taxArray.map((tax, idx) => {
                          return (
                            <S.ListTaxRow key={`tax-list${idx}`}>
                              <S.TaxInfoText>{` ${tax.name}`}</S.TaxInfoText>
                              <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                                tax.taxAmount,
                                2,
                              )}`}</S.TaxInfoText>
                            </S.ListTaxRow>
                          );
                        })}
                      </S.ListTaxWrapper>
                    </S.TaxRightWrapper>
                  </S.TaxInformationWapper>
                ) : null}
              </S.Padding>
            ) : null}
          </S.Card>
        </Col>

        <Col md={isTabletOrMobile ? 24 : 16}>
          <BaseForm
            ref={formRef}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onFieldsChange={(changedFields: any[], allFields: any[]) => {
              setisMainGuest(allFields[7]?.value);
              if (allFields[7]?.value === 1) {
                form.setFieldValue('isSave_0', allFields[6]?.value);
              }
              const bookingForField = changedFields.find(field => field.name[0] === 'bookingFor');
              const firstNameField = allFields.find(field => field.name[0] === 'firstName');
              const lastNameField = allFields.find(field => field.name[0] === 'lastName');
              const idNumberField = allFields.find(field => field.name[0] === 'idNumber');
              const emailField = allFields.find(field => field.name[0] === 'email');

              if (bookingForField) setBookingForSomeone(bookingForField.value);

              if (bookingForField && bookingForField.value === 1 && bookingForField.touched) {
                form.setFieldValue('guestName_0', firstNameField?.value);
                form.setFieldValue('guestLastName_0', lastNameField?.value);
                form.setFieldValue('guestNic_0', idNumberField?.value);
                form.setFieldValue('email_0', emailField?.value);
              }
            }}
            initialValues={{bookingFor: 1}}
            form={form}
            size="middle"
            onFinish={handleSubmit}>
            <S.BlueCard>
              <S.Padding>
                <S.CardTitle>{t('commonNames.enterTheDetails')}</S.CardTitle>
                <Row gutter={{xs: 24, md: 15, xl: 30}}>
                  <Col sm={12} xs={24} md={8}>
                    <BaseForm.Item
                      name="firstName"
                      label="First Name"
                      rules={[
                        {
                          required: true,
                          message: 'Required field',
                        },
                      ]}>
                      <AutoComplete
                        onBlur={resetAutoCompleteData}
                        options={data}
                        onSearch={value => handleSearch(value, 'FIRST_NAME')}
                        onSelect={value => {
                          const selectedGuest = find(data, (o: {value: number}) => o.value === value);
                          if (selectedGuest !== undefined) handlePopulateMainguest(selectedGuest);
                        }}>
                        <Input onChange={e => onChangeMainGuest('firstName')} onBlur={handleFirstNameChange} />
                      </AutoComplete>
                    </BaseForm.Item>
                  </Col>
                  <Col xs={24} md={8}>
                    <BaseForm.Item
                      name="lastName"
                      label="Last Name"
                      rules={[
                        {
                          required: true,
                          message: 'Required field',
                        },
                      ]}>
                      <AutoComplete
                        onBlur={resetAutoCompleteData}
                        options={data}
                        onSearch={value => handleSearch(value, 'LAST_NAME')}
                        onSelect={value => {
                          const selectedGuest = find(data, (o: {value: number}) => o.value === value);
                          if (selectedGuest !== undefined) handlePopulateMainguest(selectedGuest);
                        }}>
                        <Input onChange={e => onChangeMainGuest('lastName')} onBlur={handleLastNameChange} />
                      </AutoComplete>
                    </BaseForm.Item>
                  </Col>
                  <Col xs={24} md={8}>
                    <BaseForm.Item
                      name="email"
                      label="Email Address"
                      rules={[
                        {
                          validator: validateEmail,
                          required: true,
                        },
                      ]}>
                      <AutoComplete
                        onBlur={resetAutoCompleteData}
                        options={data}
                        onSearch={value => handleSearch(value, 'EMAIL')}
                        onSelect={value => {
                          const selectedGuest = find(data, (o: {value: number}) => o.value === value);
                          if (selectedGuest !== undefined) handlePopulateMainguest(selectedGuest);
                        }}>
                        <Input onChange={e => onChangeMainGuest('email')} onBlur={handleEmailChange} />
                      </AutoComplete>
                    </BaseForm.Item>
                  </Col>
                </Row>

                <Row gutter={{xs: 10, md: 15, xl: 30}}>
                  <Col xs={24} md={8}>
                    <BaseForm.Item
                      name="countryId"
                      label="Country"
                      rules={[
                        {
                          required: true,
                          message: 'Required field',
                        },
                      ]}>
                      <Select
                        showSearch
                        filterOption={false}
                        onSearch={async (name: string) => {
                          const countries = await getCountries(name);
                          setCountry(countries);
                        }}
                        onChange={e => onChangeMainGuest('countryId')}
                        placeholder="Select Country">
                        {selectedRooms.isResident === true && !isExistMain && (
                          <Option key="srilanka" value={166}>
                            Sri Lanka
                          </Option>
                        )}
                        {(!selectedRooms.isResident || isExistMain) &&
                          country?.map((post: {title: string; value: number}, key) => {
                            return (
                              <Option key={key} value={post.value}>
                                {post.title}
                              </Option>
                            );
                          })}
                      </Select>
                    </BaseForm.Item>
                  </Col>
                  <Col xs={24} md={8}>
                    <BaseForm.Item
                      name="idNumber"
                      label="NIC/Passport No"
                      rules={[
                        {
                          required: false,
                          message: 'Required field',
                        },
                        {
                          validator: validateUniqueNic,
                        },
                      ]}>
                      <Input
                        onChange={e => {
                          handleMainGuestNicChange(e.target.value);
                          onChangeMainGuest('idNumber');
                        }}
                        onBlur={handleIDNumberChange}
                      />
                    </BaseForm.Item>
                  </Col>

                  <Col xs={24} md={8}>
                    <BaseForm.Item name="phoneNumber" label="Phone Number">
                      <Input onChange={() => onChangeMainGuest('phoneNumber')} />
                    </BaseForm.Item>
                  </Col>
                </Row>

                <Row gutter={{xs: 10, md: 15, xl: 30}}>
                  <Col hidden={isExistMain} xs={24} md={8}>
                    <BaseForm.Item
                      name="isSave"
                      label="Save this guest?"
                      rules={[
                        {
                          required: true,
                          message: 'Required field',
                        },
                      ]}>
                      <RadioGroup defaultValue={1}>
                        <Radio value={1}>Yes</Radio>
                        <Radio value={2}>No</Radio>
                      </RadioGroup>
                    </BaseForm.Item>
                  </Col>
                  <Col xs={24} md={12}>
                    <BaseForm.Item
                      name="bookingFor"
                      label="Who are you booking for?"
                      rules={[
                        {
                          required: true,
                          message: 'Required field',
                        },
                      ]}>
                      <RadioGroup
                        onChange={e => {
                          if (e.target.value === 2) {
                            form.setFieldValue('guestName_0', '');
                            form.setFieldValue('guestLastName_0', '');
                            form.setFieldValue('guestNic_0', '');
                            form.setFieldValue('email_0', '');
                          }
                        }}
                        defaultValue={1}>
                        <Radio value={1}>I am the main guest</Radio>
                        <Radio value={2}>Booking for someone else</Radio>
                      </RadioGroup>
                    </BaseForm.Item>
                  </Col>
                </Row>
                <Row style={{gap: 10}} justify="end">
                  <BaseForm.Item name="toSendMail" label="">
                    <Radio
                      value={mainGuestCheckboxes.toSendMail}
                      checked={mainGuestCheckboxes.toSendMail}
                      onChange={e => handleMainGuestCheckboxChange('toSendMail', e.target.checked)}>
                      <S.CheckboxLabel>Send Confirmation to Payee</S.CheckboxLabel>
                    </Radio>
                  </BaseForm.Item>

                  <BaseForm.Item name="toAttachedPersonDetails" label="">
                    <Radio
                      value={mainGuestCheckboxes.toAttachedPersonDetails}
                      checked={mainGuestCheckboxes.toAttachedPersonDetails}
                      onChange={e => handleMainGuestCheckboxChange('toAttachedPersonDetails', e.target.checked)}>
                      <S.CheckboxLabel>Payee Details</S.CheckboxLabel>
                    </Radio>
                  </BaseForm.Item>
                </Row>
              </S.Padding>
            </S.BlueCard>

            <S.BlurCardWrapper>
              {selectedRooms &&
                selectedRooms?.stayTypes.map((stay, idx) => {
                  const stayTypeIndex = idx;

                  return (
                    <S.BlueCard key={idx}>
                      <S.Padding>
                        <S.TitleWrapper>
                          <S.StayTypeWrapper>
                            <S.CardTitle>
                              <StayTypeTitle
                                adultCount={stay.roomDetails.numberOfAdult}
                                childCount={stay.roomDetails.numberOfChildren}
                                isBold={true}
                                meal={stay.roomDetails.mealType}
                                name={stay.roomDetails.roomType}
                                size={FONT_SIZE.lg}
                              />
                            </S.CardTitle>
                            {selectedRooms?.reserveType === 'HOTEL' ? (
                              <Tooltip title={stay.roomDetails.roomName}>
                                <S.RoomNameWrapper onClick={() => handleClickEditRoom(stay, stayTypeIndex)}>
                                  <S.RoomNameContainer>
                                    <S.RoomName>{stay.roomDetails.roomNumber}</S.RoomName>
                                  </S.RoomNameContainer>
                                  <S.IconContainer>
                                    <EditOutlined style={{color: BASE_COLORS.white, fontSize: '12px'}} />
                                  </S.IconContainer>
                                </S.RoomNameWrapper>
                              </Tooltip>
                            ) : null}
                          </S.StayTypeWrapper>

                          {permissions.VIEW && (
                            <S.OffPriceContainer>
                              <S.DiscountButton
                                onClick={() => {
                                  setselectedStayIndex(Number(stayTypeIndex));
                                  setisVisible(true);
                                }}>
                                Discount
                              </S.DiscountButton>
                              <S.Price>
                                {currency}{' '}
                                {convertNumberFormatWithDecimal(
                                  selectedRooms?.stayTypes[stayTypeIndex]?.roomDetails?.discountAmount
                                    ? Number(selectedRooms?.stayTypes[stayTypeIndex]?.roomDetails?.discountAmount)
                                    : 0,
                                  2,
                                )}{' '}
                                {selectedRooms?.stayTypes[stayTypeIndex]?.roomDetails?.discountOption ===
                                DISCOUNT_OPTION.PERCENTAGE
                                  ? `(${selectedRooms?.stayTypes[stayTypeIndex]?.roomDetails?.enteredDiscountValue}%)`
                                  : ''}{' '}
                              </S.Price>
                            </S.OffPriceContainer>
                          )}
                        </S.TitleWrapper>
                        <S.AmenitiesSection>
                          {stay.roomDetails.amenities?.map(
                            (
                              amenities: {
                                amenityType: string;
                                amenityIcon: any;
                                name:
                                  | boolean
                                  | React.ReactChild
                                  | React.ReactFragment
                                  | React.ReactPortal
                                  | Iterable<ReactI18NextChild>
                                  | null
                                  | undefined;
                              },
                              idx: React.Key | null | undefined,
                            ) => {
                              if (amenities?.amenityType === 'PRIMARY')
                                return (
                                  <S.AmenitiesOutline key={idx}>
                                    <IconPickerItem color="#006650" icon={amenities?.amenityIcon} size={16} />
                                    <S.Amenities>{amenities?.name}</S.Amenities>
                                  </S.AmenitiesOutline>
                                );
                            },
                          )}
                        </S.AmenitiesSection>
                        <Row gutter={{xs: 10, md: 15, xl: 30}}>
                          <Col xs={24} md={8}>
                            <BaseForm.Item
                              name={`guestName_${idx}`}
                              label="First Name"
                              rules={[
                                {
                                  required: true,
                                  message: 'Required field',
                                },
                              ]}>
                              <Input
                                onChange={e => {
                                  idx === 0 ? handleChangeDependedGuest() : undefined;
                                  handleChangeExisting(e, stayTypeIndex);
                                }}
                              />
                            </BaseForm.Item>
                          </Col>

                          <Col xs={24} md={8}>
                            <BaseForm.Item
                              name={`guestLastName_${idx}`}
                              label="Last Name"
                              rules={[
                                {
                                  required: true,
                                  message: 'Required field',
                                },
                              ]}>
                              <Input
                                onChange={e => {
                                  idx === 0 ? handleChangeDependedGuest() : undefined;
                                  handleChangeExisting(e, stayTypeIndex);
                                }}
                              />
                            </BaseForm.Item>
                          </Col>

                          <Col xs={24} md={8}>
                            <BaseForm.Item
                              name={`email_${idx}`}
                              label="Email Address"
                              rules={[
                                {
                                  required: roomGuestCheckboxes[stayTypeIndex]?.toSendMail ? true : false,
                                  validator: roomGuestCheckboxes[stayTypeIndex]?.toSendMail
                                    ? validateEmail
                                    : validateEmailWithoutRequired,
                                },
                              ]}>
                              <AutoComplete
                                onBlur={resetAutoCompleteData}
                                options={data}
                                onSearch={value => handleSearch(value, 'EMAIL')}
                                onSelect={(value, option: any) => {
                                  const selectedGuest = find(data, (o: {value: number}) => o.value === value);
                                  if (selectedGuest !== undefined) handlePopulateRoomguestDetails(selectedGuest, idx);
                                }}>
                                <Input
                                  onChange={e => {
                                    idx === 0 ? handleChangeDependedGuest() : undefined;
                                    handleChangeExisting(e, stayTypeIndex);
                                    form.setFieldValue('guestNic_0', '');
                                  }}
                                />
                              </AutoComplete>
                            </BaseForm.Item>
                          </Col>
                          <Col xs={24} md={8}>
                            <BaseForm.Item
                              name={`guestNic_${idx}`}
                              label="NIC/Passport No"
                              rules={[
                                {
                                  required: false,
                                  message: 'Required field',
                                },
                                {
                                  validator: validateUniqueNic,
                                },
                              ]}>
                              <Input
                                onChange={e => {
                                  idx === 0 ? handleChangeDependedGuest() : undefined;
                                  handleChangeExisting(e, stayTypeIndex);
                                  handleRoomGuestNicChange(e.target.value, stayTypeIndex);
                                }}
                              />
                            </BaseForm.Item>
                          </Col>

                          <Col xs={24} md={8}>
                            <BaseForm.Item
                              name={`guestNumber_${stayTypeIndex}`}
                              label={t('commonNames.numberOfGuest')}
                              rules={[
                                {
                                  required: true,
                                  message: 'Required field',
                                },
                              ]}>
                              <Select
                                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                                onChange={(value: any) => handleNumberOfGuestsChange(value, stayTypeIndex)}>
                                {Array.from(
                                  {length: selectedRooms?.stayTypes[idx].roomDetails.numberOfAdult},
                                  (_, adultIndex) => [
                                    {adult: adultIndex + 1, child: 0},
                                    ...Array.from(
                                      {length: selectedRooms?.stayTypes[idx].roomDetails.numberOfChildren},
                                      (_, childIndex) => ({
                                        adult: adultIndex + 1,
                                        child: childIndex + 1,
                                      }),
                                    ),
                                  ],
                                ).flatMap(combination =>
                                  combination.map(({adult, child}) => (
                                    <Option key={`adult_${adult}_child_${child}`}>
                                      {`${adult} Guests ${child} Children`}
                                    </Option>
                                  )),
                                )}
                              </Select>
                            </BaseForm.Item>
                          </Col>

                          <Col xs={24} md={8}>
                            <BaseForm.Item
                              name={`noExtraChildren_${stayTypeIndex}`}
                              label={t('commonNames.numberOfExtraBed')}
                              rules={[
                                {
                                  required: false,
                                  message: 'Required field',
                                },
                              ]}>
                              <Select
                                disabled={!stay.roomDetails.validateCombination}
                                onSelect={value => {
                                  handleExtraChildCount(stay.roomDetails.key, Number(value), stayTypeIndex);
                                }}>
                                <Option key={0}>0</Option>
                                {Array.from(
                                  {length: stay.roomDetails.validatePersonCount},
                                  (_, index) => index + 1,
                                ).map(cNumber => {
                                  return <Option key={cNumber}>{cNumber}</Option>;
                                })}
                              </Select>
                            </BaseForm.Item>
                          </Col>
                        </Row>
                        {values[`guestNumber_${stayTypeIndex}`] > 1 && <Divider>Additional Guests Details</Divider>}
                        {Array.from({length: values[`guestNumber_${stayTypeIndex}`] - 1}, (_, guestIndex) => (
                          <Row gutter={{xs: 10, md: 15, xl: 30}} key={guestIndex}>
                            <Col xs={24} md={8}>
                              <BaseForm.Item
                                rules={[
                                  {
                                    required: false,
                                    message: 'Required field',
                                  },
                                ]}
                                name={`firstName_${stayTypeIndex}_${guestIndex + 1}`}
                                label={`First Name`}>
                                <Input />
                              </BaseForm.Item>
                            </Col>
                            <Col xs={24} md={8}>
                              <BaseForm.Item
                                rules={[
                                  {
                                    required: false,
                                    message: 'Required field',
                                  },
                                ]}
                                name={`lastName${stayTypeIndex}_${guestIndex + 1}`}
                                label={`Last Name`}>
                                <Input />
                              </BaseForm.Item>
                            </Col>
                          </Row>
                        ))}

                        <Row style={{gap: 10}} justify="end">
                          <BaseForm.Item name={`toSendMail_${stayTypeIndex}`} label="">
                            <Radio
                              checked={roomGuestCheckboxes[stayTypeIndex]?.toSendMail || false}
                              onChange={e =>
                                handleRoomGuestCheckboxChange(stayTypeIndex, 'toSendMail', e.target.checked)
                              }>
                              {' '}
                              <S.CheckboxLabel>
                                {bookingForSomeone === 1
                                  ? 'Send Confirmation to Payee'
                                  : ' Send Confirmation to the Guest'}
                              </S.CheckboxLabel>
                            </Radio>
                          </BaseForm.Item>
                          <BaseForm.Item name={`toAttachedPersonDetails_${stayTypeIndex}`} label="">
                            <Radio
                              checked={roomGuestCheckboxes[stayTypeIndex]?.toAttachedPersonDetails || false}
                              onChange={e =>
                                handleRoomGuestCheckboxChange(
                                  stayTypeIndex,
                                  'toAttachedPersonDetails',
                                  e.target.checked,
                                )
                              }>
                              {' '}
                              <S.CheckboxLabel>
                                {bookingForSomeone === 1 ? 'Payee Details' : 'Guest Details'}
                              </S.CheckboxLabel>
                            </Radio>
                          </BaseForm.Item>
                        </Row>

                        {selectedRooms.stayTypes.length > 1 && (
                          <S.RemoveWrapper>
                            <Popconfirm
                              placement="leftTop"
                              title="Are you sure to remove this room?"
                              onConfirm={() => onRemove(stayTypeIndex)}
                              okText="Yes"
                              cancelText="No">
                              <S.RemoveButton>Remove</S.RemoveButton>
                            </Popconfirm>
                          </S.RemoveWrapper>
                        )}
                      </S.Padding>
                    </S.BlueCard>
                  );
                })}
            </S.BlurCardWrapper>

            {/* {!isEmpty(balanceAmounts) && (
              <S.BlurCardWrapper>
                <S.BlueCard>
                  <S.Padding>
                    <S.CardTitle>Credit Note Details</S.CardTitle>
                    {balanceAmounts.map((credit: any, idx) => (
                      <S.BalanceButton
                        key={idx}
                        type={selectedBalances.includes(credit.id) ? 'primary' : 'default'}
                        onClick={() => handleBalanceSelect(credit.id)}>
                        {currency} {credit.balanceAmount}
                      </S.BalanceButton>
                    ))}
                  </S.Padding>
                </S.BlueCard>
              </S.BlurCardWrapper>
            )} */}

            <S.BlurCardWrapper>
              <S.BlueCard>
                <S.Padding>
                  <S.CardTitle>VAT Details</S.CardTitle>
                  <Row style={{gap: 10}} justify="start">
                    <BaseForm.Item label="">
                      <Checkbox
                        value={isVatDetailsAvailable}
                        checked={isVatDetailsAvailable}
                        onChange={e => handleClickAddVatDetails(e.target.checked)}>
                        <S.CheckboxLabel>Add VAT Details</S.CheckboxLabel>
                      </Checkbox>
                    </BaseForm.Item>
                  </Row>
                  <Row gutter={{xs: 24, md: 15, xl: 30}}>
                    <Col sm={12} xs={24} md={8}>
                      <BaseForm.Item
                        name="vatPersonEmail"
                        label={commonNames.vatEmail}
                        rules={[
                          {
                            required: isVatDetailsAvailable,
                            validator: isVatDetailsAvailable ? validateEmail : validateEmailWithoutRequired,
                          },
                        ]}>
                        <AutoComplete
                          disabled={!isVatDetailsAvailable}
                          onBlur={(event: any) => {
                            const isValidEmail = /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/g.test(event.target.value);
                            if (event.target.value.trim() !== '' && isValidEmail) {
                              handleBlurPopulate(event.target.value, 'vatPersonEmail');
                            }
                            resetVatSearchedData();
                          }}
                          options={searchedVatUsers}
                          onSearch={value => handleSearchVatUsers(value, 'vatPersonEmail')}
                          onSelect={value => {
                            const selectedGuest = find(searchedVatUsers, (o: {value: number}) => o.value === value);
                            if (selectedGuest !== undefined) handlePopulateVatInformation(selectedGuest);
                          }}>
                          <Input
                            onChange={() => handleChangeVatInput('vatPersonEmail')}
                            disabled={!isVatDetailsAvailable}
                          />
                        </AutoComplete>
                      </BaseForm.Item>
                    </Col>
                    <Col sm={12} xs={24} md={8}>
                      <BaseForm.Item
                        name="vatPersonName"
                        label={t('commonNames.vatPersonName')}
                        rules={[
                          {
                            required: isVatDetailsAvailable,
                            message: 'Required field',
                          },
                        ]}>
                        <Input
                          onChange={() => handleChangeVatInput('vatPersonName')}
                          disabled={!isVatDetailsAvailable}
                        />
                      </BaseForm.Item>
                    </Col>
                    <Col sm={12} xs={24} md={8}>
                      <BaseForm.Item
                        name="vatNumber"
                        label="VAT Number"
                        rules={[
                          {
                            required: isVatDetailsAvailable,
                            message: 'Required field',
                          },
                        ]}>
                        <AutoComplete
                          disabled={!isVatDetailsAvailable}
                          onBlur={(event: any) => {
                            if (event.target.value.trim() !== '') {
                              handleBlurPopulate(event.target.value, 'vatNumber');
                            }
                            resetVatSearchedData();
                          }}
                          options={searchedVatUsers}
                          onSearch={value => handleSearchVatUsers(value, 'vatNumber')}
                          onSelect={value => {
                            const selectedGuest = find(searchedVatUsers, (o: {value: number}) => o.value === value);

                            if (selectedGuest !== undefined) handlePopulateVatInformation(selectedGuest);
                          }}>
                          <Input onChange={() => handleChangeVatInput('vatNumber')} disabled={!isVatDetailsAvailable} />
                        </AutoComplete>
                      </BaseForm.Item>
                    </Col>

                    <Col sm={12} xs={24} md={8}>
                      <BaseForm.Item
                        name="vatPersonAddress"
                        label={t('commonNames.vatAddress')}
                        rules={[
                          {
                            required: isVatDetailsAvailable,
                            message: 'Required field',
                          },
                        ]}>
                        <Input
                          onChange={() => handleChangeVatInput('vatPersonAddress')}
                          disabled={!isVatDetailsAvailable}
                        />
                      </BaseForm.Item>
                    </Col>
                  </Row>
                </S.Padding>
              </S.BlueCard>
            </S.BlurCardWrapper>

            <S.ArrivalTimeWrapper>
              <S.BlueCard>
                <S.Padding>
                  <S.CardTitle>{t('commonNames.yourArrivalTime')}</S.CardTitle>
                  <S.DescriptionWrapper>
                    <S.CheckIcon />
                    <S.Description>Your rooms will be ready for check-in at 1:00 PM</S.Description>
                  </S.DescriptionWrapper>
                  <br />
                  <S.DescriptionWrapper>
                    <S.DeskIcon />
                    <S.Description>24/7 Hours Front-Desk Service Available</S.Description>
                  </S.DescriptionWrapper>

                  <Row gutter={{xs: 10, md: 15, xl: 30}}>
                    <Col xs={24} md={12}>
                      <BaseForm.Item
                        name="arrivalTime"
                        label={t('commonNames.yourEstimatedArrivalTime')}
                        rules={[
                          {
                            required: false,
                            message: 'Required field',
                          },
                        ]}>
                        <Select placeholder="Please select">
                          {ArrivalTimeRange.map(timeRange => {
                            return <Option key={timeRange.key}>{timeRange.time} </Option>;
                          })}
                        </Select>
                      </BaseForm.Item>
                    </Col>
                  </Row>
                </S.Padding>
              </S.BlueCard>
            </S.ArrivalTimeWrapper>

            <S.ArrivalTimeWrapper>
              <S.BlueCard>
                <S.Padding>
                  <S.CardTitle style={{marginBottom: 10}}>{t('commonNames.remarks')}</S.CardTitle>

                  <Row gutter={{xs: 10, md: 15, xl: 30}}>
                    <Col xs={24} md={24}>
                      <BaseForm.Item
                        name="internalRemarks"
                        label=""
                        rules={[
                          {
                            required: false,
                            message: 'Required field',
                          },
                        ]}>
                        <TextArea placeholder="Description..." rows={4} />
                      </BaseForm.Item>
                    </Col>
                  </Row>
                </S.Padding>
              </S.BlueCard>
            </S.ArrivalTimeWrapper>

            <Row gutter={{xs: 10, md: 15, xl: 30}} justify="end">
              <Col xs={24} md={6}>
                <Popconfirm
                  title="Are you sure to cancel this reservation?"
                  onConfirm={() => cancelBlockedReservationDetails(selectedRooms.id, false)}
                  okText="Yes"
                  cancelText="No">
                  <Button type="default" style={{marginTop: '1rem'}}>
                    Cancel Booking
                  </Button>
                </Popconfirm>
              </Col>
              <Col xs={24} md={6}>
                <Button loading={isLoading} htmlType="submit" type="primary" style={{marginTop: '1rem'}}>
                  Proceed to Pay
                </Button>
              </Col>
            </Row>
          </BaseForm>
        </Col>
      </Row>
      <DiscountUpdateModalContent
        stayTypeIndex={selectedStayIndex}
        selectedRooms={selectedRooms}
        discountForm={discountForm}
        dispatch={dispatch}
        closeModal={() => {
          setisVisible(false);
        }}
        isVisible={isVisible}
        extraChildCountInRooms={extraChildCountInRooms}
        onCancelClick={() => {
          setisVisible(false);
          discountForm.resetFields();
          setselectedStayIndex(undefined);
        }}
      />
      <DialogBox
        // @ts-ignore
        showDialog={showPrompt}
        confirmNavigation={() => {
          cancelBlockedReservationDetails(selectedRooms.id, true);
        }}
        cancelNavigation={cancelNavigation}
        loading={loadCancellingReservation}
      />

      <ChangeRoomModal
        isVisible={isVisibleAvailableRoomModal}
        onClose={() => setisVisibleAvailableRoomModal(false)}
        reservedRoomData={selectedReservedRoomData}
        handleUpdateRoom={function (data: any, roomData: any): void {
          handleUpdateRoom(data, roomData);
        }}
        updateLoading={changeRoomLoading}
      />
    </>
  );
}
