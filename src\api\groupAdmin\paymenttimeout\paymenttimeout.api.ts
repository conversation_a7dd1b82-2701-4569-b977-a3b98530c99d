import authInstance from '@app/api/authInstance';
import instance, {HOTEL_SERVICE, LOGIN_SERVICE} from '@app/api/instance';

interface IContactNumbers {
  contactNumbers: string;
}
export interface IHotelRequest {
  id?: number;
  name: string;
  title: string;
  address: string;
  contactNumber?: string;
  email?: string;
  type?: string;
  cardLogo: File;
  cardImage: File;
  sideBarImage: File;
  titleImage: File;
  groupId: number;
  contactNumbers?: IContactNumbers[];
}

export interface IApiResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface IupdatePayload {
  id: number;
  seconds: number;
  name: string;
  hotelId: number;
  
}

export const getAllPaymentTime = (hotelId:number): Promise<IApiResponse> =>
  instance.get<IApiResponse>(HOTEL_SERVICE + `payment-time-out-config/all?hotelId=${hotelId}`).then(({data}) => data);

export const updatepayment = (payload: IupdatePayload): Promise<IApiResponse> => {
  return instance.put<IApiResponse>(HOTEL_SERVICE + 'payment-time-out-config', payload).then(({data}) => data);
};
