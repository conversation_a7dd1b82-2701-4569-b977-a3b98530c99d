/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-12-19 10:35:10
 * @modify date 2024-12-19 10:35:10
 * @desc [description]
 */

import React, {useState} from 'react';
import {Modal as AntdModal} from 'antd';
import {LoadingOutlined} from '@ant-design/icons';
import EmailEditor from '../EmailEditor/EmailEditor';
import {Modal} from '../Modal/Modal';
import * as S from './EmailCustomizer.style';
import {BASE_COLORS} from '@app/styles/themes/constants';

interface Props {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  status: 'idle' | 'loading' | 'success' | 'error';
  setStatus: (status: 'idle' | 'loading' | 'success' | 'error') => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onSubmitEmailCustomizer: (data: any) => void;
  isResender: boolean;
  reservationId: number;
  emailType: string;
  message: string;
  pendingMailId: number | null;
  onClose: () => void;
  onEmpty?: () => void;
  getUrl?: string;
  sendUrl?: string;
  type?: 'EVENT' | 'RESERVATION';
}

const EmailCustomizer: React.FC<Props> = ({
  isOpen,
  setIsOpen,
  status = 'idle',
  setStatus,
  onSubmitEmailCustomizer,
  emailType,
  reservationId,
  pendingMailId,
  onClose,
  onEmpty,
  getUrl,
  sendUrl,
  type = 'RESERVATION',
}) => {
  const [isEditable, setIsEditable] = useState(false);

  const handleClose = () => {
    setIsOpen(false);
    setStatus('idle');
    if (onEmpty) {
      onEmpty();
    }
  };

  const BookingLoadingModal = () => (
    <AntdModal
      open={status === 'loading'}
      closable={false}
      footer={null}
      centered
      width={300}
      bodyStyle={{
        textAlign: 'center',
        padding: '40px 20px',
        background: '#f5f5f5',
      }}>
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}>
        <LoadingOutlined
          style={{
            fontSize: '48px',
            color: BASE_COLORS.primary,
            marginBottom: '20px',
          }}
          spin
        />
        <h3
          style={{
            color: BASE_COLORS.primary,
            marginBottom: '10px',
            fontWeight: 600,
          }}>
          Processing
        </h3>
        <p
          style={{
            color: '#6b6b6b',
            fontSize: '14px',
          }}>
          Please wait while sending your email
        </p>
      </div>
    </AntdModal>
  );

  const onModalClose = () => {
    handleClose();
    onClose && onClose();
    setIsEditable(false);
  };

  const handleSuccess = () => {
    setStatus('success');
    setIsOpen(false);
    if (onEmpty) {
      onEmpty();
    }
  };

  return (
    <>
      <BookingLoadingModal />
      <Modal title="Email Customization" size="large" open={isOpen} onCancel={onModalClose} footer={[]} centered>
        <S.TemplateViewer>
          <EmailEditor
            emailType={emailType}
            reservationId={reservationId}
            key="editor"
            onSubmitEmailCustomizer={onSubmitEmailCustomizer}
            pendingMailId={pendingMailId}
            onSuccess={() => setStatus('success')}
            isOpen={isOpen}
            onSendStart={() => setStatus('loading')}
            onSendSuccess={handleSuccess}
            onSendError={() => setStatus('error')}
            setIsOpen={setIsOpen}
            setIsEditable={setIsEditable}
            isEditable={isEditable}
            getUrl={getUrl}
            sendUrl={sendUrl}
            type={type}
            onEmpty={onEmpty}
          />
        </S.TemplateViewer>
      </Modal>
    </>
  );
};

export default EmailCustomizer;
