export interface Message {
  id: number;
  description: string;
  channelId: number;
}

export interface Mention extends Message {
  userName: string;
  userIcon: string;
  place: string;
  href: string;
}

export type Notification = Mention | Message;

export const notifications = [
  {
    id: 4,
    description: 'header.notifications.mention',
    userName: '<PERSON>',
    userIcon:
      'https://res.cloudinary.com/lapkinthegod/image/upload/v1629187274/young-male-doctor-white-uniform_x7dcrs.jpg',
    place: 'medical-dashboard.latestScreenings.title',
    href: `/#latest-screenings`,
  },
];
