import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const CreateDriver = (payload: CreateDriverProps): Promise<DriverResponse> => {
  return instance.post<DriverResponse>(HOTEL_SERVICE + 'driver', payload).then(({data}) => data);
};

export const UpdateDriver = (payload: UpdateDriverProps): Promise<DriverResponse> => {
  return instance.put<DriverResponse>(HOTEL_SERVICE + 'driver', payload).then(({data}) => data);
};

export const getAllDrivers = (
  {firstName, lastName, vehicleNumber, idNumber, email, contactNumber, channelName}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<DriverResponse> =>
  instance
    .get<DriverResponse>(
      HOTEL_SERVICE +
        `driver/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&firstName=${
          firstName ? firstName : ''
        }&lastName=${lastName ? lastName : ''}&email=${email ? email : ''}&contactNumber=${
          contactNumber ? contactNumber : ''
        }&idNumber=${idNumber ? idNumber : ''}&vehicleNumber=${vehicleNumber ? vehicleNumber : ''}&channelName=${
          channelName ? channelName : ''
        }`,
    )
    .then(({data}) => data);

export const DeleteDriver = (id: number): Promise<DriverResponse> =>
  instance.delete<DriverResponse>(HOTEL_SERVICE + `driver/${id}`).then(({data}) => data);

export interface CreateDriverProps {
  firstName: string;
  lastName: string;
  vehicleNumber: string;
  email: string;
  contactNumber: string;
  idNumber: string;
  nicNumber: boolean;
  remarks: string;
  standalone: boolean;
  channelId: number;
}

export interface UpdateDriverProps {
  id: number;
  firstName: string;
  lastName: string;
  vehicleNumber: string;
  email: string;
  contactNumber: string;
  idNumber: string;
  nicNumber: boolean;
  remarks: string;
  standalone: boolean;
  channelId: number;
}

export interface DriverResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  firstName: string;
  lastName: string;
  vehicleNumber: string;
  email: string;
  contactNumber: string;
  idNumber: string;
  channelName: string;
}
