import React from 'react';
import * as S from './StayTypeTitle.style';
import {IoPersonSharp} from 'react-icons/io5';

interface Props {
  name?: string;
  adultCount: number;
  childCount: number;
  meal: string;
  size: string;
  isBold: boolean;
  adultSize?: number;
  childSize?: number;
  align?: string;
}

export const StayTypeExtraChild: React.FC<Props> = ({
  adultCount,
  childCount,
  isBold,
  meal,
  name,
  size,
  adultSize = 16,
  childSize = 12,
  align = 'center',
}) => {
  return (
    <S.StayTitle style={{marginBottom: 0, display: 'flex', justifyContent: align}} $fontSize={size} $isBold={isBold}>
      <S.TitleIconWrapper>
        <div hidden={childCount === 0 ? true : false}>
          {childCount > 3 ? (
            <>
              {childCount} <IoPersonSharp size={childSize} />
            </>
          ) : (
            Array.from({length: childCount}, (_, index) => index + 1).map(number => {
              return <IoPersonSharp size={childSize} key={number} />;
            })
          )}
        </div>
      </S.TitleIconWrapper>
    </S.StayTitle>
  );
};
