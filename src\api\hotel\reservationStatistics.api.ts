import {IDateRange} from '@app/pages/Hotel/Dashboard/HotelDashboardPage';
import instance, {HOTEL_SERVICE} from '../instance';

// Occupancy
export const getAllReservationStatistics = (hotelId: number, dateRange: IDateRange, rangeType: string) =>
  instance
    .get(
      HOTEL_SERVICE +
        `reservation/roomCount/${hotelId}/count-type?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}&range=${rangeType}`,
    )
    .then(({data}) => data);

// Reservation By Residency
export const getAllResidencyStatistics = (hotelId: number, dateRange: IDateRange, rangeType: string) =>
  instance
    .get(
      HOTEL_SERVICE +
        `reservation/reservationCount/${hotelId}/reservation-type?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}&range=${rangeType}`,
    )
    .then(({data}) => data);

// By Reservation Types
export const getReservationTypeCountStatistics = (hotelId: number, dateRange: IDateRange, rangeType: string) =>
  instance
    .get(
      HOTEL_SERVICE +
        `reservation/reservationTypeCount/${hotelId}/reservationtype-type?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}&range=${rangeType}`,
    )
    .then(({data}) => data);

// Room Type Reservation
export const getRoomTypeCountStatistics = (hotelId: number, dateRange: IDateRange) =>
  instance
    .get(
      HOTEL_SERVICE +
        `reservation/reservationRoomType/${hotelId}/reservation-room-type?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`,
    )
    .then(({data}) => data);

// Seasonal Reservation
export const getSeasonCountStatistics = (hotelId: number, dateRange: IDateRange) =>
  instance
    .get(
      HOTEL_SERVICE +
        `reservation/reservationSeason/${hotelId}/reservation-season?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`,
    )
    .then(({data}) => data);

// Lead Type Reservation
export const getLeadTypeCountStatistics = (hotelId: number, dateRange: IDateRange) =>
  instance
    .get(
      HOTEL_SERVICE +
        `lead-type/dash-board/search?hotelId=${hotelId}&startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`,
    )
    .then(({data}) => data);

// Reservation Type Count
export const getReservationTypeCount = (hotelId: number, dateRange: IDateRange) =>
  instance
    .get(
      HOTEL_SERVICE +
        `reservation/reservationTypeReservationCount/date?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}&hotelId=${hotelId}`,
    )
    .then(({data}) => data);

// export const getprintoccupancy = (hotelId: number, firstRange: IDateRange, secondRange: IDateRange, range: string) =>
//   instance
//     .get(
//       HOTEL_SERVICE +
//         `reservation/roomCount/comparison/${hotelId}?firstStartDate=${firstRange.startDate}&firstEndDate=${firstRange.endDate}&secondStartDate=${secondRange.startDate}&secondEndDate=${secondRange.endDate}&range=${range}`,
//     )
//     .then(({data}) => data);

export const getprintoccupancy = (hotelId: number, firstRange: IDateRange, secondRange: IDateRange, range: string) => {
  const url = `${HOTEL_SERVICE}reservation/roomCount/comparison/${hotelId}?firstStartDate=${firstRange.startDate}&firstEndDate=${firstRange.endDate}&secondStartDate=${secondRange.startDate}&secondEndDate=${secondRange.endDate}&range=${range}`;
  return instance
    .get(url)
    .then(({data}) => {
      console.log('API Response Data:', data);
      return data;
    })
    .catch(error => {
      console.error('API Request Error:', error);
      throw error;
    });
};

export const getprintResidency = (hotelId: number, firstRange: IDateRange, secondRange: IDateRange, range: string) => {
  const url = `${HOTEL_SERVICE}reservation/reservationCount/ResidentialComparison/${hotelId}?firstStartDate=${firstRange.startDate}&firstEndDate=${firstRange.endDate}&secondStartDate=${secondRange.startDate}&secondEndDate=${secondRange.endDate}&range=${range}`;
  return instance
    .get(url)
    .then(({data}) => {
      console.log('API Response Data:', data);
      return data;
    })
    .catch(error => {
      console.error('API Request Error:', error);
      throw error;
    });
};

export const getprintReservationtype = (
  hotelId: number,
  firstRange: IDateRange,
  secondRange: IDateRange,
  range: string,
) => {
  const url = `${HOTEL_SERVICE}reservation/reservationTypeCount/comparison/${hotelId}?firstStartDate=${firstRange.startDate}&firstEndDate=${firstRange.endDate}&secondStartDate=${secondRange.startDate}&secondEndDate=${secondRange.endDate}&range=${range}`;
  console.log('API Request URL:', url);
  return instance
    .get(url)
    .then(({data}) => {
      console.log('API Response Data:', data);
      return data;
    })
    .catch(error => {
      console.error('API Request Error:', error);
      throw error;
    });
};

//Check this 500
export const getprintReservationTypeCount = (hotelId: number, firstRange: IDateRange, secondRange: IDateRange) => {
  const url = `${HOTEL_SERVICE}reservation/reservationTypeReservationCount/comparison?firstStartDate=${firstRange.startDate}&firstEndDate=${firstRange.endDate}&secondStartDate=${secondRange.startDate}&secondEndDate=${secondRange.endDate}&hotelId=${hotelId}`;
  console.log('API Request URL:', url);
  return instance
    .get(url)
    .then(({data}) => {
      console.log('API Response Data:', data);
      return data;
    })
    .catch(error => {
      console.error('API Request Error:', error);
      throw error;
    });
};

//Check this sent month
export const getprintRoomType = (hotelId: number, firstRange: IDateRange, secondRange: IDateRange) => {
  const url = `${HOTEL_SERVICE}reservation/reservationRoomType/comparison/${hotelId}?firstStartDate=${firstRange.startDate}&firstEndDate=${firstRange.endDate}&secondStartDate=${secondRange.startDate}&secondEndDate=${secondRange.endDate}`;
  console.log('API Request URL:', url);
  return instance
    .get(url)
    .then(({data}) => {
      console.log('API Response Data:', data);
      return data;
    })
    .catch(error => {
      console.error('API Request Error:', error);
      throw error;
    });
};

export const getprintseason = (hotelId: number, firstRange: IDateRange, secondRange: IDateRange) => {
  const url = `${HOTEL_SERVICE}reservation/reservationSeason/comparison/${hotelId}?firstStartDate=${firstRange.startDate}&firstEndDate=${firstRange.endDate}&secondStartDate=${secondRange.startDate}&secondEndDate=${secondRange.endDate}`;
  console.log('API Request URL:', url);
  return instance
    .get(url)
    .then(({data}) => {
      console.log('API Response Data:', data);
      return data;
    })
    .catch(error => {
      console.error('API Request Error:', error);
      throw error;
    });
};

export const getprintlead = (hotelId: number, firstRange: IDateRange, secondRange: IDateRange) => {
  const url = `${HOTEL_SERVICE}lead-type/dash-board/search/comparison?firstStartDate=${firstRange.startDate}&firstEndDate=${firstRange.endDate}&secondStartDate=${secondRange.startDate}&secondEndDate=${secondRange.endDate}&hotelId=${hotelId}`;
  console.log('API Request URL:', url);
  return instance
    .get(url)
    .then(({data}) => {
      console.log('API Response Data:', data);
      return data;
    })
    .catch(error => {
      console.error('API Request Error:', error);
      throw error;
    });
};
