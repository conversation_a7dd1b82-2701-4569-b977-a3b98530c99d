import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import React, {useEffect, useState} from 'react';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {Card, Checkbox, Col, DatePicker, Form, Input, Modal, Row, Select, Space} from 'antd';
import {CalendarOutlined, CreditCardFilled, FilePdfOutlined, MoneyCollectOutlined} from '@ant-design/icons';
import moment from 'moment';
import '@app/pages/MasterPages/hotel/Invoice/CreditNote/CreditNote.style.css';
import {
  getReciptData,
  postCreditNote,
  filterRecervationNumber,
} from '@app/pages/MasterPages/hotel/Invoice/CreditNote/creditNote.api';
import {notificationController} from '@app/controllers/notificationController';
import {convertNumberFormatWithDecimal} from '@app/utils/utils';
import {setLoading, setModalVisible} from '@app/store/slices/commonSlice';
import dayjs from 'dayjs';
import CurrencyInput from '@app/components/common/inputs/CurrencyInput/CurrencyInput';
import {Button} from '../buttons/Button/Button';
import {HOTEL_SERVICE_MODULE_NAME, modulePermission} from '@app/utils/permissions';

const {Option} = Select;

interface Receipt {
  chargeAmount: number;
  refNumber: string;
  creditApplicable?: boolean;
  id: number;
  receiptNumber: string;
  amount: number;
  numericAmount: number;
  createdAt: string;
  paidAmount: any;
  invoiceId: string;
  currencyId?: string;
  currencyPrefix?: string;
  guestId?: string;
  receiptStatus: string;
}

interface ICreditNoteData {
  prefix: string;
  recieptId: boolean;
  receiptStatus: string;
  paidStatus?: boolean;
  hasAttachment: any;
  currencyPrefix: any;
  type?: string;
  invoiceType?: string;
  registryType?: string;
  proformaInvoiceNumber?: any;
  reservationRefNumber?: any;
  key: number;
  id: number;
  receiptNo: number;
  date: string;
  proformaInvoiceNo: number;
  reservationNo: number;
  reservationId: number;
  refNumber: string;
  amount: number;
  paymentMethod: any;
  receipt: any;
  paymentStatus?: string;
  rowData: any;
  methodOfCredit: string;
  creditNoteNo: string;
  expiredDate: string;
  hasInvoiceCreditNote: boolean;
  reservationStatus: string;
}

interface Props {
  reloadData: () => void;
  rowData: ICreditNoteData;
  onSuccess?: (pendingEmailId: number, reservationId: number) => void;
  onClose?: () => void;
}

const CreditPayment: React.FC<Props> = ({reloadData, rowData, onSuccess, onClose}) => {
  const dispatch = useAppDispatch();
  const [isButtonDisabled, setIsButtonDisabled] = useState<boolean>(true);
  const hotelConfig = useAppSelector(state => state.hotelSlice.hotelConfig);
  const loading = useAppSelector(state => state.commonSlice.loading);
  const [receiptData, setReceiptData] = useState<Receipt[]>([]);
  const [selectedReceipts, setSelectedReceipts] = useState<string[]>([]);
  const [selectedReceiptData, setSelectedReceiptData] = useState<
    {
      amount: number;
      receiptId: number;
      refundType: string;
    }[]
  >([]);
  const [createNoteData, setCreateNoteData] = useState<Receipt | null>(null);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [lastSelectedReceipt, setLastSelectedReceipt] = useState<string | null>(null);
  const [balanceAmmount, setBalanceAmmount] = useState<number>(0);
  const [creditAmount, setCreditAmount] = useState<number>(0);
  const [isDisabledModal, setIsDisabledModal] = useState<boolean>(false);
  const [reservationOptions, setReservationOptions] = useState<Receipt[]>([]);
  const [totalSelectedAmount, setTotalSelectedAmount] = useState<number>(0);
  const [creditApplicable, setCreditApplicable] = useState<boolean>(false);
  const [reservationId, setReservationId] = useState<number | null>(null);
  const userPermission = useAppSelector(state => state.user.permissions);
  const permissions = modulePermission(userPermission, HOTEL_SERVICE_MODULE_NAME.DISCOUNT);
  const permissionsCreditNote = modulePermission(userPermission, HOTEL_SERVICE_MODULE_NAME.INVOICE_CREDIT_NOTE);

  useEffect(() => {
    if (totalSelectedAmount > 0 && totalSelectedAmount >= creditAmount) {
      setIsButtonDisabled(false);
    } else {
      setIsButtonDisabled(true);
    }
  }, [totalSelectedAmount]);

  const onSearch = async (value: string) => {
    try {
      const response: any = await filterRecervationNumber(hotelConfig?.hotelId, value, 'reservationNo');
      setReservationOptions(response.result.reservation);
      // Check if there are any reservations returned
      if (response.result.reservation.length > 0) {
        const reservation = response.result.reservation[0];
        setReservationId(reservation?.id);
        await refundData(reservation?.invoiceId);
        if (reservation?.creditApplicable === true) {
          setReservationOptions([]);
          setIsDisabledModal(true);
          notificationController.error({message: 'Credit note that already exists'});
          return;
        }

        if (reservation) {
          setCreateNoteData(reservation);
        } else {
          setCreateNoteData(null);
        }

        setReservationId(reservation.id);
        // Set the form fields with the correct values
        form.setFieldsValue({
          amount: convertNumberFormatWithDecimal(Number(reservation.paidAmount), 2), // Set paid amount
        });
      }
    } catch (error) {
      console.error('Error fetching reservation data:', error);
    }
  };

  // Receipt Data
  const refundData = async (invoiceId: string | undefined) => {
    try {
      const response: any = await getReciptData(invoiceId);
      if (response?.statusCode == '20000') {
        const receipts = response?.result?.receipt.map((receipt: any) => {
          const numericAmount = parseFloat(receipt.amount.replace(/[^0-9.-]+/g, ''));
          return {
            ...receipt,
            numericAmount,
          };
        });
        setReceiptData(receipts);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const [form] = Form.useForm();

  const handleClose = () => {
    reloadData();
    form.resetFields();
    dispatch(setLoading(false));
    dispatch(setModalVisible(false));
    onClose && onClose();
    setIsVisible(false);
  };

  //Submit credit note
  const onFinish = async () => {
    dispatch(setLoading(true));
    const formValues = form.getFieldsValue();

    // Constructing the payload
    const payload = {
      amount: parseFloat(formValues?.creditAmount),
      registryType: 'RESERVATION',
      guestId: createNoteData?.guestId,
      hotelId: hotelConfig?.hotelId,
      expiredDate: dayjs(formValues?.expiredDate).format('YYYY-MM-DD'),
      recieptId: selectedReceiptData.map(item => item.receiptId),
      currencyId: createNoteData?.currencyId,
      creditPaymentType: formValues?.creditMethod,
      reservationId,
    };

    try {
      const result = await postCreditNote(payload);
      if (result.statusCode === '20000') {
        notificationController.success({message: result.message});
        onSuccess && onSuccess(result.result.pendingEmail.pendingEmailId, result.result.pendingEmail.reservationId);
        handleClose();
      } else {
        notificationController.error({message: result.message});
      }
    } catch (error) {
      console.error('Error submitting refund data:', error);
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleCardClick = (receipt: Receipt) => {
    // Check if balanceAmmount is positive
    if (balanceAmmount <= 0) {
      // Unselect the card if it is currently selected

      if (selectedReceipts.includes(receipt.receiptNumber)) {
        setSelectedReceipts((prev: any) => prev.filter((num: any) => num !== receipt.receiptNumber));
        setTotalSelectedAmount(prev => prev - receipt.numericAmount);
        setBalanceAmmount(prev => prev + receipt.numericAmount);

        // Remove from selectedReceiptData
        setSelectedReceiptData(prev => prev.filter(item => item.receiptId !== receipt.id));
      }
      return;
    }

    const isCurrentlySelected = selectedReceipts.includes(receipt.receiptNumber);

    if (isCurrentlySelected) {
      // Unselect the receipt
      setSelectedReceipts((prev: any) => prev.filter((num: any) => num !== receipt.receiptNumber));
      setTotalSelectedAmount(prev => prev - receipt.numericAmount);
      setBalanceAmmount(prev => prev + receipt.numericAmount);

      if (lastSelectedReceipt === receipt.receiptNumber) {
        setLastSelectedReceipt(null);
      }

      // Update selectedReceiptData
      setSelectedReceiptData(prev => prev.filter(item => item.receiptId !== receipt.id));
    } else {
      // Select the receipt
      setSelectedReceipts((prev: any) => [...prev, receipt.receiptNumber]);
      setTotalSelectedAmount(prev => prev + receipt.numericAmount);
      setBalanceAmmount(prev => prev - receipt.numericAmount);
      setLastSelectedReceipt(receipt.receiptNumber);

      // Update selectedReceiptData
      setSelectedReceiptData(prev => [
        ...prev,
        {
          amount: balanceAmmount <= receipt.numericAmount ? balanceAmmount : receipt.numericAmount,
          receiptId: receipt?.id,
          refundType: 'FULL_REFUND',
        },
      ]);
    }
  };

  // Calculate credit amount when percentage changes
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    if ('percentage' in changedValues) {
      const amount = parseFloat(allValues.amount?.replace(/[^0-9.-]+/g, '') || '0');
      const percentage = parseFloat(changedValues.percentage || '0');

      setSelectedReceipts([]);
      setTotalSelectedAmount(0);

      if (percentage <= 0 || percentage > 100) {
        form.setFieldValue('creditAmount', '');
        setBalanceAmmount(0);
      } else {
        const creditAmountVal = ((amount * percentage) / 100).toFixed(2);
        setCreditAmount(parseFloat(creditAmountVal));
        form.setFieldValue('creditAmount', creditAmountVal);
        setBalanceAmmount(parseFloat(creditAmountVal));
      }
    }
    if ('creditAmount' in changedValues) {
      const amount = parseFloat(allValues.amount?.replace(/[^0-9.-]+/g, '') || '0');
      const creditAmount = parseFloat(changedValues.creditAmount || '0');
      setCreditAmount(creditAmount);

      setSelectedReceipts([]);
      setTotalSelectedAmount(0);

      if (creditAmount <= 0 || creditAmount > amount) {
        setBalanceAmmount(0);
      } else {
        setBalanceAmmount(creditAmount);
      }
    }
  };

  const showModal = () => {
    setIsVisible(true);
    // Reset states when modal opens
    setSelectedReceipts([]);
    setTotalSelectedAmount(0);
  };

  const resetForm = () => {
    const reservationNo = form.getFieldValue('reservationNo');
    form.resetFields();
    form.setFieldsValue({reservationNo});
    dispatch(setLoading(false));
  };

  useEffect(() => {
    setReservationId(rowData.reservationId);
    const reservationNo = rowData.refNumber;

    form.setFieldsValue({
      reservationNo: reservationNo,
    });

    const fetchReservation = async () => {
      await onSearch(reservationNo);
    };

    isVisible && rowData && fetchReservation();
  }, [rowData, isVisible]);

  useEffect(() => {
    const selectedReservation = reservationOptions.find(reservation => reservation.refNumber === rowData.refNumber);

    if (selectedReservation?.creditApplicable) {
      setCreditApplicable(true);
      notificationController.error({message: 'Credit note that already exists'});
    } else {
      setCreditApplicable(false);
      setCreateNoteData(selectedReservation || null);
      form.setFieldsValue({
        amount: selectedReservation ? convertNumberFormatWithDecimal(Number(selectedReservation.paidAmount), 2) : '',
      });

      if (selectedReservation?.invoiceId) {
        refundData(selectedReservation.invoiceId);
      }
      setBalanceAmmount(selectedReservation?.paidAmount || 0);
    }
  }, [reservationOptions]);

  const amountValue = parseFloat(form.getFieldValue('amount')?.replace(/[^0-9.-]+/g, '') || '0');

  return (
    <div>
      <Button
        type="primary"
        icon={<CreditCardFilled />}
        disabled={
          rowData.hasInvoiceCreditNote || rowData.reservationStatus === 'CANCELLED' || !permissionsCreditNote.ADD
        }
        onClick={showModal}>
        Credit Note
      </Button>
      <Modal
        title={<span className="modal-title">Add Credit Note</span>}
        width={1000}
        onCancel={handleClose}
        open={isVisible}
        footer={[
          // eslint-disable-next-line react/jsx-key
          <Space>
            <Button
              danger
              title="Clear"
              type="ghost"
              onClick={() => {
                resetForm();
                setSelectedReceipts([]);
              }}>
              Clear
            </Button>
            <Button
              title=""
              disabled={isButtonDisabled}
              type="primary"
              loading={loading}
              onClick={() => {
                form.submit();
              }}>
              Save
            </Button>
          </Space>,
        ]}>
        <BaseForm name="stepForm" form={form} onFinish={onFinish} onValuesChange={handleFormValuesChange}>
          <Row gutter={16}>
            <Col span={6}>
              <BaseForm.Item
                label="Reservation No"
                name="reservationNo"
                rules={[{required: true, message: 'Please enter Reservation No'}]}>
                <Input placeholder="Reservation No" style={{width: '100%'}} readOnly />
              </BaseForm.Item>
            </Col>
            <Col span={6}>
              <BaseForm.Item name="amount" label="Amount">
                <CurrencyInput
                  readOnly
                  prefix={createNoteData?.currencyPrefix}
                  bordered={true}
                  style={{
                    width: '100%',
                    backgroundColor: form.getFieldValue('amount') ? '' : '#c5d3e0',
                  }}
                  placeholder="Credit Amount"
                />
              </BaseForm.Item>
            </Col>
            <Col span={6}>
              <BaseForm.Item
                name="percentage"
                label="Percentage"
                rules={[
                  {
                    pattern: /^(0|0\.\d+|[1-9]\d*(\.\d+)?)$/,
                    message: 'Invalid value',
                  },
                  {
                    validator: (_, value) => {
                      if (value && value > 100) {
                        return Promise.reject(new Error(`Percentage cannot exceed 100%`));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}>
                <Input
                  type="number"
                  min={0}
                  max={100}
                  suffix="%"
                  disabled={amountValue <= 0}
                  placeholder="Enter the Percentage"
                  style={{width: '100%'}}
                />
              </BaseForm.Item>
            </Col>
            <Col span={6}>
              <BaseForm.Item
                name="creditAmount"
                label="Credit Amount"
                rules={[
                  {required: true, message: 'Please input the amount!'},
                  {
                    validator: (_, value) => {
                      if (value && value > amountValue) {
                        return Promise.reject(new Error(`Credit Amount cannot exceed ${amountValue}`));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}>
                <CurrencyInput
                  min={0}
                  max={amountValue}
                  prefix={createNoteData?.currencyPrefix}
                  style={{width: '100%'}}
                  placeholder="Enter the Credit Amount"
                  disabled={amountValue <= 0}
                />
              </BaseForm.Item>
            </Col>
            <Col span={6}>
              <BaseForm.Item
                label="Credit Method"
                name="creditMethod"
                rules={[{required: true, message: 'Please select Credit Method'}]}>
                <Select placeholder="Select Credit Method" disabled={amountValue <= 0} style={{width: '100%'}}>
                  <Option value="CREDIT_BY_RESERVATION">Credit by Reservation</Option>
                  <Option value="CREDIT_BY_CASHORBANK">Credit by Cash/Bank</Option>
                </Select>
              </BaseForm.Item>
            </Col>
            <Col span={6}>
              <BaseForm.Item
                label="Expired Date"
                name="expiredDate"
                rules={[{required: true, message: 'Please select Expired Date'}]}>
                <DatePicker
                  style={{width: '100%'}}
                  disabledDate={current => current && current < moment().startOf('day')}
                  disabledTime={current => {
                    if (!current) return {};

                    const now = moment();
                    const isToday = current.isSame(now, 'day');

                    return {
                      disabledHours: () =>
                        isToday ? Array.from({length: 24}, (_, i) => i).filter(hour => hour < now.hour()) : [],
                      disabledMinutes: selectedHour =>
                        isToday && selectedHour === now.hour()
                          ? Array.from({length: 60}, (_, i) => i).filter(minute => minute < now.minute())
                          : [],
                      disabledSeconds: (selectedHour, selectedMinute) =>
                        isToday && selectedHour === now.hour() && selectedMinute === now.minute()
                          ? Array.from({length: 60}, (_, i) => i).filter(second => second < now.second())
                          : [],
                    };
                  }}
                  disabled={amountValue <= 0}
                />
              </BaseForm.Item>
            </Col>
          </Row>
          <div style={{display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '16px'}}>
            {receiptData?.map(receipt => {
              const isSelected = selectedReceipts.includes(receipt.receiptNumber);
              const isPending = receipt.receiptStatus === 'PENDING';

              return (
                <div key={receipt.receiptNumber}>
                  {/* <div style={{ cursor: isPending ? 'not-allowed' : 'pointer' }}> */}
                  <Card
                    className="card"
                    onClick={() => handleCardClick(receipt)}
                    // onClick={() => !isPending && handleCardClick(receipt)} // Disabled for now
                    style={{
                      backgroundColor: isSelected ? '#cde9cd' : 'white',
                      // opacity: isPending ? 0.5 : 1, // Disabled for now
                    }}
                    extra={
                      <Checkbox
                        checked={selectedReceipts.includes(receipt.receiptNumber)}
                        // disabled={isPending}
                      />
                    }>
                    <div className="card-header">
                      <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                        <h3 className="card-title">Receipt No: {receipt?.receiptNumber}</h3>
                        <FilePdfOutlined className="icon icon-pdf" />
                      </div>
                    </div>
                    <div className="card-content">
                      <div className="receipt-detail">
                        <div className="receipt-label">
                          <MoneyCollectOutlined className="icon icon-money" />
                          <span>Total Amount:</span>
                        </div>
                        <span className="receipt-value">
                          {receipt?.currencyPrefix} {convertNumberFormatWithDecimal(Number(receipt?.chargeAmount), 2)}
                        </span>
                      </div>
                      <div className="receipt-detail">
                        <div className="receipt-label">
                          <CalendarOutlined className="icon icon-calendar" />
                          <span>Date:</span>
                        </div>
                        <span className="receipt-value">
                          {receipt?.createdAt && moment(receipt.createdAt).format('YYYY-MM-DD')}
                        </span>
                      </div>
                    </div>
                  </Card>
                  {/* </div> */}
                  {isPending && (
                    <div style={{color: 'red', fontSize: '14px', marginTop: '4px', textAlign: 'center'}}>
                      This receipt has not been authorized yet
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </BaseForm>
      </Modal>
    </div>
  );
};

export default CreditPayment;
