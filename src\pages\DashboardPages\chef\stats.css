.bullet-point {
  display: flex;
  align-items: center;
  margin: 10px 0;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.5s ease-out forwards;
  background-color: aliceblue;
  padding: 10px;
  border-radius: 10px;
}

.bullet {
  display: flex;
  align-items: center;
  opacity: 0;
  transform: translateY(20px);
  justify-content: center;
  animation: fadeInUp 0.5s ease-out forwards;
  gap: 5px;
}

/* Colors for each bullet point */
.bullet-point.request-confirmation {
  color: #9d5803; /* Brownish */
}

.bullet-point.new-orders {
  color: #d97706; /* Orange */
}

.bullet-point.accepted {
  color: #096dd9; /* Blue */
}

.bullet-point.being-prepared {
  color: #0e7490; /* Teal */
}

.bullet-point.ready-to-serve {
  color: #16a34a; /* Green */
}

/* Icon styling */
.bullet-icon {
  margin-right: 8px;
  font-size: 20px;
  vertical-align: middle;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bullet-point.request-confirmation {
  animation-delay: 0.2s;
}
.bullet-point.new-orders {
  animation-delay: 0.4s;
}
.bullet-point.accepted {
  animation-delay: 0.6s;
}
.bullet-point.being-prepared {
  animation-delay: 0.8s;
}
.bullet-point.ready-to-serve {
  animation-delay: 1s;
}

.reconfirm-tables {
  display: flex;
  font-size: 12px;
  color: #9d5803;
  margin-bottom: 0.3rem;
}

.reconfirm-tables div {
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

@media (min-width: 835px) {
  .bullet-container {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: flex-start;
  }

  .bullet-point.request-confirmation {
    display: flex;
    flex-direction: column;
    align-items: 'center';
  }

  .reconfirm-tables {
    margin-left: 0;
  }
}

@media (max-width: 834px) {
  .bullet-container {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: flex-start;
    font-size: x-small;
  }
  .bullet {
    flex-direction: column;
    align-items: center;
    gap: 1px;
  }

  .numbering {
    text-align: center;
  }

  .bullet-point {
    flex-direction: column;
  }
}

@media (max-width: 500px) {
  .bullet-point {
    justify-content: space-around;
  }

  .bullet-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding-left: 20px;
  }

  .bullet {
    margin-right: 10px;
  }

  .bullet-point.request-confirmation {
    grid-column: 1 / -1;
    align-items: center;
    justify-content: center;
  }
}
