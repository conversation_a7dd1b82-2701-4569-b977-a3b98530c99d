interface StayTypeProps {
  stayType: string;
  stayTypeId: number;
  roomPriceId: number;
  roomPrice: number;
}
interface AvailableRoomCardProps {
  roomId: number;
  roomName: string;
  roomPrice: number;
  roomImageUrl: string[];
  roomTypeId: string;
  roomTypeImage: string;
  amenities: string;
  viewTypeId: string;
  viewTypeName: string;
  stayTypeId: string;
  stayTypeName: string;
  roomTypeName: string;
  stayTypeWithRoomPriceResponse: StayTypeProps[];
}

interface Props {
  handleReservation?: () => void;
  handleViewRoomDetails?: () => void;
  handlePressRoomStatus?: () => void;
}

interface ReservationResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
  pagination: IPagination;
}

interface FilterProps {
  checkedIn: string;
  checkedOut: string;
  roomTypeId: string;
  resident?: boolean;
  ageList?: number[];
  adultCount?: number;
  childCount?: number;
  channelId: number;
  showMore: boolean;
  hotelType: string;
}

interface IReservedRoomFilter {
  [x: string]: any;
  checkInDate: string;
  mainGuestFirstName: string;
  mainGuestLastName: string;
  checkOutDate: string;
  refNumber: string;
  channelName: string;
  channelTypeId: number;
  searchPayload: any;
}

interface IPagination {
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  totalRecords: number;
}

export type {AvailableRoomCardProps, Props, ReservationResponse, FilterProps, IPagination, IReservedRoomFilter};
