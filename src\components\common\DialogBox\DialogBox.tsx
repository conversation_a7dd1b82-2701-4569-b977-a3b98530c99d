import {useAppDispatch} from '@app/hooks/reduxHooks';
import {Modal} from '../Modal/Modal';
import {Button} from '../buttons/Button/Button';
import {setCurrent} from '@app/store/slices/reservationSlice';
import {cancelBlockedReservation} from '@app/api/hotel/reservation/reservation.api';
import {notificationController} from '@app/controllers/notificationController';
import {Space} from 'antd';

interface DialogBoxProps {
  showDialog: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  cancelNavigation: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  confirmNavigation: any;
  loading: boolean;
}

const DialogBox: React.FC<DialogBoxProps> = ({showDialog, cancelNavigation, confirmNavigation, loading}) => {
  return (
    <Modal
      title="Warning"
      open={showDialog}
      onCancel={cancelNavigation}
      footer={
        <Space>
          <Button title="No" onClick={cancelNavigation}>
            No
          </Button>
          <Button type="primary" loading={loading} onClick={confirmNavigation}>
            Yes
          </Button>
        </Space>
      }>
      <b>Are you sure you want to navigate?</b>
      <br /> Your reservation process will be cancelled.
    </Modal>
  );
};
export default DialogBox;
