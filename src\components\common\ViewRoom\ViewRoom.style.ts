import {media} from '@app/styles/themes/constants';
import {Card} from 'antd';
import styled from 'styled-components';

export const TopLevelCardInfo = styled.div`
  font-size: 0.8rem;
  font-weight: 500;
  color: #fff;
  text-align: center;
  margin-top: 0.5rem;
  bottom: -20px;
  position: relative;
`;

export const TopLevelCardTitle = styled.div`
  font-weight: 700;
  color: #fff;

  @media only screen and (max-width: 1400px) {
    font-size: 1rem;
  }
`;

type styleProps = {
  bgUrl?: string;
  isCatagory?: boolean;
  circleColor?: string;
};

type bgProps = {
  isCatagory: boolean;
};

export const OuterCard = styled(Card)`
  height: 6rem;
  width: 10rem;
`;

export const TopLevelCardWrap = styled(Card)`
  width: 10rem;
  height: 6rem;
  border-radius: 5px;
  background: #fff;
  padding: 1rem;
  background-size: cover !important;
  background: linear-gradient(0deg, rgb(0 0 0 / 54%), rgb(187 187 187 / 39%)),
    url(${(props: styleProps) => props.bgUrl});
  // -webkit-box-shadow: -1px 1px 23px -3px rgba(0, 0, 0, 0.37);
  // -moz-box-shadow: -1px 1px 23px -3px rgba(0, 0, 0, 0.37);
  // box-shadow: -1px 1px 23px -3px rgba(0, 0, 0, 0.37);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  flex-direction: column;
  transition: all 0.4s ease-in-out;
  background-position-x: center;

  @media only screen and (max-width: 1400px) {
    margin-right: 1.3rem;
  }

  @media only screen and (min-width: 2000px) {
    width: 20.5rem;
    height: 30rem;
    font-size: 1rem;
  }
`;

export const Wrapper = styled.div`
  text-align: center;
  // display: flex;
  flex-direction: column;
  margin-top: 0rem;
`;

export const ImgWrapper = styled.div`
  width: 6.9375rem;
  margin: 0 auto 1.25rem auto;
  display: flex;
  justify-content: center;
  border-radius: 50%;

  background: #a26658;

  @media only screen and ${media.lg} {
  }
  @media only screen and ${media.xl} {
    width: 7rem;
    margin: 0 auto 1rem auto;
  }
  @media only screen and (min-width: 2400px) {
    width: 9rem;
  }

  & > span {
    margin: 3px;
    width: calc(100% - 6px);
    height: calc(100% - 6px);

    @media only screen and ${media.xl} {
      margin: 3px;
    }
  }
`;

//   background: conic-gradient(
//     from -35.18deg at 50% 50%,
//     #006ccf -154.36deg,
//     #ff5252 24.13deg,
//     #ffb155 118.76deg,
//     #006ccf 205.64deg,
//     #ff5252 384.13deg
//   );
