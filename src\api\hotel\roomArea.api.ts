import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const CreateRoomArea = (payload: CreateRoomAreaProps): Promise<RoomAreaResponse> => {
  return instance.post<RoomAreaResponse>(HOTEL_SERVICE + 'area', payload).then(({data}) => data);
};

export const UpdateRoomArea = (payload: UpdateRoomAreaProps): Promise<RoomAreaResponse> => {
  return instance.put<RoomAreaResponse>(HOTEL_SERVICE + 'area', payload).then(({data}) => data);
};

export const getAllRoomAreas = (hotelId: number): Promise<RoomAreaResponse> =>
  instance.get<RoomAreaResponse>(HOTEL_SERVICE + `area/search?hotelId=${hotelId}`).then(({data}) => data);

export const DeleteRoomArea = (id: number): Promise<RoomAreaResponse> =>
  instance.delete<RoomAreaResponse>(HOTEL_SERVICE + `area/${id}`).then(({data}) => data);

export interface CreateRoomAreaProps {
  name: string;
  hotelId: number;
}

export interface UpdateRoomAreaProps {
  id: number;
  name: string;
  hotelId: number;
}

export interface RoomAreaResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}
