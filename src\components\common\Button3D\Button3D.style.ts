import styled from 'styled-components';

export const ButtonContainer = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
`;

export const MainButton = styled.div`
  color: #fff;
  font-family: Helvetica, sans-serif;
  font-weight: bold;
  font-size: 14px;
  text-align: center;
  background-color: #f70d1a;
  position: relative;
  padding: 5px 5px;
  text-shadow: 0px 3px 0px #000;
  box-shadow: inset 0 1px 0 #f70d1a, 0 10px 0 #c90711;
  border-radius: 10px;

  &.the-button-main-active {
    text-shadow: 0px 1px 0px #000;
    top: 10px;
    box-shadow: inset 0 0 5px #ffe5c4;
    background-color: #f94c56;
  }

  &:hover {
    cursor: pointer;
  }
`;

export const ButtonBase = styled.div`
  height: 100%;
  width: 100%;
  padding: 5px;
  position: absolute;
  bottom: -16px;
  left: -5px;
  z-index: -1;
  background-color: #2b1800;
  border-radius: 10px;
  box-shadow: 0 0 15px #000;
`;
