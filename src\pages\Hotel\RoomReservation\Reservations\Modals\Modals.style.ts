import {CloseCircleOutlined, MailOutlined} from '@ant-design/icons';
import {Upload} from '@app/components/common/Upload/Upload';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {Input} from '@app/components/common/inputs/Input/Input';
import {BASE_COLORS, FONT_SIZE, FONT_WEIGHT, media} from '@app/styles/themes/constants';
import {Button, Card, Timeline, Typography} from 'antd';
import {Switch, Select} from 'antd';

import styled, {css} from 'styled-components';

interface PropsSelect {
  $selected: boolean;
}

type OptionType = {
  value: string;
  label: string;
};

interface Props {
  $isChecked: boolean;
}
interface RoomSelectionProps {
  $selectedRoom: boolean;
  $bgImage: string;
}
interface ChangeRoomProps {
  $isExpanded: boolean;
}

type styleProps = {
  bgUrl: string;
};
interface DateProps {
  $isSelected: boolean;
}

interface RoomCardProps {
  $isKeyhandover?: boolean;
}

interface SelectedStayType {
  $isSelected: boolean;
}

export const InfoCard = styled(Card)`
  margin-bottom: 1rem;
  .ant-card-head {
    background-color: ${BASE_COLORS.lightBlue};
  }
  .ant-card-head-title {
    color: ${BASE_COLORS.primary};
  }
  .ant-card-head > .ant-card-head-wrapper > .ant-card-head-title {
    padding: 9px 0px;
  }
`;
export const BlurCardWrapper = styled.div`
  display: flex;
  margin-top: 1rem;
  flex-direction: column;
  gap: 1rem;
`;

export const FlexWrapper = styled.div`
  display: flex;
  margin-top: 1rem;
  flex-direction: row;
  gap: 1rem;
  flex-wrap: wrap;
`;

export const BlueCard = styled.div`
  border: 1px solid var(--border-color);
  width: 100%;
  border-radius: 8px;
  background-color: ${BASE_COLORS.lightBlue};
`;

export const PrimaryOutlineCard = styled.div`
  border: 1px solid ${BASE_COLORS.skyblue};
  width: 100%;
  border-radius: 8px;
  background-color: ${BASE_COLORS.lightBlue};
`;

export const FlexCard = styled.div`
  border: 1px solid var(--border-color);
  width: 45%;
  border-radius: 8px;
  background-color: ${BASE_COLORS.lightBlue};
`;

export const GreenCardWrapper = styled.div`
  display: flex;
  margin-top: 1rem;
  flex-direction: column;
  gap: 1rem;
`;
export const GreenCard = styled.div`
  border: 1px solid ${BASE_COLORS.greenBorder};
  width: 100%;
  border-radius: 8px;
  background-color: ${BASE_COLORS.thinGreen};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const CheckInCard = styled.div`
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: ${BASE_COLORS.lightBlue};
  padding: 1rem;
  justify-content: flex-end;
  align-items: center;
`;

export const StayTypeOutline = styled.div`
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: ${BASE_COLORS.lightBlue};
  margin-top: 0.75rem;
`;

export const CheckOutCard = styled.div`
  border: 1px solid var(--border-color);
  // width: 100%;
  border-radius: 8px;
  background-color: ${BASE_COLORS.lightBlue};
  padding-left: 1rem;
  padding-right: 1rem;
  justify-content: flex-start;
  align-items: center;
  display: flex;
  align-items: center;
  gap: 3rem;
  padding: 0.5rem;
`;

export const CheckInWrapper = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 2rem;
  margin-bottom: 1rem;
`;

export const Padding = styled.div`
  padding: 1rem;
`;

export const MediumPadding = styled.div`
  padding: 0.5rem;
`;
export const Title = styled(Typography.Text)`
  display: flex;
  line-height: 1.25rem;
  font-weight: ${FONT_WEIGHT.bold};
  font-size: ${FONT_SIZE.xs};
  color: ${BASE_COLORS.black};
  margin-bottom: 0.8rem;
`;
export const CardTitle = styled(Typography.Text)`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.md};
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.bold};
  margin-bottom: 0.5rem;
  align-vertical: center;
`;

export const RemarkLabel = styled.span`
  font-size: 14px;
  color: black;
`;

export const TotalRefundWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const TotalRefundText = styled(Typography.Text)`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.md};
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.bold};
  align-vertical: center;
  margin-top: 0.5rem;
  margin-bottom: 0.4rem;
`;

export const DateHeader = styled(Typography.Text)`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.xs};
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.semibold};
  margin-bottom: 0.5rem;
  align-vertical: center;
`;

export const Date = styled(Typography.Text)`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.md};
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.bold};
  margin-bottom: 0.5rem;
  align-vertical: center;
`;

export const AvailableRoomTitle = styled(Typography.Text)`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.md};
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.semibold};
  margin-bottom: 0.5rem;
  align-vertical: center;
`;

export const ChangeRoomTitle = styled.div<ChangeRoomProps>`
  display: flex;
  font-size: ${FONT_SIZE.xxs};
  color: ${BASE_COLORS.white};
  cursor: pointer;
  background-color: var(--primary-color);
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
  border-radius: 100px;
`;
export const ChangeRoomWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;
export const HelperIconWrapper = styled.div`
  diplay: flex;
`;
export const ConfirmationTitle = styled(Typography.Text)`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.xs};
  color: ${BASE_COLORS.black};
  font-weight:${FONT_WEIGHT.thin}
  cursor: pointer;
`;
export const Label = styled(Typography.Text)`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.xs};
  color: ${BASE_COLORS.gray};
  // font-weight: ${FONT_WEIGHT.bold};
  margin-bottom: 0.4rem;
`;
export const BoldLabel = styled(Typography.Text)`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.md};
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.bold};
`;

export const AmenitiesSection = styled.div`
  display: -webkit-box;
  flex-direction: row;
  gap: 10px;
  margin-top: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 0.75rem;
`;
export const AmenitiesOutline = styled.div`
  display: flex;
  border: 1px solid #006650;
  padding-left: 0.3rem;
  padding-right: 0.3rem;
  // padding-top: 0.3rem;
  // padding-bottom: 0.3rem;
  border-radius: 3px;
  justify-content: center;
  align-items: center;
  gap: 0.3rem;
`;
export const Amenities = styled.div`
  font-size: ${FONT_SIZE.xsm};
  color: #006650;
  font-weight: ${FONT_WEIGHT.semibold};
`;
export const LabelWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;
export const LabelRow = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 2rem;
`;
export const LeftWrapper = styled.div`
  display: flex;
`;

export const RightWrapper = styled.div`
  display: flex;
  gap: 2rem;
`;
export const TitleWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;
export const ImageOutline = styled.div<Props>`
  border: 3px solid ${props => (props.$isChecked ? BASE_COLORS.darkGreen : BASE_COLORS.white)};
  background-color: ${props => (props.$isChecked ? BASE_COLORS.thinGreen : BASE_COLORS.white)};
  height: 3.2rem;
  width: 10rem;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-radius: 7px;
  cursor: pointer;
`;
export const KeyImage = styled.img`
  height: 6rem;
  width: 6rem;
`;

export const CancelImage = styled.img`
  height: 5rem;
  width: 5rem;
`;
export const PrefixIcon = styled.img`
  height: 1.75rem;
  width: 1.75rem;
`;
export const ExpandableSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

export const StayExpandableSection = styled.div`
  display: flex;
  flex-direction: column;
`;
export const RoomContainer = styled.div`
  display: flex;
  gap: 1rem;
  padding-bottom: 0.5rem;
`;

export const RoomOutlineWrap = styled.div<RoomSelectionProps>`
  width: 10rem;
  height: 6.75rem;
  display: flex;
  cursor: pointer;

  background: #fff;
  justify-content: center;
  align-items: center;

  border-radius: 9px;

  ${props =>
    props.$selectedRoom
      ? css`
          -webkit-box-shadow: 0px 0px 17px -4px rgba(0, 0, 0, 0.53);
          -moz-box-shadow: 0px 0px 17px -4px rgba(0, 0, 0, 0.53);
          box-shadow: 0px 0px 17px -4px rgba(0, 0, 0, 0.53);
        `
      : css``}
  &:hover {
    -webkit-box-shadow: 0px 0px 17px -4px rgba(0, 0, 0, 0.53);
    -moz-box-shadow: 0px 0px 17px -4px rgba(0, 0, 0, 0.53);
    box-shadow: 0px 0px 17px -4px rgba(0, 0, 0, 0.53);
  }
`;
export const RoomOutline = styled.div<RoomSelectionProps>`
  display: flex;

  padding: 1rem;
  cursor: pointer;
  border-radius: 7px;
  flex-direction: column;
  background-color: ;
  justify-content: center;
  align-items: center;
  border: ${props => (props.$selectedRoom ? '5px solid #4c9a50' : '0px solid #91caff')};
  background: url(${props => props.$bgImage});
  background-size: cover;
  background-repeat: no-repeat;
  width: 10rem;
  height: 6.75rem;
`;

export const RoomName = styled.div`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.md};
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.bold};
  // margin-bottom: 1rem;
`;

export const RoomTitle = styled.div`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.md};
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.bold};
  margin-bottom: 1rem;
`;
export const ViewName = styled(Typography.Text)`
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.xs};
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.semibold};
`;
export const ViewTypeName = styled(Typography.Text)`
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.xxs};
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.semibold};
`;
export const ButtonSection = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 0.5rem;
`;

export const RoomCard = styled(Card)`
  height: 8rem;
  width: 8rem;
  border-radius: 12px;
`;

export const TopLevelCardWrap = styled.div`
  height: 8rem;
  width: 8rem;
  background: #fff;
  padding: 1rem;
  border-radius: 12px;
  background-size: cover !important;
  background: linear-gradient(0deg, rgb(0 0 0 / 54%), rgb(187 187 187 / 39%)),
    url(${(props: styleProps) => props.bgUrl});
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  flex-direction: column;
  transition: all 0.4s ease-in-out;

  @media only screen and (max-width: 1400px) {
    margin-right: 1.3rem;
  }

  @media only screen and (min-width: 2000px) {
    width: 20.5rem;
    height: 30rem;
    font-size: 1rem;
  }
`;

export const Wrapper = styled.div`
  text-align: center;
  display: flex;
  flex-direction: column;
  margin-top: 2.3rem;
`;

export const TopLevelCardTitle = styled.div`
  font-weight: 700;
  color: #fff;

  @media only screen and (max-width: 1400px) {
    font-size: 1rem;
  }
`;
export const ButtonWrapper = styled.div`
  display: flex;
  gap: 1rem;
  flex-direction: row;
  justify-content: flex-end;
`;

export const RightButtonWrapper = styled.div`
  display: flex;
  gap: 1rem;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
`;
export const KeyHandOverWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 1.5rem;
`;
export const MultiCheckInCircle = styled.div`
  height: 40px;
  width: 40px;
  background-color: ${BASE_COLORS.lightgreen};
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px 0 10px;
  gap: 0.5rem;
  border-radius: 50%;
`;
export const CustomTimeline = styled(Timeline)`
  .ant-timeline-item-tail {
    border-left: 2px solid ${BASE_COLORS.lightgreen};
  }
  .ant-timeline-item {
    padding: 0px;
  }
`;
export const CheckinTitle = styled.div`
  font-size: ${FONT_SIZE.xs};
  font-weight: ${FONT_WEIGHT.semibold};
`;
export const StaytypeBox = styled.div`
  background-color: ${BASE_COLORS.lightBlue};
  display: flex;
`;

export const NightCount = styled.div`
  color: ${BASE_COLORS.black};
`;

export const CheckBoxWrapper = styled.div`
  display: flex;
  justify-content: flex-start;
  gap: 1rem;
  margin-top: 3rem;
  margin-right: 1rem;
`;

export const DateWrapper = styled.div`
  display: flex;
  flex-direction: row;
`;

export const DateOutline = styled.div<DateProps>`
  display: flex;
  flex-direction: column;
  border: 1px solid black;
  padding-right: 5px;
  padding-left: 5px;
  background-color: ${props => (props.$isSelected ? BASE_COLORS.white : BASE_COLORS.white)};
  border-color: ${props => (props.$isSelected ? BASE_COLORS.redBorder : 'var(--border-color)')};
  cursor: pointer;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  height: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;

  ${props =>
    props.$isSelected &&
    css`
      position: relative;
      &:before,
      &:after {
        content: '';
        position: absolute;
        width: 25px;
        height: 5px;
        background-color: ${BASE_COLORS.redBorder};
      }

      &:before {
        transform: rotate(45deg);
      }

      &:after {
        transform: rotate(-45deg);
      }
    `};
`;

export const MonthText = styled(Typography)`
  font-size: ${FONT_SIZE.xxs};
`;

export const DateText = styled(Typography)`
  font-size: ${FONT_SIZE.lg};
  // font-weight: ${FONT_WEIGHT.bold};
`;

export const CancellationReduction = styled(Typography)`
  font-size: ${FONT_SIZE.xxs};
  color: ${BASE_COLORS.redBorder};
`;

export const DateFlexWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
`;
export const MultiCheckInButton = styled.div`
  display: flex;
  margin-top: 1rem;
  justify-content: flex-end;
  align-item: center;
  padding-bottom: 1rem;
`;
export const CheckedInRoomsWrapper = styled.div`
  padding-bottom: 1rem;
`;
export const FormWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-direction: column;
  gap: 1rem;
`;

export const BaseFormWrapper = styled.div`
  display: flex;
  width: 100%;
`;

export const ReasonInput = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;
export const ButtonFlex = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
`;
export const DateOuter = styled.div`
  display: flex;
`;
export const DateTitle = styled(Typography)`
  font-size: ${FONT_SIZE.xs};
`;
export const EditIcon = styled.div`
  color: blue;
`;
export const RemarkContainer = styled.div`
  display: flex;
  flex-direction: row;
  column-gap: 0.5rem;
  align-items: center;
`;
export const CancellationTermsTitle = styled(Typography)`
  font-size: ${FONT_SIZE.xl},
  font-weight:${FONT_WEIGHT.bold}
`;

export const TotalDeductionAmount = styled(Typography)`
font-size: ${FONT_SIZE.xl},
font-weight:${FONT_WEIGHT.bold},
color: ${BASE_COLORS.red}
`;

export const TotalAmountWrapper = styled.div`
  display: flex;
  background-color: #ffcccc;
  margin-top: 1rem;
  padding: 1rem;
  justify-content: flex-end;
  font-weight: ${FONT_WEIGHT.bold};
`;

export const RedCard = styled.div`
  border: 1px solid ${BASE_COLORS.redBorder};
  width: 100%;
  border-radius: 8px;
  background-color: ${BASE_COLORS.thinRed};
`;
export const RedCardWrapper = styled.div`
  display: flex;
  margin-top: 1rem;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
`;

export const PolicyDesc = styled(Typography.Text)`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.xs};
  color: ${BASE_COLORS.black};
`;
export const EmptyFooterSpace = styled.div`
  height: 1rem;
`;
export const CheckInDate = styled.div`
  font-size: ${FONT_SIZE.xs};
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.semibold};

  margin-top: 0.75rem;
`;

export const RightTopWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: -30px;
`;

export const LeftTopWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  gap: 1rem;
  width: 100%;
`;

export const RoomChangeWrapper = styled.div``;

export const RoomChangeOutline = styled.div<RoomCardProps>`
  background-color: ${props => (props.$isKeyhandover ? BASE_COLORS.gray : BASE_COLORS.primary)};
  color: ${BASE_COLORS.white};
  font-size: ${FONT_SIZE.xxs};
  font-weight: ${FONT_WEIGHT.semibold};
  padding: 2px 10px 2px 10px;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  pointer-events: ${props => (props.$isKeyhandover ? 'none' : 'auto')};
  cursor: ${props => (props.$isKeyhandover ? 'not-allowed' : 'pointer')};
`;
export const RoomChangeButton = styled.div<{isDisabled?: boolean}>`
  display: flex;
  flex-direction: row;
  border: 1px solid ${props => (!props.isDisabled ? BASE_COLORS.primary : BASE_COLORS.lightGray)};
  border-radius: 3px;
  gap: 5px;
  background-color: ${BASE_COLORS.yellow};
`;

export const MailBackgroundButton = styled.div`
  border: 1px solid ${BASE_COLORS.yellow};
  border-radius: 3px;
  gap: 5px;
  background-color: ${BASE_COLORS.yellow};
`;

export const MailWrapper = styled.div`
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: center;
  margin-bottom: 5px;
`;

export const RoomNameWrapper = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
`;
export const SelectedText = styled.div`
  font-size: ${FONT_SIZE.xxs};
  font-weight: ${FONT_WEIGHT.bold};
  background-color: ${BASE_COLORS.darkGreen};
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 3px;
  padding-bottom: 3px;
  color: ${BASE_COLORS.white};
  bottom: 0;
  width: 100%;
`;
export const CardWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;
export const CustomCard = styled(Card)`
  .ant-card-body {
    // padding: 2px;
    height: 5rem;
    padding-left: 0.5rem;
    padding-top: 0.5rem;
  }
`;
export const StayTitle = styled.div<SelectedStayType>`
  display: flex;
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.xs};
  color: ${props => (props.$isSelected ? BASE_COLORS.white : BASE_COLORS.black)};
  // font-weight: ${FONT_WEIGHT.bold};
  margin-top: 0.25rem;
  gap: 0.3rem;
  // align-items: center;
  // justify-content: center;
`;
export const TitleIconWrapper = styled.div`
  display: flex;
  gap: 1px;
  align-items: center;
  justify-content: center;
`;

export const SelectPickerWrapper = styled.div`
  display: flex;
  width: 100%;
  justify-content: space-between;
  flex-direction: row;
  align-items: center;
`;
export const BaseFormTitle = styled(Typography.Text)`
  position: relative;
  align-items: center;
  max-width: 100%;
  height: 50px;
  color: var(--primary-color);
  font-size: 14px;
`;
export const StayTypeName = styled.div<SelectedStayType>`
  display: flex;
  border: 1px solid ${props => (props.$isSelected ? BASE_COLORS.darkGreen : '#b6c5fd')};
  background-color: ${props => (props.$isSelected ? BASE_COLORS.darkGreen : '#b6c5fd')};
  margin: 0.5rem;
  justify-content: center;
  align-items: center;
  font-size: ${FONT_SIZE.xs};
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 5px;
  cursor: pointer;
  align-self: center;
`;
export const CloseIcon = styled(CloseCircleOutlined)`
  cursor: pointer;
  color: ${BASE_COLORS.redBorder};
`;
export const UpdateGuestWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  align-item: center;
`;

export const HorizontalLine = styled.div`
  border-bottom: 1px solid var(--border-color);
`;

export const PriceSection = styled.div`
  display: flex;
  flex-direction: column;
  background-color: ${BASE_COLORS.lightBlue};
  align-items: center;
  padding: 0.75rem;
  gap: 0.5rem;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
`;

export const PriceWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`;
export const PriceHeader = styled(Typography.Text)`
  display: flex;
  line-height: 1.25rem;
  font-size: 16px;
  color: ${BASE_COLORS.black};
  font-weight: ${FONT_WEIGHT.bold};
`;
export const TaxWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  gap: 0.5rem;
  width: 100%;
`;
export const WhiteCard = styled.div`
  border: 1px solid var(--border-color);
  width: auto;
  border-radius: 8px;
  margin-top: 1rem;
`;
export const CancellationTitle = styled(Typography.Text)`
  display: flex;
  line-height: 24px;
  font-weight: ${FONT_WEIGHT.semibold};
  font-size: ${FONT_SIZE.md};
  color: ${BASE_COLORS.black};
  margin-bottom: 0.3rem;
`;

export const CancellationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const UndoButton = styled(Typography.Text)`
  display: flex;
  line-height: ${FONT_SIZE.xsm};
  font-weight: ${FONT_WEIGHT.semibold};
  font-size: ${FONT_SIZE.xs};
  color: ${BASE_COLORS.primary};
  cursor: pointer;
`;
export const StyledUpload = styled(Upload)`
  && .ant-upload-list-item-card-actions-btn.ant-btn-sm {
    display: none;
  }
`;
export const RoomStatus = styled.div<{bgColor: string}>`
  background-color: ${props => props.bgColor};
  padding: 3px 8px 3px 8px;
  border-radius: 5px;
`;

export const RoomStatusText = styled(Typography)<{color: string}>`
  color: ${props => props.color};
  font-size: ${FONT_SIZE.xs};
`;

export const NoDatesAvalialble = styled(Typography)`
  font-size: ${FONT_SIZE.md};
  margin-top: 1rem;
  color: ${BASE_COLORS.checkOutRed};
`;
export const ActionButton = styled.div<{$bgColor: string; $disabled: boolean}>`
  height: 2rem;
  padding-left: 10px;
  padding-right: 10px;
  background-color: ${props => (props.$disabled ? BASE_COLORS.gray : props.$bgColor)};
  color: ${BASE_COLORS.white};
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  cursor: ${props => (props.$disabled ? 'not-allowed' : 'pointer')};
  align-self: center;
  min-width: 5.6rem;
  position: relative;
  pointer-events: ${props => (props.$disabled ? 'none' : 'auto')};
`;
export const ActionWraper = styled.div`
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 5px;
  position: relative;
`;
export const InfoButton = styled.div<{$disabled: boolean}>`
  height: 2rem;
  padding-left: 8px;
  padding-right: 8px;
  background-color: ${props => (props.$disabled ? BASE_COLORS.gray : BASE_COLORS.skyblue2)};
  cursor: ${props => (props.$disabled ? 'not-allowed' : 'pointer')};
  pointer-events: ${props => (props.$disabled ? 'none' : 'auto')};
  color: ${BASE_COLORS.white};
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  align-self: center;
  min-width: 2.8rem;
  max-width: 2.8rem;
`;
export const CancelButton = styled.div<{$disabled: boolean}>`
  height: 2rem;
  padding-left: 8px;
  padding-right: 8px;
  background-color: ${props => (props.$disabled ? BASE_COLORS.gray : BASE_COLORS.cancelRed)};
  cursor: ${props => (props.$disabled ? 'not-allowed' : 'pointer')};
  pointer-events: ${props => (props.$disabled ? 'none' : 'auto')};
  color: ${BASE_COLORS.white};
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  align-self: center;
`;
export const ReservationDetailsContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  gap: 2rem;
  margin-left: 1rem;
`;
export const ReservationNumber = styled(Typography)`
  font-size: 12px;
`;
export const InnerRow = styled.div`
  display: flex;
  flex-direction: column;
`;
export const SendMailContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  gap: 1rem;
  padding: 10px 10px 10px 10px;
`;
export const ReservationMailContainer = styled.div`
  display: flex;
  width: 50%;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border: 1px solid var(--border-color);
  gap: 0.5rem;
`;
export const PaymentMailContainer = styled.div`
  display: flex;
  width: 50%;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border: 1px solid var(--border-color);
  padding: 10px 10px 10px 10px;
  gap: 0.5rem;
`;
export const InfoMessage = styled(Typography)`
  font-size: 14px;
  text-align: center;
`;
export const MailIcon = styled(MailOutlined)`
  cursor: pointer;
  color: ${BASE_COLORS.checkOutRed};
  font-size: 20px;
`;
export const ResendButton = styled.div<{$bgColor: string; $disabled: boolean}>`
  height: 2rem;
  padding-left: 5px;
  padding-right: 5px;
  background-color: ${props => (props.$disabled ? BASE_COLORS.gray : props.$bgColor)};
  pointer-events: ${props => (props.$disabled ? 'none' : 'auto')};
  color: ${BASE_COLORS.white};
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  cursor: pointer;
  align-self: center;
  min-width: 4rem;
  position: relative;
`;

export const PaymentOptionWrapper = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
`;
export const PaymentOptionWrapper2 = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  justify-content: space-evenly;
`;
export const PaymentOptionOutline = styled.div<PropsSelect>`
  display: flex;
  height: 6rem;
  width: 7rem;
  border: ${props => (props.$selected ? '1px solid var(--primary-color)' : '1px solid var(--border-color)')};
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  flex-direction: column;
  background-color: ${BASE_COLORS.white};
  cursor: pointer;
  transition: box-shadow 0.3s ease;
  box-shadow: ${props => (props.$selected ? '0 0 8px var(--ant-primary-color-hover)' : 'none')};

  &:hover {
    box-shadow: ${props => (props.$selected ? '0 0 8px var(--ant-primary-color-hover)' : '0 0 4px rgba(0, 0, 0, 0.1)')};
  }
`;

export const Image = styled.img`
  height: 4rem;
  width: 4rem;
`;
export const PaymentTypeContainer = styled.div`
  background-color: ${BASE_COLORS.skyblue};
  height: 2rem;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
`;
export const PaymentType = styled(Typography.Text)<PropsSelect>`
  font-size: ${FONT_SIZE.xs};
  color: ${props => (props.$selected ? BASE_COLORS.primary : BASE_COLORS.black)};
  margin-top: 5px;
`;
export const PaymentOptionSection = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;
export const FormContent = styled.div``;
export const PayButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
`;
export const ReserveInfoWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
`;
export const ResidencyChangeSection = styled.div`
  display: flex;
`;
export const ValidationMessageWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 0.5rem;
`;
export const ValidationMessage = styled(Typography)`
  font-size: ${FONT_SIZE.xs};
  color: ${BASE_COLORS.red};
  gap: 1rem;
  margin-left: 0.5rem;
`;
export const ExtrachildWrapper = styled.div`
  display: flex;
  gap: 10px;
`;
export const ExtraChildText = styled.span`
  font-size: 1rem;
`;
export const BulkCancelWrapper = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-top: 1rem;
`;
export const SelectallText = styled.span`
  font-size: 14px;
`;
export const CheckboxLabel = styled.span`
  color: ${BASE_COLORS.primary};
  font-size: 14px;
  margin-bottom: 10px;
`;
export const CustomDisableInput = styled(Input)`
  &&.ant-input-affix-wrapper-disabled .ant-input[disabled] {
    color: black;
  }
  color: ${BASE_COLORS.black};
`;

export const Btn = styled(Button)`
  width: 100%;
  height: revert;
  font-weight: 500;
  padding: 6px 16px;
  font-size: 14px;
  border-radius: 6px;
`;

export const ToggleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;
`;

export const ToggleLabel = styled(Typography.Text)`
  font-size: ${FONT_SIZE.md};
  font-weight: ${FONT_WEIGHT.semibold};
  color: ${BASE_COLORS.black};
`;

export const ToggleSwitch = styled(Switch)`
  background-color: ${props => (props.checked ? BASE_COLORS.lightgreen : BASE_COLORS.gray)}!important;
`;

export const ToggleSelect = styled(Select<string, OptionType>)`
  width: 50%;
`;

export const ToggleInput = styled(Input)`
  width: 100%;
`;

export const SummaryWrapper = styled.div`
  margin-top: 20px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      padding: 11px;
      text-align: left;
      font-size: 12px;
      color: #333;
    }

    th {
      background-color: #f0f0f0;
      font-weight: bold;
    }

    .red-class-td {
      color: red;
    }

    .red-class {
      color: red;
      font-weight: bold;
    }
  }
`;
