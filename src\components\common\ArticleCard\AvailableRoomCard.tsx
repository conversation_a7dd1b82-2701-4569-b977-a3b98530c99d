import React, {useEffect, useState} from 'react';
import {ITag} from '../Tag/Tag';
import * as S from './AvailableRoomCard.styles';
import {TbArrowsExchange, TbLiveView} from 'react-icons/tb';
import {Checkbox} from '../Checkbox/Checkbox';
import {Option} from '../selects/Select/Select';
import {MenuProps} from 'antd';
import {Dropdown} from '../Dropdown/Dropdown';
import {Button} from '../buttons/Button/Button';
import {DownOutlined, RightOutlined, UserOutlined} from '@ant-design/icons';
import {MdChildCare} from 'react-icons/md';
import {BASE_COLORS} from '@app/styles/themes/constants';
import {getAllStaytype} from '@app/api/resturant/tablecategory/stayType.api';
import {useAppSelector} from '@app/hooks/reduxHooks';
import {useDispatch} from 'react-redux';
import {setRoomDetails, setSelectedRooms} from '@app/store/slices/reservationSlice';
import {ISelectedRooms} from '@app/store/slices/reservationSlice';
import {isEmpty} from 'lodash';

interface ArticleCardProps {
  name?: React.ReactNode;
  imgUrl: string;
  amount: number;
  description: string;
  avatar?: string;
  tags?: ITag[];
  className?: string;
  view: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  stayTypes: any;
  roomId: number;
  handleReservation?: () => void;
  handleViewRoomDetails?: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  room: any;
}

interface StayTypes {
  roomPrice: number;
  roomPriceId: number;
  stayType: string;
  stayTypeId: number;
  maxChildren: number;
  maxAdults: number;
}

export const AvailableRoomCard: React.FC<ArticleCardProps> = ({
  imgUrl,
  amount,
  name,
  className = 'article-card',
  handleReservation,
  handleViewRoomDetails,
  view,
  stayTypes,
  roomId,
  room,
}) => {
  const [visible, setvisible] = useState(false);
  const [selectedStayType, setselectedStayType] = useState<StayTypes>(stayTypes[0]);
  const [selectedChildCount, setselectedChildCount] = useState(0);
  const [selectedAdultCount, setselectedAdultCount] = useState(stayTypes[0]?.maxAdults);
  const {selectedRooms} = useAppSelector(state => state.reservationSlice);
  const dispatch = useDispatch();

  const openModal = () => {
    setvisible(true);
  };

  const closeModal = () => {
    setvisible(false);
  };

  const handleFilter = (value: StayTypes) => {
    setselectedStayType(value);
  };

  const handleChildCount = (value: number) => {
    setselectedChildCount(value);
  };
  const handleAdultCount = (value: number) => {
    setselectedAdultCount(value);
  };

  const handleSelectRooms = () => {
    const data = {
      stayTypeId: selectedStayType.stayTypeId,
      roomId: roomId,
      guest_id: 1,
      stayTypeName: selectedStayType?.stayType,
      price: selectedStayType?.roomPrice,
      noOfAdults: selectedAdultCount,
      noOfChildren: selectedChildCount,
      room: {
        roomId: roomId,
        roomName: name,
        roomImage: imgUrl,
        guest: {},
      },
    };

    const existingSelectedRooms = Array.isArray(selectedRooms) ? [...selectedRooms] : [];
    const roomExists = existingSelectedRooms.some(room => room.roomId === data.roomId);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let updatedSelectedRooms: any;
    if (roomExists) {
      updatedSelectedRooms = existingSelectedRooms.filter(room => room.roomId !== data.roomId);
    } else {
      updatedSelectedRooms = [...existingSelectedRooms, data];
    }

    dispatch(setSelectedRooms(updatedSelectedRooms));
  };

  const renderChildCount = () => {
    const {maxChildren}: any = selectedStayType;
    const roomTypeFilters = [];

    for (let i = 0; i < maxChildren; i++) {
      roomTypeFilters.push(
        <S.RoomTypeFilterOutline
          onClick={() => handleChildCount(i + 1)}
          selected={selectedChildCount === i + 1}
          key={i}>
          <S.RoomTypeFilterLabel $selected={selectedChildCount === i + 1}>{i + 1}</S.RoomTypeFilterLabel>
        </S.RoomTypeFilterOutline>,
      );
    }

    return roomTypeFilters;
  };

  const renderAdultCount = () => {
    const {maxAdults}: any = selectedStayType;
    const adultCounts = [];

    for (let i = 0; i < maxAdults; i++) {
      adultCounts.push(
        <S.RoomTypeFilterOutline
          onClick={() => handleAdultCount(i + 1)}
          selected={selectedAdultCount === i + 1}
          key={i}>
          <S.RoomTypeFilterLabel $selected={selectedAdultCount === i + 1}>{i + 1}</S.RoomTypeFilterLabel>
        </S.RoomTypeFilterOutline>,
      );
    }
    return adultCounts;
  };

  const handleRoomDetails = () => {
    dispatch(setRoomDetails(room));
  };

  return (
    <S.Wrapper className={className}>
      <S.RoomSection>
        <S.LeftContainer>
          <S.RoomImage src={imgUrl} alt="article" preview={false} />
          <S.AuthorWrapper>
            <S.PriceRow>
              <S.PriceLabel>LKR. {selectedStayType?.roomPrice}</S.PriceLabel>
              <S.MaxPersonRow>
                <S.AdultContainer>
                  <S.NumberofAdults>
                    {selectedAdultCount} of {selectedStayType?.maxAdults}
                  </S.NumberofAdults>{' '}
                  <S.AdultLabel>Adults</S.AdultLabel>
                </S.AdultContainer>
                |
                <S.AdultContainer>
                  <S.NumberofAdults>
                    {selectedChildCount} of {selectedStayType?.maxChildren}
                  </S.NumberofAdults>{' '}
                  <S.AdultLabel>Child</S.AdultLabel>
                </S.AdultContainer>
              </S.MaxPersonRow>
            </S.PriceRow>

            {name && <S.Author>{name}</S.Author>}
            <S.FeatureOutlineWrapper>
              <S.FeatureWrapper>
                <S.StayTypeOption onClick={openModal}>
                  <TbArrowsExchange color={BASE_COLORS.white} />
                </S.StayTypeOption>
                <S.FeatureLable>{selectedStayType?.stayType}</S.FeatureLable>
              </S.FeatureWrapper>
            </S.FeatureOutlineWrapper>
          </S.AuthorWrapper>
        </S.LeftContainer>
        <S.RightContainer>
          <S.ReservationRow>
            <Checkbox onClick={handleSelectRooms} />
            <S.RoomDetailButton
              onClick={() => {
                handleRoomDetails();
                handleViewRoomDetails && handleViewRoomDetails();
              }}>
              Room Detail
            </S.RoomDetailButton>
          </S.ReservationRow>
        </S.RightContainer>
      </S.RoomSection>
      <S.StayTypeModal open={visible} footer={null} onCancel={closeModal}>
        <S.StayTypeContainer>
          <S.RoomImage src={imgUrl} alt="article" preview={false} />
          <S.AuthorWrapper>
            <S.PriceRow>
              <S.PriceLabel>LKR. {selectedStayType?.roomPrice}</S.PriceLabel>
              <S.MaxPersonRow>
                <S.AdultContainer>
                  <S.NumberofAdults>{selectedStayType?.maxAdults}</S.NumberofAdults> <S.AdultLabel>Adults</S.AdultLabel>
                </S.AdultContainer>
                |
                <S.AdultContainer>
                  <S.NumberofAdults>{selectedStayType?.maxChildren}</S.NumberofAdults>{' '}
                  <S.AdultLabel>Child</S.AdultLabel>
                </S.AdultContainer>
              </S.MaxPersonRow>
            </S.PriceRow>
            {name && <S.Author>{name}</S.Author>}
            <S.FeatureOutlineWrapper>
              <S.FeatureWrapper>
                <S.FeatureLable>{selectedStayType?.stayType}</S.FeatureLable>
              </S.FeatureWrapper>
            </S.FeatureOutlineWrapper>
          </S.AuthorWrapper>
        </S.StayTypeContainer>
        <S.RoomTypeFilterRow>
          {stayTypes.map((stay: StayTypes, idx: number) => {
            return (
              <S.RoomTypeFilterOutline
                onClick={() => handleFilter(stay)}
                selected={selectedStayType?.stayTypeId === stay.stayTypeId}
                key={idx}>
                <S.RoomTypeFilterLabel $selected={selectedStayType?.stayTypeId === stay.stayTypeId}>
                  {stay.stayType}
                </S.RoomTypeFilterLabel>
              </S.RoomTypeFilterOutline>
            );
          })}
        </S.RoomTypeFilterRow>
        <S.AdultLabel>Number of Adults</S.AdultLabel>
        <S.ChildPolicy>{renderAdultCount()}</S.ChildPolicy>
        {selectedStayType?.maxChildren > 0 && <S.AdultLabel>Number of Child</S.AdultLabel>}
        <S.ChildPolicy>{renderChildCount()}</S.ChildPolicy>
      </S.StayTypeModal>
    </S.Wrapper>
  );
};
