import React, {useState, useEffect} from 'react';
import addressBg from '../../../assets/restaurant/address.jpg';
import headerBg from '../../../assets/restaurant/background.jpg';
import tableModalBg from '../../../assets/restaurant/book-a-table.jpg';
import onlineReservationBg from '../../../assets/restaurant/online-reservations.jpg';
import openingHourBg from '../../../assets/restaurant/opening-hours.jpg';
import {Card, Row, Col, Tag, Button, Space, Typography, Divider as AntDivider} from 'antd';
import {
  UserOutlined,
  PhoneOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import {
  Header,
  Navigation,
  NavLinks,
  NavLink,
  ReservationButton,
  Title,
  Divider,
  Star,
  HeaderText,
  InfoSection,
  InfoCard,
  CardTitle,
  AddressText,
  DirectionsButton,
  ReservationForm,
  FormTitle,
  BookingTitle,
  SelectContainer,
  Select,
  BookNowButton,
  HoursSection,
  MealTime,
  Hours,
  Modal,
  ModalContent,
  CloseButton,
  ModalText,
  ModalForm,
  RestaurantLogo,
} from './Reservation.style';
import {useNavigate} from 'react-router-dom';

const {Title: AntTitle, Text} = Typography;

interface ReservationData {
  id: string;
  customerInfo: {
    name: string;
    phone: string;
  };
  bookingDetails: {
    guests: string;
    date: string;
    time: string;
  };
  wantPreOrder: boolean;
  preOrderItems?: any[];
  totalAmount?: number;
  status: 'confirmed' | 'pending' | 'completed' | 'cancelled';
  createdAt: string;
}

const TableReservation = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [bookingForm, setBookingForm] = useState({
    guests: '1 Person',
    date: '2025-03-06',
    time: '9:00 am',
  });
  const [reservations, setReservations] = useState<ReservationData[]>([]);

  const openModal = () => {
    setIsModalOpen(true);
  };
  const closeModal = () => setIsModalOpen(false);

  const navigate = useNavigate();

  // Load reservations from localStorage (replace with API call)
  useEffect(() => {
    const loadReservations = () => {
      // Get reservations from localStorage
      const storedReservations = localStorage.getItem('allReservations');
      if (storedReservations) {
        setReservations(JSON.parse(storedReservations));
      } else {
        // Mock data for demonstration
        const mockReservations: ReservationData[] = [
          {
            id: '1',
            customerInfo: {
              name: 'John Doe',
              phone: '+1234567890',
            },
            bookingDetails: {
              guests: '2 People',
              date: '2025-03-06',
              time: '7:00 pm',
            },
            wantPreOrder: true,
            preOrderItems: [
              {item: 'Margherita Pizza', quantity: 2, price: 15.99},
              {item: 'Caesar Salad', quantity: 1, price: 8.99},
            ],
            totalAmount: 40.97,
            status: 'confirmed',
            createdAt: '2025-03-05T10:30:00Z',
          },
          {
            id: '2',
            customerInfo: {
              name: 'Jane Smith',
              phone: '+1987654321',
            },
            bookingDetails: {
              guests: '4 People',
              date: '2025-03-07',
              time: '6:30 pm',
            },
            wantPreOrder: false,
            status: 'pending',
            createdAt: '2025-03-05T14:15:00Z',
          },
          {
            id: '3',
            customerInfo: {
              name: 'Mike Johnson',
              phone: '+1122334455',
            },
            bookingDetails: {
              guests: '3 People',
              date: '2025-03-08',
              time: '8:00 pm',
            },
            wantPreOrder: true,
            preOrderItems: [
              {item: 'Grilled Salmon', quantity: 2, price: 24.99},
              {item: 'Pasta Carbonara', quantity: 1, price: 18.99},
            ],
            totalAmount: 68.97,
            status: 'confirmed',
            createdAt: '2025-03-05T16:45:00Z',
          },
        ];
        setReservations(mockReservations);
        localStorage.setItem('allReservations', JSON.stringify(mockReservations));
      }
    };

    loadReservations();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'green';
      case 'pending':
        return 'orange';
      case 'completed':
        return 'blue';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const handleBookNow = () => {
    // Navigate to customer info page with booking details
    navigate('/toplevel/customer-info', {
      state: {bookingDetails: bookingForm},
    });
  };

  const handleModalBookNow = () => {
    // Get values from modal form
    const modalForm = {
      guests: (document.querySelector('.modal-guests') as HTMLSelectElement)?.value || '1 Person',
      date: (document.querySelector('.modal-date') as HTMLSelectElement)?.value || '2025-03-06',
      time: (document.querySelector('.modal-time') as HTMLSelectElement)?.value || '9:00 am',
    };

    navigate('/toplevel/customer-info', {
      state: {bookingDetails: modalForm},
    });
    closeModal();
  };

  return (
    <>
      <div style={{display: 'flex', flexDirection: 'column', width: '100%', overflowY: 'auto'}}>
        <Header bgImage={headerBg}>
          <Navigation>
            <NavLinks>
              <NavLink
                onClick={() => {
                  navigate('/toplevel/home');
                }}>
                HOME
              </NavLink>
              <NavLink
                onClick={() => {
                  navigate('/category/main');
                }}>
                MENUS
              </NavLink>
            </NavLinks>
            <ReservationButton onClick={() => openModal()}>FIND A TABLE</ReservationButton>
          </Navigation>

          <Title>RESERVATIONS</Title>
          <Divider>
            <Star>★</Star>
            <Star>★</Star>
            <Star>★</Star>
          </Divider>
          <HeaderText>
            For banquet inquiries, please call our direct banquet line at (************* or fax to (*************. You
            may also E-mail <NAME_EMAIL>.
          </HeaderText>
        </Header>

        <div>
          <InfoSection>
            <InfoCard bgImage={addressBg}>
              <CardTitle>ADDRESS</CardTitle>
              <AddressText>72 Madison Avenue</AddressText>
              <AddressText>(Between 27th & 28th Street)</AddressText>
              <AddressText>New York, NY 10016</AddressText>
              <AddressText>Phone: (*************</AddressText>
              <AddressText>Fax: (*************</AddressText>
              <DirectionsButton>GET DIRECTIONS</DirectionsButton>
            </InfoCard>

            <InfoCard bgImage={onlineReservationBg}>
              <FormTitle>ONLINE RESERVATION</FormTitle>
              <ReservationForm>
                <BookingTitle>BOOK A TABLE</BookingTitle>
                <SelectContainer>
                  <Select
                    value={bookingForm.guests}
                    onChange={e => setBookingForm({...bookingForm, guests: e.target.value})}>
                    <option>1 Person</option>
                    <option>2 People</option>
                    <option>3 People</option>
                    <option>4 People</option>
                    <option>5+ People</option>
                  </Select>
                </SelectContainer>
                <SelectContainer>
                  <Select
                    value={bookingForm.date}
                    onChange={e => setBookingForm({...bookingForm, date: e.target.value})}>
                    <option>2025-03-06</option>
                    <option>2025-03-07</option>
                    <option>2025-03-08</option>
                  </Select>
                </SelectContainer>
                <SelectContainer>
                  <Select
                    value={bookingForm.time}
                    onChange={e => setBookingForm({...bookingForm, time: e.target.value})}>
                    <option>9:00 am</option>
                    <option>10:00 am</option>
                    <option>11:00 am</option>
                    <option>12:00 pm</option>
                    <option>1:00 pm</option>
                  </Select>
                </SelectContainer>
                <BookNowButton onClick={handleBookNow}>BOOK NOW</BookNowButton>
              </ReservationForm>
            </InfoCard>

            <InfoCard bgImage={openingHourBg}>
              <CardTitle>OPENING HOURS</CardTitle>
              <HoursSection>
                <MealTime>LUNCH</MealTime>
                <Hours>Monday — Friday</Hours>
                <Hours>12 pm to 3:30 pm</Hours>

                <MealTime>DINNER</MealTime>
                <Hours>Monday — Saturday</Hours>
                <Hours>4 pm to 11 pm</Hours>
                <Hours>Sunday</Hours>
                <Hours>4 pm to 10 pm</Hours>
              </HoursSection>
            </InfoCard>
          </InfoSection>

          {/* Reservation Management Section */}
          <div style={{padding: '40px 20px', backgroundColor: '#f8f9fa'}}>
            <div style={{maxWidth: '1200px', margin: '0 auto'}}>
              <AntTitle level={2} style={{textAlign: 'center', marginBottom: '30px', color: '#333'}}>
                Manage Reservations
              </AntTitle>

              {reservations.length === 0 ? (
                <div style={{textAlign: 'center', padding: '60px', color: '#666'}}>
                  <Text style={{fontSize: '16px'}}>No reservations found</Text>
                </div>
              ) : (
                <Row gutter={[16, 16]}>
                  {reservations.map(reservation => (
                    <Col xs={24} sm={12} lg={8} key={reservation.id}>
                      <Card
                        hoverable
                        style={{
                          borderRadius: '12px',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                          height: '100%',
                        }}
                        title={
                          <Space>
                            <UserOutlined />
                            <Text strong>{reservation.customerInfo.name}</Text>
                            <Tag color={getStatusColor(reservation.status)}>{reservation.status.toUpperCase()}</Tag>
                          </Space>
                        }
                        extra={
                          <Text type="secondary" style={{fontSize: '12px'}}>
                            #{reservation.id}
                          </Text>
                        }>
                        {/* Customer Info */}
                        <Space direction="vertical" size="small" style={{width: '100%', marginBottom: '16px'}}>
                          <Space>
                            <PhoneOutlined style={{color: '#1890ff'}} />
                            <Text>{reservation.customerInfo.phone}</Text>
                          </Space>
                        </Space>

                        <AntDivider style={{margin: '12px 0'}} />

                        {/* Booking Details */}
                        <Space direction="vertical" size="small" style={{width: '100%', marginBottom: '16px'}}>
                          <Space>
                            <TeamOutlined style={{color: '#52c41a'}} />
                            <Text>{reservation.bookingDetails.guests}</Text>
                          </Space>
                          <Space>
                            <CalendarOutlined style={{color: '#fa8c16'}} />
                            <Text>{reservation.bookingDetails.date}</Text>
                          </Space>
                          <Space>
                            <ClockCircleOutlined style={{color: '#722ed1'}} />
                            <Text>{reservation.bookingDetails.time}</Text>
                          </Space>
                        </Space>

                        {/* Pre-order Info */}
                        {reservation.wantPreOrder && (
                          <>
                            <AntDivider style={{margin: '12px 0'}} />
                            <Space direction="vertical" size="small" style={{width: '100%'}}>
                              <Space>
                                <ShoppingCartOutlined style={{color: '#eb2f96'}} />
                                <Text strong>Pre-Order</Text>
                              </Space>
                              {reservation.preOrderItems && reservation.preOrderItems.length > 0 && (
                                <div style={{marginLeft: '20px'}}>
                                  {reservation.preOrderItems.slice(0, 2).map((item: any, index: number) => (
                                    <div key={index} style={{marginBottom: '4px'}}>
                                      <Text style={{fontSize: '12px', color: '#666'}}>
                                        {item.quantity}x {item.item} - ${item.price}
                                      </Text>
                                    </div>
                                  ))}
                                  {reservation.preOrderItems.length > 2 && (
                                    <Text style={{fontSize: '12px', color: '#999'}}>
                                      +{reservation.preOrderItems.length - 2} more items
                                    </Text>
                                  )}
                                  <div style={{marginTop: '8px'}}>
                                    <Text strong style={{color: '#1890ff'}}>
                                      Total: ${reservation.totalAmount?.toFixed(2)}
                                    </Text>
                                  </div>
                                </div>
                              )}
                            </Space>
                          </>
                        )}

                        {/* Action Buttons */}
                        <div style={{marginTop: '16px'}}>
                          <Space style={{width: '100%'}} direction="vertical">
                            <Button
                              type="primary"
                              size="small"
                              style={{width: '100%'}}
                              onClick={() => {
                                // Handle view details
                                console.log('View details for reservation:', reservation.id);
                              }}>
                              View Details
                            </Button>
                            {reservation.status === 'pending' && (
                              <Space style={{width: '100%'}}>
                                <Button
                                  type="default"
                                  size="small"
                                  style={{flex: 1}}
                                  onClick={() => {
                                    // Handle confirm reservation
                                    const updatedReservations = reservations.map(r =>
                                      r.id === reservation.id ? {...r, status: 'confirmed' as const} : r,
                                    );
                                    setReservations(updatedReservations);
                                    localStorage.setItem('allReservations', JSON.stringify(updatedReservations));
                                  }}>
                                  Confirm
                                </Button>
                                <Button
                                  danger
                                  size="small"
                                  style={{flex: 1}}
                                  onClick={() => {
                                    // Handle cancel reservation
                                    const updatedReservations = reservations.map(r =>
                                      r.id === reservation.id ? {...r, status: 'cancelled' as const} : r,
                                    );
                                    setReservations(updatedReservations);
                                    localStorage.setItem('allReservations', JSON.stringify(updatedReservations));
                                  }}>
                                  Cancel
                                </Button>
                              </Space>
                            )}
                          </Space>
                        </div>

                        {/* Created Date */}
                        <div style={{marginTop: '12px', textAlign: 'right'}}>
                          <Text type="secondary" style={{fontSize: '11px'}}>
                            Created: {new Date(reservation.createdAt).toLocaleDateString()}
                          </Text>
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>
              )}
            </div>
          </div>
        </div>
        <Modal isOpen={isModalOpen}>
          <ModalContent bgImage={tableModalBg}>
            <CloseButton onClick={closeModal}>×</CloseButton>
            <RestaurantLogo />

            <Title>BOOK A TABLE</Title>
            <Divider>
              <Star>★</Star>
              <Star>★</Star>
              <Star>★</Star>
            </Divider>

            <ModalText>
              Phone bookings: (*************. Closed Sunday night and Mondays. Lorem ipsum dolor sit amet, consectetuer
              adipiscing elit.
            </ModalText>

            <ModalForm>
              <SelectContainer style={{width: '30%'}}>
                <Select className="modal-guests" defaultValue="1 Person">
                  <option>1 Person</option>
                  <option>2 People</option>
                  <option>3 People</option>
                  <option>4 People</option>
                  <option>5+ People</option>
                </Select>
              </SelectContainer>

              <SelectContainer style={{width: '30%'}}>
                <Select className="modal-date" defaultValue="2025-03-06">
                  <option>2025-03-06</option>
                  <option>2025-03-07</option>
                  <option>2025-03-08</option>
                </Select>
              </SelectContainer>

              <SelectContainer style={{width: '30%'}}>
                <Select className="modal-time" defaultValue="9:00 am">
                  <option>9:00 am</option>
                  <option>10:00 am</option>
                  <option>11:00 am</option>
                  <option>12:00 pm</option>
                  <option>1:00 pm</option>
                </Select>
              </SelectContainer>

              <BookNowButton style={{width: '30%', margin: 0}} onClick={handleModalBookNow}>
                BOOK NOW
              </BookNowButton>
            </ModalForm>

            <Divider>
              <Star>★</Star>
              <Star>★</Star>
              <Star>★</Star>
            </Divider>
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default TableReservation;
