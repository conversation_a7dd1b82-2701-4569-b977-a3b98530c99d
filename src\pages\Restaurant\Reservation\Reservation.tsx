import React, {useState} from 'react';
import addressBg from '../../../assets/restaurant/address.jpg';
import headerBg from '../../../assets/restaurant/background.jpg';
import tableModalBg from '../../../assets/restaurant/book-a-table.jpg';
import onlineReservationBg from '../../../assets/restaurant/online-reservations.jpg';
import openingHourBg from '../../../assets/restaurant/opening-hours.jpg';
import {
  Header,
  Navigation,
  NavLinks,
  NavLink,
  ReservationButton,
  Title,
  Divider,
  Star,
  HeaderText,
  InfoSection,
  InfoCard,
  CardTitle,
  AddressText,
  DirectionsButton,
  ReservationForm,
  FormTitle,
  BookingTitle,
  SelectContainer,
  Select,
  BookNowButton,
  HoursSection,
  MealTime,
  Hours,
  Modal,
  ModalContent,
  CloseButton,
  ModalText,
  ModalForm,
  RestaurantLogo,
} from './Reservation.style';
import {useNavigate} from 'react-router-dom';

const TableReservation = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [bookingForm, setBookingForm] = useState({
    guests: '1 Person',
    date: '2025-03-06',
    time: '9:00 am',
  });

  const openModal = () => {
    setIsModalOpen(true);
  };
  const closeModal = () => setIsModalOpen(false);

  const navigate = useNavigate();

  const handleBookNow = () => {
    // Navigate to customer info page with booking details
    navigate('/toplevel/customer-info', {
      state: {bookingDetails: bookingForm},
    });
  };

  const handleModalBookNow = () => {
    // Get values from modal form
    const modalForm = {
      guests: (document.querySelector('.modal-guests') as HTMLSelectElement)?.value || '1 Person',
      date: (document.querySelector('.modal-date') as HTMLSelectElement)?.value || '2025-03-06',
      time: (document.querySelector('.modal-time') as HTMLSelectElement)?.value || '9:00 am',
    };

    navigate('/toplevel/customer-info', {
      state: {bookingDetails: modalForm},
    });
    closeModal();
  };

  return (
    <>
      <div style={{display: 'flex', flexDirection: 'column', width: '100%', overflowY: 'auto'}}>
        <Header bgImage={headerBg}>
          <Navigation>
            <NavLinks>
              <NavLink
                onClick={() => {
                  navigate('/toplevel/home');
                }}>
                HOME
              </NavLink>
              <NavLink
                onClick={() => {
                  navigate('/category/main');
                }}>
                MENUS
              </NavLink>
            </NavLinks>
            <ReservationButton onClick={() => openModal()}>FIND A TABLE</ReservationButton>
          </Navigation>

          <Title>RESERVATIONS</Title>
          <Divider>
            <Star>★</Star>
            <Star>★</Star>
            <Star>★</Star>
          </Divider>
          <HeaderText>
            For banquet inquiries, please call our direct banquet line at (************* or fax to (*************. You
            may also E-mail <NAME_EMAIL>.
          </HeaderText>
        </Header>

        <div>
          <InfoSection>
            <InfoCard bgImage={addressBg}>
              <CardTitle>ADDRESS</CardTitle>
              <AddressText>72 Madison Avenue</AddressText>
              <AddressText>(Between 27th & 28th Street)</AddressText>
              <AddressText>New York, NY 10016</AddressText>
              <AddressText>Phone: (*************</AddressText>
              <AddressText>Fax: (*************</AddressText>
              <DirectionsButton>GET DIRECTIONS</DirectionsButton>
            </InfoCard>

            <InfoCard bgImage={onlineReservationBg}>
              <FormTitle>ONLINE RESERVATION</FormTitle>
              <ReservationForm>
                <BookingTitle>BOOK A TABLE</BookingTitle>
                <SelectContainer>
                  <Select
                    value={bookingForm.guests}
                    onChange={e => setBookingForm({...bookingForm, guests: e.target.value})}>
                    <option>1 Person</option>
                    <option>2 People</option>
                    <option>3 People</option>
                    <option>4 People</option>
                    <option>5+ People</option>
                  </Select>
                </SelectContainer>
                <SelectContainer>
                  <Select
                    value={bookingForm.date}
                    onChange={e => setBookingForm({...bookingForm, date: e.target.value})}>
                    <option>2025-03-06</option>
                    <option>2025-03-07</option>
                    <option>2025-03-08</option>
                  </Select>
                </SelectContainer>
                <SelectContainer>
                  <Select
                    value={bookingForm.time}
                    onChange={e => setBookingForm({...bookingForm, time: e.target.value})}>
                    <option>9:00 am</option>
                    <option>10:00 am</option>
                    <option>11:00 am</option>
                    <option>12:00 pm</option>
                    <option>1:00 pm</option>
                  </Select>
                </SelectContainer>
                <BookNowButton onClick={handleBookNow}>BOOK NOW</BookNowButton>
              </ReservationForm>
            </InfoCard>

            <InfoCard bgImage={openingHourBg}>
              <CardTitle>OPENING HOURS</CardTitle>
              <HoursSection>
                <MealTime>LUNCH</MealTime>
                <Hours>Monday — Friday</Hours>
                <Hours>12 pm to 3:30 pm</Hours>

                <MealTime>DINNER</MealTime>
                <Hours>Monday — Saturday</Hours>
                <Hours>4 pm to 11 pm</Hours>
                <Hours>Sunday</Hours>
                <Hours>4 pm to 10 pm</Hours>
              </HoursSection>
            </InfoCard>
          </InfoSection>
        </div>
        <Modal isOpen={isModalOpen}>
          <ModalContent bgImage={tableModalBg}>
            <CloseButton onClick={closeModal}>×</CloseButton>
            <RestaurantLogo />

            <Title>BOOK A TABLE</Title>
            <Divider>
              <Star>★</Star>
              <Star>★</Star>
              <Star>★</Star>
            </Divider>

            <ModalText>
              Phone bookings: (*************. Closed Sunday night and Mondays. Lorem ipsum dolor sit amet, consectetuer
              adipiscing elit.
            </ModalText>

            <ModalForm>
              <SelectContainer style={{width: '30%'}}>
                <Select className="modal-guests" defaultValue="1 Person">
                  <option>1 Person</option>
                  <option>2 People</option>
                  <option>3 People</option>
                  <option>4 People</option>
                  <option>5+ People</option>
                </Select>
              </SelectContainer>

              <SelectContainer style={{width: '30%'}}>
                <Select className="modal-date" defaultValue="2025-03-06">
                  <option>2025-03-06</option>
                  <option>2025-03-07</option>
                  <option>2025-03-08</option>
                </Select>
              </SelectContainer>

              <SelectContainer style={{width: '30%'}}>
                <Select className="modal-time" defaultValue="9:00 am">
                  <option>9:00 am</option>
                  <option>10:00 am</option>
                  <option>11:00 am</option>
                  <option>12:00 pm</option>
                  <option>1:00 pm</option>
                </Select>
              </SelectContainer>

              <BookNowButton style={{width: '30%', margin: 0}} onClick={handleModalBookNow}>
                BOOK NOW
              </BookNowButton>
            </ModalForm>

            <Divider>
              <Star>★</Star>
              <Star>★</Star>
              <Star>★</Star>
            </Divider>
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default TableReservation;
