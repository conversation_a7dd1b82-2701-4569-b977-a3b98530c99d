import authInstance from '@app/api/authInstance';
import {LOGIN_SERVICE} from '@app/api/instance';

export const getAllHotelPermissionByServiceId = (serviceId: number): Promise<HotelPrivilegeResponse> =>
  authInstance
    .get<HotelPrivilegeResponse>(
      LOGIN_SERVICE + `api/v1/services-permission/with-initial-permission/service/${serviceId}`,
    )
    .then(({data}) => data);

export const hotelPermissionUpdate = (payload: HotelPrivilegePayload): Promise<HotelPrivilegeResponse> =>
  authInstance
    .put<HotelPrivilegeResponse>(LOGIN_SERVICE + `api/v1/services-permission`, payload)
    .then(({data}) => data);

export const hotelPermissionUpdateAll = (payload: HotelPrivilegeByHotelPayload[]): Promise<HotelPrivilegeResponse> =>
  authInstance
    .put<HotelPrivilegeResponse>(LOGIN_SERVICE + `api/v1/hotel-permission-update-all`, payload)
    .then(({data}) => data);

export const getAllHotelPermissionByHotelId = (
  hotelId: number,
  serviceId: number | '',
): Promise<HotelPrivilegeResponse> =>
  authInstance
    .get<HotelPrivilegeResponse>(LOGIN_SERVICE + `api/v1/hotel-permission/hotel/${hotelId}?serviceId=${serviceId}`)
    .then(({data}) => data);

export const hotelPermissionUpdateByHotelId = (
  payload: HotelPrivilegeByHotelPayload,
): Promise<HotelPrivilegeResponse> =>
  authInstance.put<HotelPrivilegeResponse>(LOGIN_SERVICE + `api/v1/hotel-permission`, payload).then(({data}) => data);

export const servicePermissionUpdateAll = (payload: HotelPrivilegePayload[]): Promise<HotelPrivilegeResponse> =>
  authInstance
    .put<HotelPrivilegeResponse>(LOGIN_SERVICE + `api/v1/services-permission-update-all`, payload)
    .then(({data}) => data);

export const getAllGroupsPermissionByGroupId = (
  groupsId: number,
  serviceId: number | '',
): Promise<HotelPrivilegeResponse> =>
  authInstance
    .get<HotelPrivilegeResponse>(LOGIN_SERVICE + `api/v1/groups-permission/group/${groupsId}?serviceId=${serviceId}`)
    .then(({data}) => data);

export const groupPermissionUpdateByGroupId = (
  payload: GroupPrivilegeByGroupPayload,
): Promise<HotelPrivilegeResponse> =>
  authInstance.put<HotelPrivilegeResponse>(LOGIN_SERVICE + `api/v1/groups-permission`, payload).then(({data}) => data);

  export const groupPermissionUpdateAll = (payload: GroupPrivilegeByGroupPayload[]): Promise<HotelPrivilegeResponse> =>
  authInstance
    .put<HotelPrivilegeResponse>(LOGIN_SERVICE + `api/v1/groups-permission-update-all`, payload)
    .then(({data}) => data);
    
export interface HotelPrivilegeResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface HotelPrivilegePayload {
  id: any;
  status: string;
  serviceId: number;
  permissionId: number;
}

export interface HotelPrivilegeByHotelPayload {
  id: any;
  status: string;
  hotelId: number;
  permissionId: number;
}
export interface GroupPrivilegeByGroupPayload {
  id: any;
  status: string;
  groupsId: number;
  permissionId: number;
}
