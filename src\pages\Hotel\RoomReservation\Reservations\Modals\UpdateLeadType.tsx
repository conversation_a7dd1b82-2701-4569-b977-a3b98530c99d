import React, {useEffect} from 'react';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import * as S from './Modals.style';
import {Col, Row} from 'antd';
import {FormInstance} from 'antd/es/form/Form';
import {useAppDispatch} from '@app/hooks/reduxHooks';
import {setLoading} from '@app/store/slices/commonSlice';
import {Option, Select} from '@app/components/common/selects/Select/Select';
import {updateLeadType, updateNationality} from '@app/api/hotel/reservation/reservation.api';
import {notificationController} from '@app/controllers/notificationController';
import {RESERVATION_LEAD_TYPE} from '@app/shared/constants';
import {LeadType} from '@app/pages/MasterPages/hotel/leadType/LeadTypeContent';

export interface IPaymentValues {
  amount: string;
}

interface FieldData {
  name: string | number;
  value?: any;
}

export interface MakePaymentProps {
  amount: number;
  invoiceId: number;
  paid: boolean;
  currencyId: number;
  paymentMethod?: string;
}

export interface IInvoiceData {
  id: number;
  dueAmount: number;
  currencyPrefix: string;
  currencyId: number;
}
interface Props {
  form: FormInstance;
  reloadData: () => void;
  isModalVisible: boolean;
  onCancel: () => void;
  leadType: string;
  reservationId: number;
  data: LeadType[];
}

export const UpdateLeadType: React.FC<Props> = ({
  isModalVisible,
  onCancel,
  form,
  leadType,
  reloadData,
  reservationId,
  data,
}) => {
  const [fields, setFields] = React.useState<FieldData[]>([{name: 'amount', value: undefined}]);
  const dispatch = useAppDispatch();

  const onCloseModal = () => {
    reloadData();
    form.resetFields();
    onCancel();
  };

  const create = async (reservationId: number, leadTypeId: number) => {
    try {
      const response = await updateLeadType(reservationId, leadTypeId);
      if (response.statusCode === '20000') {
        notificationController.success({message: response.message});
        await onCloseModal();
      } else {
        notificationController.error({message: response.message});
      }
    } catch (error) {}
  };

  const onFinish = () => {
    dispatch(setLoading(true));
    const formData = form.getFieldsValue();
    create(reservationId, formData.leadTypeId);
  };
  useEffect(() => {
    if (isModalVisible) {
      form.setFieldValue('leadTypeId', leadType);
    }
  }, [leadType, isModalVisible, form]);

  return (
    <S.FormContent>
      <BaseForm size="middle" form={form} initialValues={{leadType: leadType}} fields={fields} onFinish={onFinish}>
        <Row
          gutter={{xs: 10, md: 15, xl: 30}}
          style={{
            display: 'flex',
            justifyContent: 'center',
          }}>
          <Col xs={24} md={20}>
            <BaseForm.Item
              name="leadTypeId"
              label="Lead Type"
              rules={[{required: false, message: 'Lead type is required'}]}>
              <Select placeholder="Select Lead Type">
                {data &&
                  data.map(lead => (
                    <Option key={lead.id} value={lead.id}>
                      {lead.name}
                    </Option>
                  ))}
              </Select>
            </BaseForm.Item>
          </Col>
        </Row>
      </BaseForm>
    </S.FormContent>
  );
};
