import styled from 'styled-components';
import {But<PERSON>, Image, Typography} from 'antd';
import {BASE_COLORS, BORDER_RADIUS, FONT_SIZE, FONT_WEIGHT, media} from '@app/styles/themes/constants';
import {Select} from '../selects/Select/Select';
import {Modal} from '../Modal/Modal';

interface RoomTypeFilterOutlineProps {
  selected: boolean;
}

interface FilterLabelProps {
  $selected: boolean;
}

export const Header = styled.div`
  height: 5.5rem;
  margin-left: 1.5625rem;
  display: flex;
  align-items: center;
`;

export const AuthorWrapper = styled.div`
  display: flex;
  flex-direction: column;
  margin-left: 0.625rem;
  gap: 0.3rem;
  width: 18rem;
`;

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1 1 6.5rem;
  position: relative;
  max-width: 42.5rem;

  border-radius: ${BORDER_RADIUS};
  transition: 0.3s;

  [data-theme='dark'] & {
    background: var(--secondary-background-color);
  }
`;

export const Author = styled.div`
  font-size: ${FONT_SIZE.xs};
  font-weight: ${FONT_WEIGHT.bold};
  color: var(--text-main-color);
  line-height: 1.5625rem;
`;

export const InfoWrapper = styled.div`
  padding: 1.25rem;

  @media only screen and ${media.xl} {
    padding: 1rem;
  }

  @media only screen and ${media.xxl} {
    padding: 1.85rem;
  }
`;

export const InfoHeader = styled.div`
  display: flex;
  margin-bottom: 1rem;

  @media only screen and ${media.md} {
    margin-bottom: 0.625rem;
  }

  @media only screen and ${media.xxl} {
    margin-bottom: 1.25rem;
  }
`;

export const Title = styled.div`
  font-size: ${FONT_SIZE.xl};
  font-weight: ${FONT_WEIGHT.semibold};
  width: 80%;
  line-height: 1.375rem;

  color: var(--text-main-color);

  @media only screen and ${media.md} {
    font-size: ${FONT_SIZE.xxl};
  }
`;

export const DateTime = styled(Typography.Text)`
  font-size: ${FONT_SIZE.xs};
  color: var(--text-main-color);
  line-height: 1.25rem;
`;

export const RoomImage = styled(Image)`
  height: 5rem;
  width: 5rem;
  border-radius: 10px;
  // margin-left: 1rem;
`;

export const Description = styled.div`
  font-size: ${FONT_SIZE.xs};
  color: var(--text-main-color);

  @media only screen and ${media.xxl} {
    font-size: 1rem;
  }
`;

export const TagsWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.625rem;
  padding: 0 1.25rem 1.25rem;
`;

export const RoomSection = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;
export const LeftContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const StayTypeContainer = styled.div`
  display: flex;
`;
export const RightContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;
export const ReservationRow = styled.div`
  display: flex;
  // flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
`;
export const RoomDetailButton = styled.div`
  font-size: ${FONT_SIZE.xxs};
  color: var(--primary-color);
  cursor: pointer;
  font-weight: ${FONT_WEIGHT.semibold};
  @media only screen and ${media.xxl} {
    font-size: 1rem;
  }
`;
export const Btn = styled(Button)`
  display: block;
  align-self: right;
  border-radius: 2rem;
  padding-left: 0.7rem;
  padding-right: 0.7rem;
`;

export const FeatureLable = styled.div`
  font-size: ${FONT_SIZE.xxs};
  color: var(--text-main-color);

  @media only screen and ${media.xxl} {
    font-size: 1rem;
  }
`;
export const FeatureWrapper = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
`;

export const FeatureOutlineWrapper = styled.div`
  display: flex;
  gap: 1rem;
`;
export const PriceLabel = styled.div`
  font-size: ${FONT_SIZE.xs};
  color: var(--primary-color);
  font-weight: ${FONT_WEIGHT.semibold};
  @media only screen and ${media.xxl} {
    font-size: 1rem;
  }
`;
export const BtnLabel = styled.div`
  font-size: ${FONT_SIZE.xxs};
  color: var(--white);
  font-weight: none;
  @media only screen and ${media.xxl} {
    font-size: 1rem;
  }
`;
export const StayTypePicker = styled(Select)`
  width: 70%;
  &.ant-select-borderless {
    background: var(--secondary-background-color);
    border-radius: 0px;
  }

  .ant-select-selection-placeholder {
    font-size: ${FONT_SIZE.xxxs};
    color: var(--text-main-color);
  }

  .ant-select-arrow {
    color: var(--text-main-color);
    font-size: 10px;
  }
  .jogFpf .ant-input-group-addon .ant-select .ant-select-selection-item {
    min-width: 5.5rem;
    color: var(--primary-color);
    font-weight: 600;
    font-size: ${FONT_SIZE.xs};
  }

  &.ant-select-multiple.ant-select-sm .ant-select-selection-item {
    height: 0.875rem;
    line-height: ${FONT_SIZE.xs};
    font-size: ${FONT_SIZE.xs};
    margin-top: 0.1875rem;
    margin-bottom: 0.1875rem;
  }

  &.ant-select-single .ant-select-selector .ant-select-selection-item,
  .ant-select-single .ant-select-selector .ant-select-selection-placeholder {
    line-height: inherit;
    font-size: 12px;
  }

  &.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    color: var(--disabled-color);
  }
  &.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    height: 25px;
  }

  .ant-select-clear {
    color: var(--disabled-color);
  }
  .ant-select-selection-item-remove {
    color: var(--icon-color);
    &:hover {
      color: var(--icon-hover-color);
    }
  }
  .ant-select-item-option-disabled {
    color: var(--disabled-color);
  }
`;

export const PriceRow = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;
export const AdultContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;
export const StayTypeOption = styled.div`
  background-color: var(--primary-color);
  height: 17px;
  width: 20px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(0, 89, 171, 0.3);
`;
export const NumberofAdults = styled.div`
  font-size: 12px;
  margin-right: 5px;
`;
export const AdultLabel = styled.div`
  font-size: 12px;
`;
export const MaxPersonRow = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
`;
export const StayTypeModal = styled(Modal)``;
export const RoomTypeFilterOutline = styled.div<RoomTypeFilterOutlineProps>`
  display: flex;
  border: 1px solid;
  padding-left: 0.7rem;
  padding-right: 0.7rem;
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
  border-radius: 3px;
  cursor: pointer;
  background-color: ${props => (props.selected ? 'var(--primary-color)' : 'transparent')};
  border-color: ${props => (props.selected ? 'var(--primary-color);' : BASE_COLORS.lightgrey)};
  // box-shadow: 0 5px 15px rgba(0, 89, 171, 0.3);
`;
export const RoomTypeFilterLabel = styled.div<FilterLabelProps>`
  font-size: ${FONT_SIZE.xxs};
  color: ${props => (props.$selected ? BASE_COLORS.white : 'var(--text-main-color)')};
  @media only screen and ${media.xxl} {
    font-size: 1rem;
  }
`;
export const RoomTypeFilterRow = styled.div`
  display: -webkit-box;
  flex-direction: row;
  gap: 10px;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  overflow: scroll;
  flex-wrap: wrap;
  margin-top: 2rem;
`;
export const ChildPolicy = styled.div`
  display: -webkit-box;
  flex-direction: row;
  gap: 10px;
  margin-bottom: 1rem;
  overflow: scroll;
  flex-wrap: wrap;
  margin-top: 0.5rem;
`;
export const ChildrenRow = styled.div`
  display: flex;
`;
