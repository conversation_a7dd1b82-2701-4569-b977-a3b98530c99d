.ant-dropdown {
  .ant-table-filter-dropdown {
    min-width: 13rem;
    .ant-table-filter-dropdown-btns {
      border-top: none;
    }
    .ant-table-filter-dropdown-search {
      .anticon {
        color: #000000;
      }
      .ant-input-affix-wrapper {
        padding: 0.625rem;
        font-size: 0.875rem;
        .ant-input {
          font-size: 0.875rem;
          color: #404040;
          &::placeholder {
            color: #404040;
            opacity: 1;
          }
        }
      }
    }
    .ant-table-filter-dropdown-tree {
      .ant-tree-treenode-checkbox-checked .ant-tree-node-content-wrapper,
      .ant-tree-treenode-checkbox-checked .ant-tree-node-content-wrapper:hover {
        background: transparent;
      }
      .ant-checkbox-wrapper.ant-table-filter-dropdown-checkall {
        color: #01509a;
        font-size: 0.875rem;
        line-height: 1.25rem;
      }
      .ant-tree-checkbox {
        margin-top: 0;
      }
      .ant-checkbox-inner,
      .ant-tree-checkbox-inner {
        border-radius: 0.1875rem;
        height: 1.25rem;
        width: 1.25rem;
        border: 1px solid #01509a;
      }
      .ant-tree-treenode.ant-tree-treenode-switcher-open {
        font-size: 0.875rem;
        line-height: 1.25rem;
      }
      .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-open {
        .ant-tree-title {
          color: #01509a;
        }
      }
    }
  }
}
