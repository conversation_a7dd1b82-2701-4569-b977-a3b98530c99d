import React from 'react';
import * as S from './StayTypeTitle.style';
import {IoPersonSharp} from 'react-icons/io5';

interface Props {
  name?: string;
  adultCount: number;
  childCount: number;
  meal: string;
  size: string;
  isBold: boolean;
  adultSize?: number;
  childSize?: number;
  align?: string;
}

export const StayTypeTitle: React.FC<Props> = ({
  adultCount,
  childCount,
  isBold,
  meal,
  name,
  size,
  adultSize = 16,
  childSize = 12,
  align = 'center',
}) => {
  return (
    <S.StayTitle style={{marginBottom: 0, display: 'flex', justifyContent: align}} $fontSize={size} $isBold={isBold}>
      {name ? name : ''}
      {name ? ' - ' : ''}
      <S.TitleIconWrapper>
        {adultCount > 3 ? (
          <>
            {adultCount} <IoPersonSharp size={adultSize} />
          </>
        ) : (
          Array.from({length: adultCount}, (_, index) => index + 1).map(number => {
            return <IoPersonSharp size={adultSize} key={number} />;
          })
        )}

        <div hidden={childCount === 0 ? true : false}>
          {' + '}{' '}
          {childCount > 3 ? (
            <>
              {childCount} <IoPersonSharp size={childSize} />
            </>
          ) : (
            Array.from({length: childCount}, (_, index) => index + 1).map(number => {
              return <IoPersonSharp size={childSize} key={number} />;
            })
          )}
        </div>
        {' - '}
      </S.TitleIconWrapper>
      {meal === 'ROOM_ONLY' ? 'RO' : meal}
    </S.StayTitle>
  );
};
