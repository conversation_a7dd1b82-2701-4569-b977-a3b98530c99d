import React from 'react';

class FlippyCard extends React.Component {
  static defaultProps: {animationDuration: number};
  render() {
    const {className, cardType, style, elementType, animationDuration, ...rest}: any = this.props;
    return React.createElement(
      elementType || 'div',
      {
        className: `flippy-card flippy-${cardType} ${className || ''}`,
        ...rest,
        style: {
          ...(style || {}),
          ...{transitionDuration: `${animationDuration / 1000}s`},
        },
      },
      this.props.children,
    );
  }
}

export const FrontSide = ({isFlipped, style, animationDuration, ...props}: any) => (
  <FlippyCard
    {...props}
    style={{
      ...(style || {}),
    }}
    animationDuration={animationDuration}
    cardType="front"
  />
);

export const BackSide = ({isFlipped, style, ...props}: any) => (
  <FlippyCard
    {...props}
    style={{
      ...(style || {}),
    }}
    cardType="back"
  />
);

FlippyCard.defaultProps = {
  animationDuration: 600,
};
