import authInstance from '@app/api/authInstance';
import instance, {HOTEL_SERVICE} from '../instance';
import {CreateEmployeeProps} from '../employee/employee.api';

export const getAllUserDiscount = (): Promise<UserDiscountResponse> =>
  instance.get<UserDiscountResponse>(HOTEL_SERVICE + `user-discount-permission`).then(({data}) => data);

export const getAllUserDiscountById = (
  id?: string,
  searchUser?: string,
  groupId?: any,
): Promise<UserDiscountResponse> => {
  const groupIdParam = groupId || '';
  const searchUserParam = searchUser || '';
  const idParam = groupId ? '' : id || '';
  const url =
    HOTEL_SERVICE +
    `user-discount-permission?hotelId=${idParam}&groupsId=${groupIdParam}` +
    (searchUser ? `&userEmail=${searchUser}` : '');

  return instance.get<UserDiscountResponse>(url).then(({data}) => data);
};

export const getAllUserDiscountByGroupId = (groupId: string, searchUser?: string): Promise<UserDiscountResponse> =>
  instance
    .get<UserDiscountResponse>(
      HOTEL_SERVICE +
        (searchUser === ''
          ? `user-discount-permission?&groupsId=${groupId}`
          : `user-discount-permission?&userEmail=${searchUser}&groupsId=${groupId}`),
    )
    .then(({data}) => data);

export const getAllUserDiscountByUserId = (id: number, hotelId?: number): Promise<UserDiscountResponse> =>
  instance
    .get<UserDiscountResponse>(HOTEL_SERVICE + `user-discount-permission/user/${id}?hotelId=${hotelId}`)
    .then(({data}) => data);

export const CreateUserDiscount = (payload: UserDiscountPayload): Promise<UserDiscountResponse> => {
  return instance
    .post<UserDiscountResponse>(HOTEL_SERVICE + 'user-discount-permission', payload)
    .then(({data}) => data);
};

export const UpdateDiscount = (payload: any): Promise<UserDiscountResponse> => {
  return instance.put<UserDiscountResponse>(HOTEL_SERVICE + 'user-discount-permission', payload).then(({data}) => data);
};

export const DeleteEmployee = (id: number): Promise<UserDiscountResponse> =>
  instance.delete<UserDiscountResponse>(HOTEL_SERVICE + `api/v1/employee/${id}`).then(({data}) => data);

export const postRefundAmount = (payload: any): Promise<UserDiscountResponse> => {
  return instance.post<UserDiscountResponse>(HOTEL_SERVICE + 'refund', payload).then(({data}) => data);
};

export interface UserDiscountResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface UserDiscountPayload {
  userId: string;
  name: string;
  discount: string;
  hotelId: string;
  id: string;
}
