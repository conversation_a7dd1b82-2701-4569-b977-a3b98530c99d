import React, {useEffect} from 'react';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {notificationController} from '@app/controllers/notificationController';
import {Col, FormInstance, Row, Checkbox, Select, InputNumber} from 'antd';
import {Input} from '@app/components/common/inputs/Input/Input';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {setIsTouchAction, setLoading, setModalVisible} from '@app/store/slices/commonSlice';
import {CreateLicense, UpdateLicense} from '@app/api/resturant/tablecategory/license.api';
import {ILicenseData} from './ManageLicense';

interface Props {
  form: FormInstance;
  reloadData: () => void;
  rowData?: ILicenseData;
}

const featureOptions = [
  {label: 'RESTAURANT', value: 'RESTAURANT'},
  {label: 'HOTEL', value: 'HOTEL'},
];

const typeOptions = [
  {label: 'YEAR', value: 'YEAR'},
  {label: 'MONTH', value: 'MONTH'},
];

export const AddCategory: React.FC<Props> = ({form, reloadData, rowData}) => {
  const dispatch = useAppDispatch();
  const isEditAction = useAppSelector(state => state.commonSlice.isEditAction);

  useEffect(() => {
    dispatch(setIsTouchAction(false));
  }, [dispatch]);

  const onFinish = async (values: ILicenseData) => {
    dispatch(setLoading(true));
    try {
      let res;

      if (isEditAction && rowData?.id) {
        const licenseId = rowData?.id;
        const updateLicensePayload = {
          id: licenseId,
          name: values.name,
          features: values.features,
          freeTrail: values.freeTrail,
          maximumRoom: 200,
          maximumHotel: 10,
          active: values.active,
          pricePrefix: 'LKR',
          yearPrice: values.yearPrice,
          type: values.type,
          monthPrice: values.monthPrice,
          perRoomPrice: 100,
          perPropertyPrice: 20000,
          description: 'Restaurant License',
          hasTrail: true,
          numberOfDays: values.numberOfDays,
        };
        console.log('updateLicensePayload', updateLicensePayload);
        res = await UpdateLicense(updateLicensePayload);
      } else {
        const CreateLicensePayload = {
          name: values.name,
          features: values.features,
          freeTrail: values.freeTrail,
          maximumRoom: 200,
          maximumHotel: 10,
          active: values.active,
          pricePrefix: 'LKR',
          yearPrice: values.yearPrice,
          type: values.type,
          monthPrice: values.monthPrice,
          perRoomPrice: 100,
          perPropertyPrice: 20000,
          description: 'Restaurant License',
          hasTrail: true,
          numberOfDays: values.numberOfDays,
        };
        console.log('CreateLicensePayload', CreateLicensePayload);
        res = await CreateLicense(CreateLicensePayload);
      }

      if (res.statusCode === '20000') {
        notificationController.success({message: res.message});
        reloadData();
        form.resetFields();
        dispatch(setModalVisible(false));
      } else {
        notificationController.error({message: res.message});
      }
    } catch {
      notificationController.error({message: 'Something went wrong'});
    } finally {
      dispatch(setLoading(false));
    }
  };

  return (
    <BaseForm
      style={{fontFamily: 'Poppins, sans-serif'}}
      layout="vertical"
      form={form}
      onFieldsChange={() => dispatch(setIsTouchAction(true))}
      onFinish={onFinish}>
      <Row gutter={16}>
        <Col span={12}>
          <BaseForm.Item
            name="name"
            label="Plan Name"
            rules={[{required: true, message: 'Please enter the plan name'}]}>
            <Input placeholder="e.g., Starter Plan" />
          </BaseForm.Item>
        </Col>
        <Col span={12}>
          <BaseForm.Item name="type" label="Type" rules={[{required: true, message: 'Select at least one type'}]}>
            <Select placeholder="Select type" options={typeOptions} />
          </BaseForm.Item>
        </Col>

        <Col span={12}>
          <BaseForm.Item
            name="monthPrice"
            label="Monthly Price"
            rules={[{required: true, type: 'number', min: 0, message: 'Enter a valid monthly price'}]}>
            <InputNumber style={{width: '100%'}} placeholder="e.g., 8000" />
          </BaseForm.Item>
        </Col>

        <Col span={12}>
          <BaseForm.Item
            name="yearPrice"
            label="Yearly Price"
            rules={[{required: true, type: 'number', min: 0, message: 'Enter a valid yearly price'}]}>
            <InputNumber style={{width: '100%'}} placeholder="e.g., 150000" />
          </BaseForm.Item>
        </Col>

        <Col span={24}>
          <BaseForm.Item
            name="features"
            label="Features"
            rules={[{required: true, message: 'Select at least one feature'}]}>
            <Select mode="multiple" placeholder="Select features" options={featureOptions} />
          </BaseForm.Item>
        </Col>
        <Col span={10}>
          <BaseForm.Item
            name="numberOfDays"
            label="Number of Days"
            rules={[{type: 'number', min: 0, message: 'Enter valid number of days'}]}>
            <InputNumber style={{width: '100%'}} placeholder="e.g., 14" />
          </BaseForm.Item>
        </Col>
        <Col span={6} style={{marginTop: '30px'}}>
          <BaseForm.Item name="freeTrail" valuePropName="checked">
            <Checkbox>Free Trial</Checkbox>
          </BaseForm.Item>
        </Col>
        <Col span={6} style={{marginTop: '30px'}}>
          <BaseForm.Item name="active" valuePropName="checked">
            <Checkbox>Active</Checkbox>
          </BaseForm.Item>
        </Col>
      </Row>
    </BaseForm>
  );
};
