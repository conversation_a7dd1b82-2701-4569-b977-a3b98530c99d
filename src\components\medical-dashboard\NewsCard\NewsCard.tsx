import React from 'react';
import {dashboardNews} from '@app/constants/dashboardNews';
import {DashboardCard} from '../DashboardCard/DashboardCard';
import * as S from './NewsCard.styles';
import {useTranslation} from 'react-i18next';
import {AvailableRoomCard} from '@app/components/common/ArticleCard/AvailableRoomCard';

export const NewsCard: React.FC = () => {
  const {t} = useTranslation();

  return (
    <DashboardCard title={t('medical-dashboard.news')}>
      <S.Wrapper>
        {dashboardNews.map((advice: any, index) => (
          <AvailableRoomCard
            key={index}
            imgUrl={advice.img}
            name={advice.title}
            description={advice.text}
            avatar={advice.avatarUrl}
            amount={0}
            view=""
            // author={advice.author}
            tags={advice.tags}
          />
        ))}
      </S.Wrapper>
    </DashboardCard>
  );
};
