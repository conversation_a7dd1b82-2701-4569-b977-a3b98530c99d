/* eslint-disable @typescript-eslint/no-explicit-any */
import instance, {HOTEL_SERVICE} from '@app/api/instance';
import {IReservedRoomFilter, ReservationResponse} from '@app/components/apps/roomFeed/interface';

export interface IRoomBlocking {
  checkInDate: string;
  checkOutDate: string;
  channelId: number;
  resident: boolean;
  hotelId: number;
  roomId: string | null;
  roomTypeId: number | null;
}

export const roomBlockAndFetchStayTypesByRoomId = (payload: IRoomBlocking): any => {
  return instance.post(HOTEL_SERVICE + 'reservation/inquiry/room-block', payload).then(({data}) => data);
};

export const getAllAvailableStayTypesByRoomId = (payload: any): any => {
  return instance.post(HOTEL_SERVICE + 'reserved-room/reserved-room/stayType/inquiry', payload).then(({data}) => data);
};

export const getAllAvailableRoomsByDate = (payload: any): any => {
  return instance.post(HOTEL_SERVICE + 'reserved-room/inquiry/available-room', payload).then(({data}) => data);
};

export const createReservationInquiry = (payload: any): any => {
  return instance.post(HOTEL_SERVICE + 'reservation/inquiry/room-block', payload).then(({data}) => data);
};

export const getAllInquiryOverView = (hotelId: number, startDate: string, endDate: string) =>
  instance
    .get(
      HOTEL_SERVICE +
        `reservation/inquiry/roomType/${hotelId}?startDate=${startDate}&endDate=${endDate}&reservedRoomStatus=INQUIRY,CONFIRMED,PENDING,DEPOSIT_PAID`,
    )
    .then(({data}) => data);

export const getAllInquiriesHistories = (
  hotelId: number,
  {checkInDate, mainGuestFirstName, mainGuestLastName, checkOutDate, refNumber, channelName}: IReservedRoomFilter,
  pageSize: number | undefined,
  current: number,
): Promise<ReservationResponse> =>
  instance
    .get<ReservationResponse>(
      HOTEL_SERVICE +
        `reservation/inquiry/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&reservationStatusIds=9&checkOutDate=${
          checkOutDate ? checkOutDate : ''
        }&checkInDate=${checkInDate ? checkInDate : ''}&mainGuestFirstName=${
          mainGuestFirstName ? mainGuestFirstName : ''
        }&mainGuestLastName=${mainGuestLastName ? mainGuestLastName : ''}&refNumber=${
          refNumber ? refNumber : ''
        }&hotelId=${hotelId}&channelName=${channelName ? channelName : ''}&reservationStatus=`,
    )
    .then(({data}) => data);

export const updateReservationInquiry = (payload: any): any => {
  return instance.put(HOTEL_SERVICE + 'reservation/inquiry/room-block', payload).then(({data}) => data);
};
