import inventoryInstance, {INVENTORY_SERVICE} from '@app/api/inventoryInstance';

export const CreateCategory = (payload: CreateCategoryProps): Promise<CategoryResponse> => {
  return inventoryInstance.post<CategoryResponse>(INVENTORY_SERVICE + 'category', payload).then(({data}) => data);
};

export const UpdateCategory = (payload: UpdateCategoryProps): Promise<CategoryResponse> => {
  return inventoryInstance.put<CategoryResponse>(INVENTORY_SERVICE + 'category', payload).then(({data}) => data);
};

export const getAllCategories = (): Promise<CategoryResponse> =>
  inventoryInstance.get<CategoryResponse>(INVENTORY_SERVICE + 'categories').then(({data}) => data);

export const DeleteCategory = (id: number): Promise<CategoryResponse> =>
  inventoryInstance.delete<CategoryResponse>(INVENTORY_SERVICE + `category/${id}`).then(({data}) => data);

export interface CreateCategoryProps {
  name: string;
  description: string;
}

export interface UpdateCategoryProps {
  id: number;
  name: string;
  description: string;
}

export interface CategoryResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}
