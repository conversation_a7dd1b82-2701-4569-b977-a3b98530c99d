import {useState} from 'react';
import {Modal} from 'antd';
import {QrCode} from 'lucide-react';
import {QRCodeSVG} from 'qrcode.react';
import {BASE_COLORS} from '@app/styles/themes/constants';

interface QrCodeWithModalProps {
  qrCodeUrl: any;
}

const QrCodeWithModal: React.FC<QrCodeWithModalProps> = ({qrCodeUrl}) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  return (
    <>
      <div style={{cursor: 'pointer'}} onClick={() => setIsModalOpen(true)}>
        <QrCode />
      </div>

      <Modal
        title={
          <span
            style={{
              color: BASE_COLORS.rmsBasicColor,
              fontSize: '20px',
              fontWeight: 'bold',
              textAlign: 'center',
              fontFamily: "'Inter', sans-serif",
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            Scan Here
          </span>
        }
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
        width={350}
        centered={true}
        bodyStyle={{height: '300px', display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
        <QRCodeSVG value={qrCodeUrl} size={200} />
      </Modal>
    </>
  );
};

export default QrCodeWithModal;
