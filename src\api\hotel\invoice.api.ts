import instance, {HOTEL_SERVICE} from '@app/api/instance';
import {AxiosRequestConfig} from 'axios';

export const getAllInvoices = (
  hotelId: number,
  invoiceStatus: string,
  {invoiceNo, reservationNo, revenueNumbers, firstName, lastName, invoiceNos}: FilterProps,
  pageSize: number | undefined,
  current: number,
  invoiceType: 'PROFORMA' | 'REVENUE',
): Promise<InvoiceResponse> =>
  instance
    .get<InvoiceResponse>(
      HOTEL_SERVICE +
        `invoice/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&hotelId=${hotelId}&invoiceStatus=${
          invoiceStatus ? invoiceStatus : ''
        }&invoiceNo=${invoiceNos ? invoiceNos : ''}&revenueNumbers=${
          revenueNumbers ? revenueNumbers : ''
        }&reservationNo=${reservationNo ? reservationNo : ''}&invoiceType=${invoiceType}&firstName=${
          firstName ? firstName : ''
        }&lastName=${lastName ? lastName : ''}`,
    )
    .then(({data}) => data);

const config: AxiosRequestConfig = {
  responseType: 'blob',
};

export const getAllAdditionalBills = (
  hotelId: number,
  invoiceStatus: string,
  searchQuery: FilterSearchProps,
  pageSize: number | undefined,
  current: number,
  invoiceType: string,
): Promise<InvoiceResponse> =>
  instance
    .get<InvoiceResponse>(
      HOTEL_SERVICE +
        `additional-service-invoice/search?page=${current}&size=${pageSize}&invoiceNo=${
          searchQuery.additionalServiceInvoiceNo ? searchQuery.additionalServiceInvoiceNo : ''
        }&guestName=${
          searchQuery.guestName ? searchQuery.guestName : ''
        }&sortField=id&direction=DESC&invoiceStatus=&hotelId=${hotelId}&additionalServiceRefNumber=${
          searchQuery.additionalServiceRefNumber ? searchQuery.additionalServiceRefNumber : ''
        }&invoiceType=${invoiceType}&bookedDate=${searchQuery.checkInDate ? searchQuery.checkInDate : ''}`,
    )
    .then(({data}) => data);

export const getAllAdditionalEventsInvoice = (
  hotelId: number,
  invoiceStatus: string,
  searchQuery: FilterSearchInvProps,
  pageSize: number | undefined,
  current: number,
  invoiceType: string,
): Promise<InvoiceResponse> => {
  return instance
    .get<InvoiceResponse>(
      HOTEL_SERVICE +
        `additional-activity-invoice/search?page=${current}&size=${pageSize}&hotelId=${hotelId}&invoiceNo=${
          searchQuery.additionalActivityInvoiceNo ? searchQuery.additionalActivityInvoiceNo : ''
        }&guestName=${searchQuery.guestName ? searchQuery.guestName : ''}&refNumber=${
          searchQuery.refNumber ? searchQuery.refNumber : ''
        }&direction=DESC&sortField=id&invoiceType=${invoiceType}&bookedDate=${
          searchQuery.checkInDate ? searchQuery.checkInDate : ''
        }`,
    )
    .then(({data}) => data);
};

export const getInvoicePdf = (invoiceId: number): Promise<any> =>
  instance.get<any>(HOTEL_SERVICE + `invoice/openpdf/${invoiceId}`, config).then(({data}) => data);

export const makeInvoicePayment = (payload: MakePaymentProps): Promise<InvoiceResponse> => {
  return instance.post<InvoiceResponse>(HOTEL_SERVICE + 'payment', payload).then(({data}) => data);
};

export const makePaymentByLink = (reservationId: number): Promise<InvoiceResponse> => {
  return instance
    .get<InvoiceResponse>(HOTEL_SERVICE + `reservation-outstanding-payment/${reservationId}`)
    .then(({data}) => data);
};

export const makeAdditionalBillPayment = (payload: MakePaymentProps): Promise<InvoiceResponse> => {
  return instance.post<InvoiceResponse>(HOTEL_SERVICE + 'additional-service-payment', payload).then(({data}) => data);
};
export interface InvoiceResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  invoiceNo: string;
  invoiceStatus: string;
  reservationNo: string;
  revenueNumbers: string;
  firstName: string;
  lastName: string;
  invoiceNos: string;
}
export interface FilterSearchProps {
  additionalServiceInvoiceNo: string;
  guestName: string;
  additionalServiceRefNumber: string;
  checkInDate?: string;
}
export interface FilterSearchInvProps {
  additionalActivityInvoiceNo?: string;
  guestName?: string;
  refNumber?: string;
  checkInDate?: string;
}
export interface MakePaymentProps {
  amount: number;
  invoiceId: number;
  paid: boolean;
  currencyId: number;
  paymentMethod?: string;
}
