import {Button} from 'antd';
import React from 'react';
import {FaFileInvoice} from 'react-icons/fa';
import * as S from './CustomIcon.Style';

interface IProps {
  disabled?: boolean;
  icon?: React.ReactElement;
  onClick: () => void;
}

function NavigationIcon({disabled = false, onClick, icon = <FaFileInvoice />}: IProps) {
  return (
    <S.ColumnActionArea>
      <Button disabled={disabled} size="small" icon={icon} onClick={onClick}></Button>
    </S.ColumnActionArea>
  );
}

export default NavigationIcon;
