import React from 'react';
// import { TopLevelCardWrap } from './TopLevelCard.style';
import * as S from './ViewRoom.style';
import {Card, Col, Row} from 'antd';

interface Props {
  name: string;
  bgUrl: string;
  onClick: () => void;
  info: string;
}

const ViewRoom: React.FC<Props> = ({name, bgUrl, onClick, info}) => {
  return (
    <S.OuterCard cover={<img style={{height: '6rem', width: '10rem'}} src={bgUrl} alt="" />} onClick={() => onClick}>
      <S.Wrapper>
        <Row justify={'center'}>
          <Col>
            <S.TopLevelCardTitle>{name && name.toUpperCase()}</S.TopLevelCardTitle>
          </Col>
        </Row>
      </S.Wrapper>
      <S.TopLevelCardInfo>{info}</S.TopLevelCardInfo>
      {/* <div className="overlay"></div> */}
    </S.OuterCard>
  );
};

export default ViewRoom;
