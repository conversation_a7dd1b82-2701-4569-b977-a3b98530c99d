import instance, {HOTEL_SERVICE} from '@app/api/instance';
import {ICreatePayload} from '@app/pages/MasterPages/hotel/Room/hall/interface';

export const getAllHalls = (hotelId: number): Promise<RoomResponse> =>
  instance.get<RoomResponse>(HOTEL_SERVICE + `hall/halls/${hotelId}`).then(({data}) => data);

export const CreateHall = (payload: ICreatePayload): Promise<RoomResponse> => {
  return instance.post<RoomResponse>(HOTEL_SERVICE + 'hall', payload).then(({data}) => data);
};

export const UpdateHall = (payload: ICreatePayload): Promise<RoomResponse> => {
  return instance.put<RoomResponse>(HOTEL_SERVICE + 'hall', payload).then(({data}) => data);
};

export const DeleteHall = (id: number): Promise<RoomResponse> => {
  return instance.delete<RoomResponse>(HOTEL_SERVICE + `hall/${id}`).then(({data}) => data);
};

export interface RoomResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}
export interface FilterProps {
  roomNumber: string;
  unitCode: string;
  viewType: string;
  roomType: string;
  phoneExtention: string;
  roomName: string;
}
