/**
 * <AUTHOR> Admin
 * @desc [Food Ingredients management page]
 */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Tables} from '@app/components/tables/Tables/Tables';
import {PageTitle} from '@app/components/common/PageTitle/PageTitle';
import {Space, Tag} from 'antd';
import {DeleteOutlined, EditOutlined} from '@ant-design/icons';
import {BASE_COLORS} from '@app/styles/themes/constants';
import {ColumnsType} from 'antd/lib/table';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {setIsEditAction, setLoading, setModalVisible} from '@app/store/slices/commonSlice';
import {TablePaginationConfig} from 'antd/es/table';
import {Button} from '@app/components/common/buttons/Button/Button';
import {Popconfirm} from '@app/components/common/Popconfirm/Popconfirm';
import {notificationController} from '@app/controllers/notificationController';
import {RESTAURANT_ADMIN_MODULE_NAME, modulePermission} from '@app/utils/permissions';
import _ from 'lodash';
import FoodIngredientForm from './FoodIngredientForm';

export interface IFoodIngredientData {
  id: number;
  name: string;
  description: string;
  unit: string;
  costPerUnit: number;
  category: string;
  allergens: string[];
  active: boolean;
}

const ManageFoodIngredients: React.FC = () => {
  // Get permission
  const userPermission = useAppSelector(state => state.user.permissions);
  const permissions = modulePermission(userPermission, RESTAURANT_ADMIN_MODULE_NAME.FOOD_INGREDIENTS);
  const {t} = useTranslation();
  const [form] = BaseForm.useForm();
  const loading = useAppSelector(state => state.commonSlice.loading);
  const isEditAction = useAppSelector(state => state.commonSlice.isEditAction);
  const isTouch = useAppSelector(state => state.commonSlice.isTouch);
  const hotelConfig = useAppSelector(state => state.hotelSlice.hotelConfig);
  const hotelServiceConfig = useAppSelector(state => state.hotelSlice.hotelServiceConfig);

  const [rowData, setRowData] = useState<IFoodIngredientData>({
    id: 0,
    name: '',
    description: '',
    unit: '',
    costPerUnit: 0,
    category: '',
    allergens: [],
    active: true,
  });

  let [searchObj]: any = useState({});
  const [ingredients, setIngredients] = useState<IFoodIngredientData[]>([]);
  const [pagination, setPagination] = React.useState<TablePaginationConfig>({current: 0, pageSize: 10, total: 0});

  const dispatch = useAppDispatch();

  const columns: ColumnsType<IFoodIngredientData> = [
    {
      title: 'Name',
      dataIndex: 'name',
      align: 'left',
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      align: 'left',
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: 'Unit',
      dataIndex: 'unit',
      align: 'center',
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: 'Cost per Unit',
      dataIndex: 'costPerUnit',
      align: 'center',
      render: (text: number) => <span>{Number(text).toFixed(2)}</span>,
    },
    {
      title: 'Category',
      dataIndex: 'category',
      align: 'center',
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: 'Allergens',
      dataIndex: 'allergens',
      align: 'center',
      render: (allergens: string[]) => (
        <Space>
          {allergens.map((allergen, index) => (
            <Tag key={index} color="orange">
              {allergen}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'active',
      align: 'center',
      render: (active: boolean) => <Tag color={active ? 'green' : 'red'}>{active ? 'Active' : 'Inactive'}</Tag>,
    },
    {
      title: 'Action',
      dataIndex: 'action',
      align: 'center',
      render: (_text: string, record: IFoodIngredientData) => {
        return (
          <Space>
            {permissions.EDIT && (
              <EditOutlined
                style={{color: BASE_COLORS.primary}}
                type="ghost"
                onClick={() => {
                  setRowData(record);
                  dispatch(setIsEditAction(true));
                  dispatch(setModalVisible(true));
                }}
              />
            )}
            {permissions.DELETE && (
              <Popconfirm
                title="Are you sure you want to delete this ingredient?"
                onConfirm={() => deleteIngredient(record.id)}
                okText="Yes"
                cancelText="No">
                <DeleteOutlined style={{color: BASE_COLORS.red}} type="ghost" />
              </Popconfirm>
            )}
          </Space>
        );
      },
    },
  ];

  const deleteIngredient = async (id: number) => {
    try {
      dispatch(setLoading(true));
      // TODO: Implement delete API call
      // await DeleteFoodIngredient(id);
      notificationController.success({message: 'Ingredient deleted successfully'});
      // Reload data
      listFoodIngredients(searchObj, pagination.pageSize, pagination.current ? pagination.current - 1 : 0);
    } catch (error: any) {
      notificationController.error({message: error.message || 'Failed to delete ingredient'});
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handlePagination = (pagination: TablePaginationConfig) => {
    setPagination({
      pageSize: pagination.pageSize,
      current: pagination.current ? pagination.current - 1 : 0,
      total: pagination.total,
    });
    listFoodIngredients(searchObj, pagination.pageSize, pagination.current ? pagination.current - 1 : 0);
  };

  const onChangeTableSearch = (searchQuery: any) => {
    searchObj = searchQuery;
    listFoodIngredients(searchQuery, pagination.pageSize, 0);
  };

  const listFoodIngredients = async (searchQuery: any, pageSize: number | undefined, current: number) => {
    try {
      dispatch(setLoading(true));
      // TODO: Implement API call to fetch ingredients
      // For now, using mock data
      const mockData: IFoodIngredientData[] = [
        {
          id: 1,
          name: 'Tomatoes',
          description: 'Fresh red tomatoes',
          unit: 'kg',
          costPerUnit: 2.5,
          category: 'Vegetables',
          allergens: [],
          active: true,
        },
        {
          id: 2,
          name: 'Cheese',
          description: 'Mozzarella cheese',
          unit: 'kg',
          costPerUnit: 8.0,
          category: 'Dairy',
          allergens: ['Dairy'],
          active: true,
        },
        {
          id: 3,
          name: 'Flour',
          description: 'All-purpose flour',
          unit: 'kg',
          costPerUnit: 1.2,
          category: 'Grains',
          allergens: ['Gluten'],
          active: true,
        },
      ];

      setIngredients(mockData);
      setPagination({
        pageSize: pageSize,
        current: current + 1,
        total: mockData.length,
      });
    } catch (error: any) {
      notificationController.error({message: error.message || 'Failed to fetch ingredients'});
    } finally {
      dispatch(setLoading(false));
    }
  };

  useEffect(() => {
    if (hotelConfig.hotelId && hotelServiceConfig.serviceId) {
      listFoodIngredients(searchObj, pagination.pageSize, 0);
    }
  }, [hotelConfig.hotelId, hotelServiceConfig.serviceId]);

  return (
    <div style={{fontFamily: "'Inter', serif"}}>
      <PageTitle>Food Ingredients</PageTitle>
      <div style={{borderRadius: '12px', overflow: 'hidden'}}>
        <Tables
          isCreate={permissions.ADD}
          title="Food Ingredient"
          tableData={ingredients}
          columns={columns}
          searchFields={['name', 'category']}
          onChangeFilter={handlePagination}
          onChangeSearch={onChangeTableSearch}
          modalChildren={
            // <></>
            <FoodIngredientForm
              form={form}
              rowData={rowData}
              reloadData={() =>
                listFoodIngredients(searchObj, pagination.pageSize, pagination.current ? pagination.current - 1 : 0)
              }
            />
          }
        />
      </div>
    </div>
  );
};

export default ManageFoodIngredients;
