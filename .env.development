REACT_APP_BASE_URL="http://************"
# REACT_APP_BASE_URL="https://test.serendipityretreats.com"
REACT_APP_WEB_CHANNEL_ID=2
REACT_APP_WEB_CLIENT_IP=https://**************

REACT_APP_ASSETS_BUCKET="https://lightence-assets.s3.amazonaws.com"
# more info https://create-react-app.dev/docs/advanced-configuration
ESLINT_NO_DEV_ERRORS=true
TSC_COMPILE_ON_ERROR=true

REACT_APP_CAVERN_CONTACT_PHONE_NUMBER=+94768384512
REACT_APP_DEFAULT_CONTACT_PHONE_NUMBER=+94766973287
REACT_APP_CAVERN_CONTACT_EMAIL_ADDRESS=<EMAIL>
REACT_APP_DEFAULT_CONTACT_EMAIL_ADDRESS=<EMAIL>

REACT_APP_HOTEL_INFO='{"1":"+94 77 777 6065","2":"+94 77 208 5314","3":"+94 77 208 5314","4":"+94 77 208 5314","5":"+94 77 111 1135","6":"+94 77 111 1135"}'
REACT_APP_HOTEL_MIN_NIGHT='{"1":true,"2":false,"3":true,"4":false,"5":true,"6":true}'

# REACT_APP_CAVERN_CONTACT_PHONE_NUMBER=+94771111135
# REACT_APP_DEFAULT_CONTACT_PHONE_NUMBER=+94772085314
# REACT_APP_CAVERN_CONTACT_EMAIL_ADDRESS=<EMAIL>
# REACT_APP_DEFAULT_CONTACT_EMAIL_ADDRESS=<EMAIL>

REACT_APP_IMAGE_SIZE_LIMIT_MB=10
REACT_APP_IMAGE_SIZE_ERROR_MSG="Image must be smaller than 10MB!"
