import React from 'react';
import styled from 'styled-components';

const StatusStampContainer = styled.div`
  position: absolute;
  top: 10px;
  left: 0;
  transform: rotate(-30deg) translateX(-10px);
  transform-origin: top left;
  z-index: 3;
  min-width: 70px; /* Increased from 50px */
  text-align: center;
`;

const StampCircle = styled.div`
  background-color: ${props => props.color || '#16a34a'};
  color: white;
  font-weight: bold;
  font-size: 12px; /* Increased from 9px */
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  position: relative;
  height: 65px; /* Increased from 45px */
  width: 65px; /* Increased from 45px */

  &::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    bottom: 3px;
    border: 1px dashed rgba(255, 255, 255, 0.8);
    border-radius: 50%;
  }

  /* Create notches/gaps around the circle */
  &::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid ${props => props.color || '#16a34a'};
    border-radius: 50%;
    background: transparent;
    /* Create notched effect with radial-gradient */
    mask: radial-gradient(
      transparent 0%,
      transparent 12%,
      black 13%,
      black 30%,
      transparent 31%,
      transparent 39%,
      black 40%,
      black 57%,
      transparent 58%,
      transparent 66%,
      black 67%,
      black 84%,
      transparent 85%,
      transparent 93%,
      black 94%
    );
  }
`;

const StampText = styled.div`
  text-transform: uppercase;
  letter-spacing: 0.3px;
  font-size: 8px;
  line-height: 1.2;
  text-align: center;
  max-width: 55px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 2px;
`;

interface StatusStampProps {
  children: React.ReactNode;
  color: string;
}

const StatusStamp: React.FC<StatusStampProps> = ({children, color}) => {
  return (
    <StatusStampContainer>
      <StampCircle color={color}>
        <StampText>{children}</StampText>
      </StampCircle>
    </StatusStampContainer>
  );
};

export default StatusStamp;
