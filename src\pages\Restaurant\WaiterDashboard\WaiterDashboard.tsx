/* eslint-disable react/jsx-key */
/* eslint-disable prefer-const */
import * as React from 'react';
// import * as S from './DashboardPage.styles';
// import {useTranslation} from 'react-i18next';
import {Row, Form, Space, Button, Col} from 'antd';
import {PageTitle} from '@app/components/common/PageTitle/PageTitle';
import {IOrderItem} from '@app/api/getOrders.api';
import {SearchOutlined} from '@ant-design/icons';
import {find, remove, uniqBy} from 'lodash';
import RightArea from './RightArea';
import LeftArea from './LeftArea';
import {getCategoryStuctureByID} from '@app/api/resturant/tablecategory/categoryStucture.api';
import {notificationController} from '@app/controllers/notificationController';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {INewOrderList, addFoodItems, addTableItems, setNewOrderList} from '../slices/waiterDasboardSlice';
import TopLevelFilter from './TopLevelFilter';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {Input} from '@app/components/common/inputs/Input/Input';
import {searchFoodByName} from '@app/api/resturant/tablecategory/foodItems.api';
// import {MdTableRestaurant} from 'react-icons/md';
// import {useNavigate} from 'react-router';
import {GridContainer, LeftBottom, LeftContainer, LeftTop, RightContainer} from '../WaiterDashboard/dashboard.style';
import {BASE_COLORS} from '@app/styles/themes/constants';
import '../../Restaurant/WaiterDashboard/TopLevelFilter.style.css';
import {FaCartArrowDown} from 'react-icons/fa';
import {BsCartCheckFill} from 'react-icons/bs';
// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface IProps {}

const WaiterDashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  // const navigate = useNavigate();

  const [width, setWidth] = React.useState(window.innerWidth);

  // const windowSize = React.useRef([window.innerWidth, window.innerHeight]);
  const foodItems = useAppSelector(state => state.waiterDasbordSlice.foodItems);
  const newOrderList = useAppSelector(state => state.waiterDasbordSlice.newOrderList);
  const selelctedTableId = useAppSelector(state => state.waiterDasbordSlice.selectedTableId);
  const hotelServiceConfig = useAppSelector(state => state.hotelSlice.hotelServiceConfig);
  const [isReadyToServe, setIsReadyToServe] = React.useState<boolean>(false);
  const [showRightContainer, setShowRightContainer] = React.useState(false);
  const [cartCount, setCartCount] = React.useState(null);
  const [isSteward, setIsSteward] = React.useState<boolean>(false);

  const [form] = Form.useForm();

  const getAllFoodItemsByCategory = async (id: any, name?: string) => {
    const results = await getCategoryStuctureByID(id, hotelServiceConfig.serviceId, name);
    if (results?.statusCode === '20000') {
      let data: any[] = [];

      results.result.category &&
        results.result.category.map((post: any, i: any) => {
          data.push({
            id: post.id,
            category: '',
            currency: 'LKR',
            itemImage: post.image,
            item: post.name,
            quantity: 1,
            commentQuantity: 0,
            price: post.price,
            orderedItemStatus: 'NEW',
            totalPrice: post.price,
            active: post.active,
          });
        });

      dispatch(addFoodItems({foodItems: data}));
    } else {
      notificationController.error({message: results?.message});
    }
  };

  const converFinalData = (data: INewOrderList[]) => {
    const updatedTableData = data.map(table => {
      const updatedFoodItemList = table.foodItemList.map(item => {
        const matchingItems = table.foodItemList.filter(foodItem => foodItem.uniqKey === item.uniqKey);
        const totalQuantity = matchingItems.reduce((sum, foodItem) => sum + foodItem.quantity, 0);
        const totalPrice = totalQuantity * item.price;

        return {
          ...item,
          totalPrice: totalPrice,
          quantity: totalQuantity,
        };
      });

      return {
        tableId: table.tableId,
        foodItemList: uniqBy(updatedFoodItemList, 'uniqKey'),
      };
    });
    return updatedTableData;
  };

  const onChangeItem = (item: IOrderItem, index?: any, quantity?: number) => {
    if (selelctedTableId) {
      const findObj = find(newOrderList, res => res.tableId === selelctedTableId);
      const defaultData: INewOrderList[] = [...newOrderList];
      const selectTableOrderList: INewOrderList = findObj
        ? findObj
        : {tableId: 0, orderId: 0, orderStatus: '', foodItemList: []};
      const initOrderList: IOrderItem[] = selectTableOrderList.foodItemList;

      const selectedItem = find(
        initOrderList,
        res => res.uniqKey === `${selelctedTableId}_${item.id}_${item.orderedItemStatus}`,
      );

      const extingComment = selectedItem ? selectedItem.comments : '';

      const newItem: IOrderItem = {
        id: 0,
        key: '',
        uniqKey: `${selelctedTableId}_${item.id}_${item.orderedItemStatus}`,
        quantity: item.quantity,
        item: item.item,
        itemId: item.id,
        currency: item.currency,
        itemImage: item.itemImage,
        comments: extingComment,
        commentQuantity: item.commentQuantity,
        orderedItemStatus: item.orderedItemStatus,
        selelctedTableId: selelctedTableId,
        price: item.price,
        category: item.category,
        totalPrice: item.price,
        hotelServiceId: hotelServiceConfig.serviceId,
        active: item.active,
      };
      if (findObj) {
        remove(defaultData, res => res.tableId === selelctedTableId);
        const xOrder = [newItem, ...initOrderList];
        // xOrder.push(newItem);
        const newTableObj: INewOrderList = {
          tableId: selelctedTableId,
          orderId: selectTableOrderList.orderId,
          orderStatus: selectTableOrderList.orderStatus,
          foodItemList: xOrder,
        };
        defaultData.push(newTableObj);
      } else {
        const xOrder = [newItem, ...initOrderList];
        // xOrder.push(newItem);
        const newTableObj: INewOrderList = {
          tableId: selelctedTableId,
          orderId: selectTableOrderList.orderId,
          orderStatus: selectTableOrderList.orderStatus,
          foodItemList: xOrder,
        };
        defaultData.push(newTableObj);
      }

      // addTableItems;
      dispatch(addTableItems({tableItems: defaultData}));
      // @ts-ignore
      dispatch(setNewOrderList({orderList: converFinalData(defaultData)}));
    } else {
      notificationController.warning({message: 'Please select a table'});
    }
  };

  // Get food items by category ID
  const getFoodsByCategoryId = async (id: any) => {
    try {
      const result = await getCategoryStuctureByID(id, hotelServiceConfig.serviceId, '');
      const foodItems: any[] = [];
      result.result.category &&
        result.result.category.map((post: any, i: any) => {
          foodItems.push({
            id: post.id,
            category: '',
            currency: 'LKR',
            itemImage: post.image,
            item: post.name,
            quantity: 1,
            commentQuantity: 0,
            price: post.price,
            orderedItemStatus: 'NEW',
            totalPrice: post.price,
            active: post.active,
          });
        });
      dispatch(addFoodItems({foodItems: foodItems}));
    } catch (error) {}
  };

  // Search foods
  const handleSearchFoods = (text: any) => {
    foodSearchByName(text);
  };

  const foodSearchByName = async (name: string, dropdownSearch?: boolean) => {
    try {
      const result = await searchFoodByName(name, hotelServiceConfig.serviceId);

      if (name.length === 0) {
        getFoodsByCategoryId(localStorage.getItem('categoryId'));
      } else {
        if (result?.statusCode === '20000') {
          if (dropdownSearch) {
            const dropDownItems: any[] = [];
            result.result.item &&
              result.result.item.map((post: any, i: any) => {
                dropDownItems.push({
                  value: post.id,
                  title: post.name,
                });
              });
          } else {
            const foodItems: any[] = [];

            result.result.item &&
              result.result.item.map((post: any, i: any) => {
                foodItems.push({
                  id: post.id,
                  category: '',
                  currency: 'LKR',
                  itemImage: post.image,
                  item: post.name,
                  quantity: 1,
                  commentQuantity: 0,
                  price: post.price,
                  orderedItemStatus: 'NEW',
                  totalPrice: post.price,
                  active: post.active,
                });
              });
            dispatch(addFoodItems({foodItems: foodItems}));
          }
        } else {
        }
      }
    } catch (error) {}
  };

  React.useEffect(() => {
    //
  }, []);

  const orderCurrentTime = new Date().toLocaleDateString('en-GB', {
    weekday: 'long',
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  });

  const getReadyToServeStatus = (status: boolean) => {
    setIsReadyToServe(status);
  };

  // Search input for search foods
  const MemorizedInput = React.useMemo(
    () => (
      <BaseForm
        name="stepForm"
        form={form}
        onValuesChange={(info: any, data: any) => {
          handleSearchFoods(data.search);
        }}>
        <BaseForm.Item name="search">
          <Input
            size="small"
            suffix={<SearchOutlined />}
            placeholder="Search foods..."
            style={{
              width: '100%',
              marginRight: '2%',
              left: '-10px',
            }}
          />
        </BaseForm.Item>
      </BaseForm>
    ),

    [],
  );

  React.useEffect(() => {
    const handleResize = () => setWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  React.useEffect(() => {
    const match = location.pathname.match(/^\/restaurant\/dashboard\/:(\d+)$/);
    if (match) {
      localStorage.setItem('restaurantId', match[1]);
    }
  }, [location.pathname]);

  const desktopLayout = (
    <>
      <Col md={24} lg={16} sm={24} xs={24} xl={16} className="left-container" style={{marginRight: '0.5rem'}}>
        <LeftTop>
          <TopLevelFilter />
        </LeftTop>
        <LeftBottom>
          <LeftArea
            onChangeItem={onChangeItem}
            getAllFoodItemsByCategory={getAllFoodItemsByCategory}
            orderFilterItems={foodItems}
          />
        </LeftBottom>
      </Col>

      <Col md={24} lg={8} sm={24} xs={24} className={`right-container ${showRightContainer ? 'visible' : ''}`}>
        <RightContainer>
          <RightArea
            getReadyToServeStatus={getReadyToServeStatus}
            setCartCount={setCartCount}
            isSteward={isSteward}
            isReadyToServe={isReadyToServe}
          />
        </RightContainer>
      </Col>
      <div
        className="show-button"
        style={{
          display: 'flex',
          justifyContent: 'flex-start',
          right: '20px',
          bottom: '20px',
          position: 'absolute',
        }}>
        <div
          style={{
            padding: '6px 12px',
            borderRadius: '10px',
            color: 'white',
            boxShadow: 'none',
            marginBottom: '-10px',
            backgroundColor: showRightContainer ? BASE_COLORS.rmsBasicColor : '#1f55eb',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '40px',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            transform: `scale(${showRightContainer ? 1 : 0.9})`,
            transitionDelay: `${showRightContainer ? '0s' : '0.3s'}`,
            zIndex: 100,
            marginLeft: '10px',
            fontSize: '16px',
          }}
          onClick={() => setShowRightContainer(!showRightContainer)}>
          {showRightContainer ? <FaCartArrowDown /> : <BsCartCheckFill />}
        </div>
        <div
          style={{
            position: 'absolute',
            top: '-5px',
            right: '-5px',
            backgroundColor: BASE_COLORS.red,
            borderRadius: '50%',
            color: BASE_COLORS.white,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            fontSize: '12px',
            fontWeight: 'bold',
            height: '20px',
            width: '20px',
            zIndex: 200,
          }}>
          {cartCount}
        </div>
      </div>
    </>
  );

  return (
    <>
      <PageTitle>Steward</PageTitle>
      {width > 1024 ? desktopLayout : <Row>{desktopLayout}</Row>}
    </>
  );
};

export default React.memo(WaiterDashboard);
