import {ExpandOutlined} from '@ant-design/icons';
import {BASE_COLORS, FONT_SIZE, FONT_WEIGHT} from '@app/styles/themes/constants';
import {Typography} from 'antd';
import {AiOutlineCloudUpload} from 'react-icons/ai';
import {FaCloudUploadAlt} from 'react-icons/fa';
import {GrUpload} from 'react-icons/gr';
import {IoCloudUpload} from 'react-icons/io5';
import styled from 'styled-components';
import {Popover} from '../Popover/Popover';

export const FileInputContainer = styled.div`
  position: relative;
  // width: 150px; /* Set your desired width for the square box */
  height: 170px; /* Set your desired height for the square box */
  border: 1px solid var(--border-color);
  border-radius: 5px;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const UploadedImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

export const UploadIcon = styled(IoCloudUpload)`
  font-size: 4rem;
  color: ${BASE_COLORS.primary};
`;

export const FileInputLabel = styled.label`
  position: absolute;
  top: 8px;
  right: 8px;
  width: auto;
  padding: 6px;
  background-color: rgba(255, 255, 255, 0.8);
  text-align: center;
  cursor: pointer;
  font-weight: ${FONT_WEIGHT.bold};
  font-size: ${FONT_SIZE.xxs} !important;
  border-radius: 50px;
  color: ${BASE_COLORS.primary};
`;

export const FileInput = styled.input`
  display: none !important;
`;

export const ImageStatus = styled(Typography)`
  font-size: ${FONT_SIZE.xxs};
  color: ${BASE_COLORS.lightningRed};
  font-weight: ${FONT_WEIGHT.bold};
`;

export const ImageStatusBox = styled.div`
  position: absolute;
  bottom: 5px;
  left: 5px;
  background-color: ${BASE_COLORS.white};
  padding: 0px 10px 0px 10px;
  border-radius: 50px;
`;

export const UploadImageContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;
export const UploadImageText = styled(Typography)`
  font-size: ${FONT_SIZE.xs};
`;
export const ZoomIcon = styled(ExpandOutlined)`
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 6px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  font-size: 14px;
  cursor: zoom-in;
`;
export const CustomPopover = styled(Popover)`
  &&.ant-popover-inner-content {
    padding: 2px 5px !important;
  }
`;
