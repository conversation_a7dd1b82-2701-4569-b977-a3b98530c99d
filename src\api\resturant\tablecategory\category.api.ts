import restInstance, {RESTAURANT_SERVICE} from '@app/api/resturantInstance';

export interface CategoryRequest {
  category: {
    id?: number;
    title: string;
    hotelServiceId: number;
  };
  image: any;
}

export interface CategoryResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export const getAllCatgories = (
  hotelId: number,
  hotelServiceId: number,
  searchObj: any,
  pageSize: number | undefined,
  current: number | undefined,
): Promise<any> =>
  restInstance
    .get<any>(
      RESTAURANT_SERVICE +
        `categories/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&hotelServiceId=${hotelServiceId}`,
    )
    .then(({data}) => data);

// import instance, {RESTAURANT_SERVICE} from '@app/api/instance';
export const CreateFoodCategory = (tableCategoryPayload: CategoryRequest): Promise<CategoryResponse> => {
  const categoryFormData = new FormData();
  categoryFormData.append('category', JSON.stringify(tableCategoryPayload.category));
  categoryFormData.append('image', tableCategoryPayload.image);
  return restInstance
    .post<CategoryResponse>(RESTAURANT_SERVICE + 'categories', categoryFormData)
    .then(({data}) => data);
};

export const getAllCategory = (): Promise<CategoryResponse> =>
  restInstance.get<CategoryResponse>(RESTAURANT_SERVICE + 'table-categories').then(({data}) => data);

export const UpdateFoodCategory = (tableCategoryPayload: CategoryRequest): Promise<CategoryResponse> => {
  const categoryFormData = new FormData();
  categoryFormData.append('category', JSON.stringify(tableCategoryPayload.category));
  categoryFormData.append('image', tableCategoryPayload.image);
  return restInstance.put<CategoryResponse>(RESTAURANT_SERVICE + 'categories', categoryFormData).then(({data}) => data);
};

export const DeleteFoodCategory = (id: number): Promise<CategoryResponse> =>
  restInstance.delete<CategoryResponse>(RESTAURANT_SERVICE + `categories/${id}`).then(({data}) => data);
