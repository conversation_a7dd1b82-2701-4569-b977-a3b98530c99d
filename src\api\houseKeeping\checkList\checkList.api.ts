import houseKeepingInstance from '@app/api/houseKeepingInstance';
import {HOUSE_KEEPING_SERVICE} from '@app/api/instance';

export const createCheckLists = (payload: CreateCheckListProps): Promise<Response> => {
  return houseKeepingInstance.post<Response>(HOUSE_KEEPING_SERVICE + 'checkLists', payload).then(({data}) => data);
};

export const updateCheckLists = (payload: UpdateCheckListProps): Promise<Response> => {
  return houseKeepingInstance.put<Response>(HOUSE_KEEPING_SERVICE + 'checkLists', payload).then(({data}) => data);
};

// export const multiSearchCheckLists = (
//   {name}: FilterProps,
//   pageSize: number | undefined,
//   current: number,
// ): Promise<PaginatedResponse> =>
//   houseKeepingInstance
//     .get<PaginatedResponse>(
//       HOUSE_KEEPING_SERVICE +
//         `checkList/search?page=${current}&size=${pageSize}&sortField=id&direction=ASC&name=${name ? name : ''}`,
//     )
//     .then(({data}) => data);
export const multiSearchCheckLists = (hotelId: number): Promise<PaginatedResponse> =>
  houseKeepingInstance
    .get<PaginatedResponse>(HOUSE_KEEPING_SERVICE + `checkLists/searchroomTypeWithArea?hotelId=${hotelId}`)
    .then(({data}) => data);

export const deleteCheckList = (id: number): Promise<Response> =>
  houseKeepingInstance.delete<Response>(HOUSE_KEEPING_SERVICE + `checkList/${id}`).then(({data}) => data);

export interface CreateCheckListProps {
  name: string;
  roomTypeName: string;
  roomTypeId: number;
  actionIds: number[];
  areaRequestList: any[];
  hotelId: number;
}

export interface UpdateCheckListProps {
  id: number;
  name: string;
  roomTypeName: string;
  roomTypeId: number;
  actionIds: number[];
  areaRequestList: any[];
  hotelId: number;
}

export interface Response {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface PaginatedResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
  pagination: {
    pageNumber: number;
    totalRecords: number;
  };
}

export interface FilterProps {
  name?: string;
}
