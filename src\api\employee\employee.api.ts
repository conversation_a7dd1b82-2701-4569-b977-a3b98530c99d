import authInstance from '@app/api/authInstance';
import {LOGIN_SERVICE} from '@app/api/instance';

// export const getAllEmployee = (hotelId: number): Promise<EmployeeResponse> =>
//   authInstance.get<EmployeeResponse>(LOGIN_SERVICE + `api/v1/employee/hotel/${hotelId}`).then(({data}) => data);

export const getAllEmployee = (
  hotelId: number,
  searchQuery: any,
  pageSize: number | undefined,
  current: number,
): Promise<EmployeeResponse> =>
  authInstance
    .get<EmployeeResponse>(
      LOGIN_SERVICE + `api/v1/employee/hotel/${hotelId}?page=${current}&sortField=id&direction=DESC&size=${pageSize}`,
    )
    .then(({data}) => data);

export const CreateEmployee = (payload: CreateEmployeeProps): Promise<EmployeeResponse> => {
  return authInstance.post<EmployeeResponse>(LOGIN_SERVICE + 'api/v1/employee', payload).then(({data}) => data);
};

export const UpdateEmployee = (payload: UpdateEmployeeProps): Promise<EmployeeResponse> => {
  return authInstance.put<EmployeeResponse>(LOGIN_SERVICE + 'api/v1/employee', payload).then(({data}) => data);
};

export const DeleteEmployee = (id: number): Promise<EmployeeResponse> =>
  authInstance.delete<EmployeeResponse>(LOGIN_SERVICE + `api/v1/employee/${id}`).then(({data}) => data);

export interface EmployeeResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface CreateEmployeeProps {
  firstName: string;
  lastName: string;
  contactNumber: number;
  email: string;
  groupsId: number;
  username?: string;
  hotelId?: number;
}

export interface UpdateEmployeeProps {
  id: number;
  firstName: string;
  lastName: string;
  contactNumber: number;
  email: string;
  groupsId: number;
  username?: string;
  hotelId?: number;
}
