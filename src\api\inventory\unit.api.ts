import inventoryInstance, {INVENTORY_SERVICE} from '@app/api/inventoryInstance';

export const CreateUnit = (payload: CreateUnitProps): Promise<UnitResponse> => {
  return inventoryInstance.post<UnitResponse>(INVENTORY_SERVICE + 'units', payload).then(({data}) => data);
};

export const UpdateUnit = (payload: UpdateUnitProps): Promise<UnitResponse> => {
  return inventoryInstance.put<UnitResponse>(INVENTORY_SERVICE + 'units', payload).then(({data}) => data);
};

export const getAllUnits = (
  {name, symbol, unitGroupName}: any,
  pageSize: number | undefined,
  current: number,
): Promise<UnitResponse> =>
  inventoryInstance
    .get<UnitResponse>(
      INVENTORY_SERVICE +
        `units/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${name ? name : ''}&symbol=${
          symbol ? symbol : ''
        }&unitGroupName=${unitGroupName ? unitGroupName : ''}`,
    )
    .then(({data}) => data);

export const DeleteUnit = (id: number): Promise<UnitResponse> =>
  inventoryInstance.delete<UnitResponse>(INVENTORY_SERVICE + `units/${id}`).then(({data}) => data);

export const getAllUnitByUnitGroupId = (unitGroupId: number): Promise<UnitResponse> =>
  inventoryInstance.get<UnitResponse>(INVENTORY_SERVICE + `units/unitGroup/${unitGroupId}`).then(({data}) => data);

export interface CreateUnitProps {
  name: string;
  symbol: string;
  unitGroupId: number;
}

export interface UpdateUnitProps {
  id: number;
  name: string;
  symbol: string;
  unitGroupId: number;
}

export interface UnitResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  name: string;
  symbol: string;
  unitGroupName: string;
}
