import instance, {HOTEL_SERVICE} from '@app/api/instance';

export interface RoomPriceRequest {
  id?: number | any;
  key?: number;

  lkrPrice: number;
  usdPrice: number;
  stayTypeId: string | number;
  hotelId: number;
  childPolicyId: number;
}

export interface StayTypeResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export const CreateRoomPrice = (tableCategoryPayload: RoomPriceRequest): Promise<StayTypeResponse> =>
  instance.post<StayTypeResponse>(HOTEL_SERVICE + 'room-prices', {...tableCategoryPayload}).then(({data}) => data);

export const getAllRoomPrice = (
  hotelId: number,
  searchFeilds: any,
  currentPage?: number,
  pageSize?: number,
): Promise<StayTypeResponse> =>
  instance
    .get<StayTypeResponse>(
      HOTEL_SERVICE +
        `room-prices/search?page=${currentPage}&size=${pageSize}&sortField=id&direction=DESC&roomType=${
          searchFeilds?.roomType ? searchFeilds?.roomType : ''
        }&stayType=${searchFeilds?.stayType ? searchFeilds?.stayType : ''}&usdPrice=${
          searchFeilds?.usdPrice ? searchFeilds?.usdPrice : ''
        }&lkrPrice=${searchFeilds?.lkrPrice ? searchFeilds?.lkrPrice : ''}&hotelId=${hotelId}`,
    )
    .then(({data}) => data);

export const UpdateRoomPrice = (tableCategoryPayload: RoomPriceRequest): Promise<StayTypeResponse> =>
  instance.put<StayTypeResponse>(HOTEL_SERVICE + 'room-prices', {...tableCategoryPayload}).then(({data}) => data);

export const DeleteRoomPrice = (id: number): Promise<StayTypeResponse> =>
  instance.delete<StayTypeResponse>(HOTEL_SERVICE + `room-prices/${id}`).then(({data}) => data);
