// import instance, {RESTAURANT_SERVICE} from '@app/api/instance';
import restInstance, {RESTAURANT_SERVICE} from '../resturantInstance';

export const CreateRestTax = (payload: CreateRestTaxProps): Promise<RestTaxResponse> => {
  return restInstance.post<RestTaxResponse>(RESTAURANT_SERVICE + 'tax', payload).then(({data}) => data);
};

export const UpdateRestTax = (payload: UpdateRestTaxProps): Promise<RestTaxResponse> => {
  return restInstance.put<RestTaxResponse>(RESTAURANT_SERVICE + 'tax', payload).then(({data}) => data);
};

export const getAllRestTaxes = (hotelServiceId: number): Promise<RestTaxResponse> =>
  restInstance.get<RestTaxResponse>(RESTAURANT_SERVICE + `taxes/${hotelServiceId}`).then(({data}) => data);

export const DeleteRestTax = (id: number): Promise<RestTaxResponse> =>
  restInstance.delete<RestTaxResponse>(RESTAURANT_SERVICE + `tax/${id}`).then(({data}) => data);

export const UpdateRestTaxSquence = (payload: TaxSequenceUpdateProps[]): Promise<RestTaxResponse> => {
  return restInstance.put<RestTaxResponse>(RESTAURANT_SERVICE + 'tax-sequence', payload).then(({data}) => data);
};

export const getAllRestTaxesByHotelId = (hotelId: number): Promise<RestTaxResponse> =>
  restInstance.get<RestTaxResponse>(RESTAURANT_SERVICE + `tax/hotel/${hotelId}`).then(({data}) => data);

export interface CreateRestTaxProps {
  name: string;
  rate: number;
  formula: string;
  sequence: number;
  serviceCharge: boolean;
  element?: string;
  active: boolean;
  hotelId: number;
  hotelServiceId: number;
  socialSecurityContributionLevy: boolean;
  vat: boolean;
  startDate: string;
}

export interface UpdateRestTaxProps {
  id: number;
  name: string;
  rate: number;
  formula: string;
  sequence: number;
  serviceCharge: boolean;
  element?: string;
  active: boolean;
  hotelId: number;
  hotelServiceId: number;
  socialSecurityContributionLevy: boolean;
  vat: boolean;
  startDate: string;
}

export interface RestTaxResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface TaxSequenceUpdateProps {
  id: number;
  sequenceNumber: number;
}
