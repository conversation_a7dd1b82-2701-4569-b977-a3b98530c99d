import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const CreateOnlineChannel = (payload: CreateOnlineChannelProps): Promise<OnlineChannelResponse> => {
  return instance.post<OnlineChannelResponse>(HOTEL_SERVICE + 'online-channels', payload).then(({data}) => data);
};

export const UpdateOnlineChannel = (payload: UpdateOnlineChannelProps): Promise<OnlineChannelResponse> => {
  return instance.put<OnlineChannelResponse>(HOTEL_SERVICE + 'online-channels', payload).then(({data}) => data);
};

export const getAllOnlineChannels = (
  {name, url}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<OnlineChannelResponse> =>
  instance
    .get<OnlineChannelResponse>(
      HOTEL_SERVICE +
        `online-channel/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${
          name ? name : ''
        }&url=${url ? url : ''}`,
    )
    .then(({data}) => data);

export const DeleteOnlineChannel = (id: number): Promise<OnlineChannelResponse> =>
  instance.delete<OnlineChannelResponse>(HOTEL_SERVICE + `online-channels/${id}`).then(({data}) => data);

export interface CreateOnlineChannelProps {
  name: string;
  url: string;
}

export interface UpdateOnlineChannelProps {
  id: number;
  name: string;
  url: string;
}

export interface OnlineChannelResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  name: string;
  url: string;
}
