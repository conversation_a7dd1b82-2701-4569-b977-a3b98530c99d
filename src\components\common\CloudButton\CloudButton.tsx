import React from 'react';
import styled from 'styled-components';

const Button = styled.div`
  text-decoration: none;
  color: white;
  padding: 10px;
  text-transform: uppercase;
  display: inline-block;
  text-shadow: -2px 2px 0 rgba(0, 0, 0, 0.2);
  font-weight: bold;
  padding-right: 10px;
  // margin: 10px;
  transition: all 0.1s linear;
  transform: translateZ(0);
  position: relative;
  cursor: pointer;

  &.blue {
    background: linear-gradient(to bottom, #a2d3e9, #7abedf);
    box-shadow: -1px 0px 1px #6fadcb, 0px 1px 1px #54809d, -2px 1px 1px #6fadcb, -1px 2px 1px #54809d,
      -3px 2px 1px #6fadcb, -2px 3px 1px #54809d, -4px 3px 1px #6fadcb, -3px 4px 1px #54809d, -5px 4px 1px #6fadcb,
      -4px 5px 1px #54809d, -6px 5px 1px #6fadcb, -6px 7px 0 rgba(0, 0, 0, 0.05), -5px 8px 0 rgba(0, 0, 0, 0.05),
      -3px 9px 0 rgba(0, 0, 0, 0.04), -2px 10px 0 rgba(0, 0, 0, 0.04), -1px 11px 0 rgba(0, 0, 0, 0.03),
      0px 12px 0 rgba(0, 0, 0, 0.03), 1px 13px 0 rgba(0, 0, 0, 0.02), 2px 14px 0 rgba(0, 0, 0, 0.02),
      3px 15px 0 rgba(0, 0, 0, 0.01), 4px 16px 0 rgba(0, 0, 0, 0.01), 5px 17px 0 rgba(0, 0, 0, 0.01),
      6px 18px 0 rgba(0, 0, 0, 0.01), inset 0 4px 5px -2px rgba(255, 255, 255, 0.5), inset 0 1px 0 0 rgba(0, 0, 0, 0.3);

    &::after,
    &::before {
      background: black;
    }

    &::after {
      filter: drop-shadow(-2px 0 0 rgba(255, 255, 255, 0.4));
    }

    &::before {
      filter: drop-shadow(0 -2px 0 rgba(255, 255, 255, 0.35));
    }

    .arrow {
      filter: drop-shadow(-2px 0 0 rgba(0, 0, 0, 0.2));
    }
  }

  &.yellow {
    background: linear-gradient(to bottom, #33b249, #33b249);
    color: black;
    text-shadow: -2px 2px 0 rgba(255, 255, 255, 0.3);
    box-shadow: -1px 0px 1px #25a93b, 0px 1px 1px #25a93b, -2px 1px 1px #25a93b, -1px 2px 1px #25a93b,
      -3px 2px 1px #25a93b, -2px 3px 1px #25a93b, -4px 3px 1px #25a93b, -3px 4px 1px #25a93b, -5px 4px 1px #25a93b,
      -4px 5px 1px #25a93b, -6px 5px 1px #25a93b, -6px 7px 0 rgba(0, 0, 0, 0.05), -5px 8px 0 rgba(0, 0, 0, 0.05),
      -3px 9px 0 rgba(0, 0, 0, 0.04), -2px 10px 0 rgba(0, 0, 0, 0.04), -1px 11px 0 rgba(0, 0, 0, 0.03),
      0px 12px 0 rgba(0, 0, 0, 0.03), 1px 13px 0 rgba(0, 0, 0, 0.02), 2px 14px 0 rgba(0, 0, 0, 0.02),
      3px 15px 0 rgba(0, 0, 0, 0.01), 4px 16px 0 rgba(0, 0, 0, 0.01), 5px 17px 0 rgba(0, 0, 0, 0.01),
      6px 18px 0 rgba(0, 0, 0, 0.01), inset 0 4px 5px -2px rgba(255, 255, 255, 0.5), inset 0 1px 0 0 rgba(0, 0, 0, 0.3);

    &::after,
    &::before {
      background: black;
    }

    &::after {
      filter: drop-shadow(-2px 0 0 rgba(255, 255, 255, 0.4));
    }

    &::before {
      filter: drop-shadow(0 -2px 0 rgba(255, 255, 255, 0.35));
    }
    .arrow {
      filter: drop-shadow(-2px 0 0 rgba(0, 0, 0, 0.2));
    }
  }

  &:active {
    box-shadow: none;
    transform: translate3d(-6px, 6px, 0);
  }

  .arrow {
    filter: drop-shadow(-2px 0 0 rgba(0, 0, 0, 0.2));
  }

  &::after {
    filter: drop-shadow(-2px 0 0 rgba(0, 0, 0, 0.2));
  }

  &::after,
  &::before {
    position: absolute;
    right: 15px;
    top: 14px;
    // width: 6px;
    height: 18px;
    background: white;
    transform: rotate(-45deg);
    display: block;
    z-index: 2;
  }

  &::before {
    height: 14px;
    top: 26px;
    right: 16px;
    z-index: 3;
    transform: rotate(-137deg);
    filter: drop-shadow(0 -2px 0 rgba(0, 0, 0, 0.15));
  }
`;

const ButtonWithEffect = ({children, color, onClick}: any) => {
  return (
    <Button onClick={onClick} className={`button ${color}`}>
      {children}
    </Button>
  );
};

export default ButtonWithEffect;
