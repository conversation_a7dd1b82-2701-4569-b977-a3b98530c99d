import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const CreatePolicy = (payload: CreatePolicyProps): Promise<PolicyResponse> => {
  return instance.post<PolicyResponse>(HOTEL_SERVICE + 'cancellation', payload).then(({data}) => data);
};

export const UpdatePolicy = (payload: UpdatePolicyProps): Promise<PolicyResponse> => {
  return instance.put<PolicyResponse>(HOTEL_SERVICE + 'cancellation', payload).then(({data}) => data);
};

export const getAllPolicies = (hotelId: number): Promise<PolicyResponse> =>
  instance.get<PolicyResponse>(HOTEL_SERVICE + `cancellation/hotel/${hotelId}`).then(({data}) => data);

export const getAllPolicieswithPagination = (
  hotelId: number,
  {
    name,
    noOfDays,
    percentage,
    policyName,
    channelIds,
    channelNames,
    seasonIds,
    seasonNames,
    channelName,
    seasonName,
  }: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<PolicyResponse> =>
  instance
    .get<PolicyResponse>(
      HOTEL_SERVICE +
        `cancellation/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${
          name ? name : ''
        }&noOfDays=${noOfDays ? noOfDays : ''}&percentage=${
          percentage ? percentage : ''
        }&hotelId=${hotelId}&seasonName=${seasonName ? seasonName : ''}`,
    )
    .then(({data}) => data);

export const DeletePolicy = (id: number): Promise<PolicyResponse> =>
  instance.delete<PolicyResponse>(HOTEL_SERVICE + `cancellation/${id}`).then(({data}) => data);

export interface CreatePolicyProps {
  name: string;
  noOfDays: number;
  percentage: number;
  hotelId: number;
  // reservationTypeIds: number[];
  channelIds: React.Key[];
  seasonId: number[];
}

export interface UpdatePolicyProps {
  id: number;
  name: string;

  noOfDays: number;
  percentage: number;
  hotelId: number;
  // reservationTypeIds: number[];
  channelIds: React.Key[];
  seasonId: number[];
}

export interface PolicyResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  name: string;
  noOfDays: string;
  percentage: string;
  policyName: string;
  channelIds: number[];
  channelNames: string[];
  seasonIds: number[];
  seasonNames: string[];
  channelName: string;
  seasonName: string;
}
