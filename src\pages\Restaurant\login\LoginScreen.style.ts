import styled from 'styled-components';
import {Form, Input, Button, Typography} from 'antd';
const {Title} = Typography;

// Existing styles
export const LoginLayout = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #2c4b3c;
`;

export const LoginContent = styled.div`
  width: 90%;
  max-width: 900px;
  height: 85vh;
  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: 1024px) {
    max-width: 90%;
    height: 50vh;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    height: auto;
    padding: 80px 0;
  }

  @media (min-width: 1440px) {
    max-width: 1200px;
    height: 80vh;
  }
`;

export const LoginContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  font-family: 'Inter', serif;
`;

export const LeftPanel = styled.div`
  flex: 1;
  position: relative;
  overflow: hidden;

  @media (max-width: 768px) {
    display: none;
  }
`;

export const BackgroundImage = styled.div<{image: string}>`
  width: 100%;
  height: 100%;
  background-image: url(${props => props.image});
  background-size: cover;
  background-position: center;
`;

export const RightPanel = styled.div`
  flex: 1;
  background-color: white;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const GradientCircle = styled.div<{
  size: number;
  top?: string;
  right?: string;
  bottom?: string;
  left?: string;
  opacity: number;
}>`
  position: absolute;
  width: ${props => props.size}px;
  height: ${props => props.size}px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2c4b3c, #365b4a);
  opacity: ${props => props.opacity};
  top: ${props => props.top || 'auto'};
  right: ${props => props.right || 'auto'};
  bottom: ${props => props.bottom || 'auto'};
  left: ${props => props.left || 'auto'};
`;

export const LoginFormContainer = styled.div`
  width: 80%;
  max-width: 400px;
  z-index: 1;
`;

export const StyledTitle = styled(Title)`
  margin-bottom: 30px !important;
  text-align: center;
  color: #333;
`;

export const StyledForm = styled(Form)`
  width: 100%;
`;

export const InputLabel = styled.span`
  font-weight: 500;
  color: #555;
`;

export const StyledInput = styled(Input)`
  height: 45px;
  border-radius: 6px;
  margin-bottom: 5px;
`;

export const StyledPasswordInput = styled(Input.Password)`
  height: 45px;
  border-radius: 6px;
  margin-bottom: 5px;
`;

export const ForgotPasswordContainer = styled.div`
  text-align: right;
  margin-bottom: 20px;
`;

export const ForgotPasswordLink = styled.a`
  color: #2c4b3c;
  font-size: 14px;
  transition: color 0.3s;

  &:hover {
    color: #365b4a;
  }
`;

export const LoginButton = styled(Button)`
  width: 100%;
  height: 45px;
  border-radius: 6px;
  background: linear-gradient(135deg, #2c4b3c, #365b4a);
  border: none;
  font-weight: 500;
  box-shadow: 0 5px 15px #2c4b3c;
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    background: linear-gradient(135deg, #2c4b3c, #365b4a);
    transform: translateY(-2px);
    box-shadow: 0 7px 18px #2c4b3c;
  }
`;

export const RegisterSection = styled.div`
  margin-top: 20px;
  text-align: center;
`;

export const AccountQuestion = styled.span`
  color: #777;
  font-size: 14px;
  margin-right: 5px;
`;

export const RegisterLink = styled.a`
  color: #2c4b3c;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s;

  &:hover {
    color: #365b4a;
  }
`;

export const SliderContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
`;

export const SliderImage = styled.div<{image: string; active: boolean}>`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(${props => props.image});
  background-size: cover;
  background-position: center;
  opacity: ${props => (props.active ? 1 : 0)};
  transition: opacity 0.8s ease-in-out;
`;

export const SliderDots = styled.div`
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 2;
`;

export const SliderDot = styled.div<{active: boolean}>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: ${props => (props.active ? '#ffffff' : 'rgba(255, 255, 255, 0.5)')};
  cursor: pointer;
  transition: background-color 0.3s;
`;
