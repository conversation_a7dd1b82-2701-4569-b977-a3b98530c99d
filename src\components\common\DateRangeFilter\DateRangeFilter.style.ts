import styled from 'styled-components';

export const SearchWrapper = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  width: 100%;
  background-color: #e5e5e5;
  padding: 0.3rem;
  border: 1px solid cadetblue;
  border-radius: 0px;
  align-items: center;
`;

export const DateSearchWrapper = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  width: auto;
  background-color: #ffff;
  padding: 0.5rem;
  // border: 1px solid cadetblue;
  border-radius: 5px;
  margin: 0 auto;
  text-align: center;
  -webkit-box-shadow: -2px 41px 76px -51px rgba(168, 168, 168, 1);
  -moz-box-shadow: -2px 41px 76px -51px rgba(168, 168, 168, 1);
  box-shadow: -2px 41px 76px -51px rgba(168, 168, 168, 1);
`;

export const FilterWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  justify-content: flex-end;
`;

export const FilterBtn = styled.div`
  border: solid 1px black;
  align-items: center;

  height: 2rem;
  width: 2rem;
  border-radius: 5px;
  background-color: white;
  box-shadow: 5px 2px 5px #e9e2e2;
  cursor: pointer;
`;
