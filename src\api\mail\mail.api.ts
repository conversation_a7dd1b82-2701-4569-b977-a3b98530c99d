import instance, {HOTEL_SERVICE} from '@app/api/instance';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getMailTemplate = (url: string, pendingMailId: number | null): Promise<any> =>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  instance.get<any>(HOTEL_SERVICE + `${url}/get/${pendingMailId}`).then(({data}) => data);

export const sendMailTemplate = (url: string, payload: Props): Promise<any> =>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  instance.post<any>(HOTEL_SERVICE + `${url}/send`, payload).then(({data}) => data);

interface Props {
  pendingEmailId: number;
  reservationId: number;
  pendingEmailType: number;
  mailSubject: string;
  mailtoList: string[];
  mailCcList: string[];
  htmlBody: string;
}
