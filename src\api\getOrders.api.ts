export interface IOrderItem {
  id: number;
  key: string;
  uniqKey: string;
  quantity: number;
  item: string;
  itemId?: number;
  currency: string;
  itemImage: string;
  comments?: string | any;
  commentQuantity?: number | any;
  orderedItemStatus?: string;
  selelctedTableId?: number;
  totalPrice: number;
  price: any;
  kotId?: number;
  hotelServiceId?: number;
  orderCompletedTime?: any;
  category:
    | 'FOOD'
    | 'Beverages'
    | 'DRINKS'
    | 'DESSERTS'
    | 'BREAKFAST'
    | 'FRESH JUICE'
    | 'BIRYANI'
    | 'KOTTU ROTTI'
    | 'FRIED RICE'
    | 'NOODLES'
    | 'RICE & CURRY'
    | 'CAKES'
    | 'ICE CREAMS'
    | 'CHOCOLATES'
    | 'ALCOHOLIC DRINKS'
    | 'SOFT DRINKS'
    | 'TEA TIME'
    | 'BISCUITS'
    | 'NUTS'
    | 'CHIPS'
    | 'HOPPERS'
    | 'ROTTI'
    | 'ROTI'
    | 'BUN'
    | 'PANCAKES'
    | any;
  active: boolean;
}

export interface IChefOrderItem {
  id: number;
  quantity: number;
  item: string;
  price: number;
  currency: string;
  itemImage: string;
  comments?: string;
  category:
    | 'FOOD'
    | 'Beverages'
    | 'DRINKS'
    | 'DESSERTS'
    | 'BREAKFAST'
    | 'FRESH JUICE'
    | 'BIRYANI'
    | 'KOTTU ROTTI'
    | 'FRIED RICE'
    | 'NOODLES'
    | 'RICE & CURRY'
    | 'CAKES'
    | 'ICE CREAMS'
    | 'CHOCOLATES'
    | 'ALCOHOLIC DRINKS'
    | 'SOFT DRINKS'
    | 'TEA TIME'
    | 'BISCUITS'
    | 'NUTS'
    | 'CHIPS'
    | 'HOPPERS'
    | 'ROTTI'
    | 'ROTI'
    | 'BUN'
    | 'PANCAKES'
    | any;
  dateTime?: string;
  status: 'NEW' | 'READY' | 'PREPARING' | 'REJECTED';
  kot: string;
}

export interface ITableChefOrderItem {
  orderId: number;
  tableNumber?: string;
  kot: string;
  items: IChefOrderItem[];
  waiterName?: string;
  dateTime?: string;
  status: 'NEW' | 'READY' | 'PREPARING' | 'CANCELED';
}

export interface IOrders {
  orderId: number;
  ticket: string;
  status: string;
  time: string;
  customerName: string;
  items: IOrderItem[];
}
