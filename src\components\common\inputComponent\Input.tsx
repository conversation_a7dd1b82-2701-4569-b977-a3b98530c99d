import {FC, useState} from 'react';
import './Input.style.css';
import 'react-datepicker/dist/react-datepicker.css';

interface DateInputProps {
  label: 'Check In' | 'Check Out' | 'Adults' | 'Children';
  day?: number;
  month?: string;
  year?: number;
  type: 'datepicker' | 'inputpicker' | 'selectpicker';
  onClick?: () => void;
  ref?: any;
}

const DateInput: FC<DateInputProps> = ({label = 'Check In', day, month, year, type = 'inputpicker', onClick, ref}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleClick = () => {
    setIsOpen(!isOpen);
    if (onClick) {
      onClick();
    }
  };

  return (
    <>
      <div onClick={handleClick} ref={ref} className="input-container">
        <div className="label">{label}</div>
        <div className="inner-container">
          {type === 'datepicker' && (label === 'Check In' || label === 'Check Out') ? (
            <div className="date">
              <div className="date-in-number">{day}</div>
              <div className="month-year">
                <div className="month">{month}</div>
                <div className="year">{year}</div>
              </div>
            </div>
          ) : null}

          {/* {type === 'selectpicker' ? (
            label === 'Adults' ? (
              <div className="selected-number">{selectedNumber !== null ? selectedNumber : 2}</div>
            ) : (
              <div className="selected-number">{selectedNumber !== null ? selectedNumber : 0}</div>
            )
          ) : null} */}
          <div className="icon"></div>
        </div>
      </div>

      {/* {isOpen && type === 'selectpicker' && (
        <div className="dropdown">
          {numberOptions.map(num => (
            <div key={num} className="dropdown-option" onClick={() => handleSelect(num)}>
              {num}
            </div>
          ))}
        </div>
      )} */}
    </>
  );
};

export default DateInput;
