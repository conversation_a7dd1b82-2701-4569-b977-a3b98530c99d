import {LAYOUT} from '@app/styles/themes/constants';
import {Col, Row, Image, Card, Avatar, Typography, Badge, Space} from 'antd';
import {useTimer} from 'react-timer-hook';
import React, {useState} from 'react';
import * as S from './DashboardPage.styles';
import styled from 'styled-components';
import {IChefOrder, IItems} from './chefMockData';
import {IChefOrderItem} from '@app/api/getOrders.api';
import {Button} from '@app/components/common/buttons/Button/Button';
import {useStopwatch} from 'react-timer-hook';
// import {chefOrderList} from '@app/utils/utils';
import {filter, find, isEmpty, sortBy, unionBy} from 'lodash';
import _ from 'lodash';
import ChefOrderCard from './ChefOrderCard';
import 'react-responsive-carousel/lib/styles/carousel.min.css'; // requires a loader
import {Carousel} from 'react-responsive-carousel';
import './ChefDashBoardStyle.css';
import {Card as CommonCard} from '@app/components/common/Card/Card';

import SockJS from 'sockjs-client';
import {Stomp} from '@stomp/stompjs';
import {RESTAURANT_WS_SERVICE} from '@app/api/resturantInstance';
import {
  GetResponse,
  PrepareTimeCard,
  PrepareTimeTable,
  ReadyToServe,
  acceptAllOrders,
  getAllCountStatus,
  getAllPlacedOrders,
  getAllPlacedOrdersNew,
  rejectAllOrderItems,
  // rejectOrderItem,
  rejectOrderItemNew,
  requestReConfirmation,
} from './chef.api';
// import {PlusOutlined} from '@ant-design/icons';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {Modal} from '@app/components/common/Modal/Modal';
// import {Input} from '@app/components/common/inputs/Input/Input.styles';
import {TextArea} from '@app/components/common/inputs/Input/Input';
import {NOTIFY_SOUND} from '@app/assets';
// import {updateOrder} from '@app/api/resturant/tablecategory/order.api';
import {notificationController} from '@app/controllers/notificationController';
import {PageTitle} from '@app/components/common/PageTitle/PageTitle';
import {ShadowedButton} from '../Restaurant/WaiterDashboard/RightArea';
import Tooltip from '@app/components/common/Tooltip/Tooltip';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {setChefSliderIndex} from '../Restaurant/slices/waiterDasboardSlice';
import DashboardStats from './chef/DashboardStats';
import AllocateTime from './AllocateTime';
// import {duration} from '@mui/material';

const {Text} = Typography;

export const ChefDashboardLayout = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;
  font-family: 'Poppins', serif;
  margin-bottom: 6px;
`;

export const DashboardStatsSection = styled.div`
  width: 100%;
  top: -9px;
  z-index: 10;
  font-family: 'Poppins', serif;
`;

export const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 1rem;
  padding-top: 15px;
  padding-bottom: 0px;

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
`;

export const LeftSideCol = styled.div`
  padding: 0rem 1rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  border-radius: 10px;
  width: 100%;
  border: '0.2px solid gray';

  .carousel .thumbs-wrapper {
    margin: 0px !important;
    overflow: hidden;
  }

  .carousel .control-arrow {
    height: 96%;
  }
  @media (max-width: 768px) {
    height: 90%;
  }
`;

export const RightSideCol = styled.div`
  height: 68vh;
  border-radius: 10px;
  // background-color: white;
  // box-shadow: 0px 0px 8px #ccc;
  overflow-y: auto;

  @media (max-width: 1366px) and (min-height: 1024px) {
    height: 80vh;
  }

  @media (max-width: 1024px) {
    max-height: 61vh;
    margin-top: -1px;
  }

  @media (max-width: 834px) {
    max-height: 42vh;
  }

  @media (max-width: 768px) {
    max-height: 35vh;
    margin-top: -30px;
  }
`;

// Order Item Card
export const OrderItem = styled(Card)`
  min-height: 80px;
  border-radius: 15px;
  -webkit-box-shadow: 0px 0px 18px -4px rgba(0, 0, 0, 0.32);
  -moz-box-shadow: 0px 0px 18px -4px rgba(0, 0, 0, 0.32);
  box-shadow: 0px 0px 20px -13px rgba(0, 0, 0, 0.32);
`;

// Scrollable card container
export const ScrollCardWrapper = styled.div`
  overflow-y: auto;
  overflow-x: hidden;
  height: 280px;
  padding: 20px;

  .ant-card-body {
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
  }

  .ant-ribbon.ant-ribbon-placement-start {
    left: 9px;
    border-bottom-left-radius: 0;
  }
`;

export const RejectButton = styled(Button)`
  &.ant-btn-dangerous.ant-btn-primary[disabled],
  .ant-btn-dangerous.ant-btn-primary[disabled]:active,
  .ant-btn-dangerous.ant-btn-primary[disabled]:focus,
  .ant-btn-dangerous.ant-btn-primary[disabled]:hover {
    color: var(--disabled-color) !important;
    border-color: var(--border-base-color) !important;
    background: var(--disabled-bg-color) !important;
    text-shadow: none;
    box-shadow: none;
  }

  &.ant-btn-dangerous.ant-btn-primary {
    color: #fff;
    border-color: var(--ant-error-color);
    background: rgb(253 61 61);
  }

  &.ant-btn-dangerous.ant-btn-primary:focus,
  &.ant-btn-dangerous.ant-btn-primary:hover {
    color: #fff;
    border-color: #df241c;
    background: #df241c;
  }

  width: 70px;
  font-size: 0.75rem;
  height: 32px;
  font-family: 'Poppins', sans-serif;
  border-radius: 6px;
  letter-spacing: 1px;
`;

export const AcceptButton = styled(Button)`
  &.ant-btn-primary[disabled],
  .ant-btn-primary[disabled]:active,
  .ant-btn-primary[disabled]:focus,
  .ant-btn-primary[disabled]:hover {
    color: var(--disabled-color) !important;
    border-color: var(--border-base-color) !important;
    background: var(--disabled-bg-color) !important;
    text-shadow: none;
    box-shadow: none;
  }

  &.ant-btn-primary {
    color: #fff;
    border-color: #28a745;
    background: #28a745;
  }

  &.ant-btn-primary:focus,
  &.ant-btn-primary:hover {
    color: #fff;
    border-color: #1e7e34;
    background: #1e7e34;
  }
  font-size: 0.9rem;
  height: 32px;
  font-family: 'Poppins', sans-serif;
  border-radius: 6px;
  letter-spacing: 1px;
`;

// Reconfirm Order Button
export const ReconfirmButton = styled(Button)`
  &.ant-btn-warning[disabled],
  .ant-btn-warning[disabled]:active,
  .ant-btn-warning[disabled]:focus,
  .ant-btn-warning[disabled]:hover {
    color: var(--disabled-color) !important;
    border-color: var(--border-base-color) !important;
    background: var(--disabled-bg-color) !important;
    text-shadow: none;
    box-shadow: none;
  }

  &.ant-btn-warning {
    color: #fff;
    border-color: #f0ad4e; /* Orange border */
    background: #f0ad4e; /* Orange background */
  }

  &.ant-btn-warning:focus,
  &.ant-btn-warning:hover {
    color: #fff;
    border-color: #ec971f; /* Darker orange on hover */
    background: #ec971f;
  }

  border-radius: 6px;
  width: 25%;
  font-size: 15px;
  height: 35px;
  border: none;
  margin: 0.5rem;
  font-family: 'Poppins', sans-serif;
`;

export const CountCard = styled(Card)`
  width: 95%;
  align-self: end;
  height: 77px;
  display: flex;
  margin: 6px;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.75);

  .ant-card-body {
    padding: 0px !important;
  }
`;

export const CustomCard = styled(CommonCard)`
  width: 100%;
  margin-bottom: 1.25rem;
  .ant-card-head-title {
    font-size: 1.5rem;
  }
  .ant-card-body {
    display: flex;
    flex-wrap: wrap;
    gap: 1.25rem;
    align-items: center;
    padding: 10px 40px;
  }
  .ant-card-body:before {
    display: none;
  }
  .ant-card-body:after {
    display: none;
  }
  &.ant-card-bordered {
    border: 1px solid var(--border-color);
  }
`;

const ChefDashBoard = () => {
  const [form] = BaseForm.useForm();
  const chefSilderIndex = useAppSelector(state => state.waiterDasbordSlice.chefSilderIndex);
  const dispatch = useAppDispatch();
  const [selectedItems, setSelectedItems] = React.useState<IChefOrder[]>([]);
  const [selectedItemsTable, setSelectedItemsTable] = React.useState<IChefOrder[]>([]);
  const [currentItems, setCurrentItems] = React.useState<any>([]);
  const [newItems, setNewItems] = React.useState<any>([]);
  const [currentIndex, setCurrentIndex] = React.useState<number>(0);

  const [openReason, setOpenReason] = React.useState<boolean>(false);

  const [selectedOrder, setSelectedOrder] = React.useState<any>();
  const [selectedOrderItem, setSelectedOrderItem] = React.useState<any>();
  const [tableNumber, setTableNumber] = React.useState<any>();
  const [dashboardCount, setDashboardCount] = React.useState<{
    accepted: number;
    beingPrepared: number;
    placed: number;
    readyToServe: number;
    requestConfirmation: number;
  }>({accepted: 0, beingPrepared: 0, placed: 0, readyToServe: 0, requestConfirmation: 0});

  const [newReceivedItems, setNewReceivedItems] = React.useState<any[]>([]);
  const [enableRequest, setEnablerequest] = useState<boolean>(false);
  const [reconfirmTables, setReconfirmTables] = useState<string[]>([]);
  const [reason, setreason] = useState('');
  const [newOrderItemCount, setNewOrderItemCount] = React.useState<number>(0);

  const hotelServiceConfig = useAppSelector(state => state.hotelSlice.hotelServiceConfig);

  React.useEffect(() => {
    getAllChefOrders({});
    getAllProcessChefOrders();
    GetDashboardCounts();
  }, []);

  React.useEffect(() => {
    if (selectedItems.length > 0 && selectedItems.length - 1 < chefSilderIndex) {
      dispatch(setChefSliderIndex({index: selectedItems.length - 1}));
    }
  }, [selectedItems, newReceivedItems]);

  const GetDashboardCounts = async () => {
    try {
      const result: any = await getAllCountStatus(hotelServiceConfig.serviceId);

      setDashboardCount(result?.result?.orderCount);
    } catch (error) {}
  };

  React.useEffect(() => {
    let stompClient: any = null;

    const WebSocketClient = (url: string, tableIndex: number) => {
      const sock = new SockJS(RESTAURANT_WS_SERVICE);
      stompClient = Stomp.over(sock);

      sock.onopen = function () {
        console.log('onopen');
      };

      stompClient.connect({}, (frame: any) => {
        stompClient.subscribe(url, (data: any) => {
          const receivedData: any = JSON.parse(data.body);
          const key: any = Object.keys(receivedData)[0];
          const action: any = receivedData[key];

          if (receivedData && action === 'CANCELLED') {
            dispatch(setChefSliderIndex({index: 0}));
          }
          setNewReceivedItems(receivedData);
          if (receivedData) {
            getAllChefOrders({});
            getAllProcessChefOrders();
            GetDashboardCounts();
          }

          if (receivedData) {
            const audio = new Audio(NOTIFY_SOUND);
            audio.play();
          }
        });
      });
      stompClient.activate();
    };

    WebSocketClient(`/chef/${hotelServiceConfig.serviceId}`, chefSilderIndex);

    return () => {
      if (stompClient) {
        stompClient.disconnect();
      }
    };
  }, []);

  const getAllProcessChefOrders = async () => {
    try {
      const results: GetResponse = await getAllPlacedOrders(hotelServiceConfig.serviceId);
      const orderResults: any = results.result.order;

      setSelectedItemsTable(orderResults);
    } catch (error) {}
  };

  const getAllChefOrders = async (item?: any, isMount?: boolean) => {
    try {
      const results: GetResponse = await getAllPlacedOrdersNew(hotelServiceConfig.serviceId);
      const orderResults = results.result.orderItems;

      const sorterData = sortBy(orderResults, 'tableId');
      const orderResultsLength = sorterData?.length;
      // setSelectedItemsTable(orderResults);
      if (currentIndex > orderResultsLength) {
        dispatch(setChefSliderIndex({index: orderResultsLength > 0 ? orderResultsLength - 1 : 0}));
      }

      setSelectedItems(sorterData);
      const requestConfirmData = filter(sorterData, order => order.orderStatus === 'REQUEST_CONFIRMATION');
      const requestConfirmTables = requestConfirmData.map(post => post.tableNumber);
      const iniItemOrderCount = filter(sorterData, (item: any, index: number) =>
        hasNewOrderedItem(item.orderedItemResponseList),
      ).length;
      const itemListLength = filter(
        sorterData,
        (item: any, index: number) =>
          hasNewOrderedItem(item.orderedItemResponseList) && item.orderStatus !== 'REQUEST_CONFIRMATION',
      ).length;

      setNewOrderItemCount(itemListLength);
      setReconfirmTables(requestConfirmTables);
      if (iniItemOrderCount === 1) {
        setCurrentIndex(0);
      }
      setTableNumber(
        filter(sorterData, (item: any, index: number) => hasNewOrderedItem(item.orderedItemResponseList))[0]
          .tableNumber,
      );

      const filterData = filter(sorterData, (item: any, index: number) =>
        hasNewOrderedItem(item.orderedItemResponseList),
      );

      if (!selectedOrder && filterData.length > 0) {
        setSelectedOrder(filterData[0]);
      }

      const defaultData = [...filterData];
      if (tableNumber === undefined) {
        if (filterData.length > 0) {
          const tableOrderedItemResponseList = filterData[0].orderedItemResponseList;
          const findObj = find(tableOrderedItemResponseList, res => res.orderedItemStatus === 'REJECTED');
          if (findObj) {
            setEnablerequest(true);
          } else {
            setEnablerequest(false);
          }
        }
      } else {
        const tableOrderedItemResponseList = find(
          defaultData,
          (item: any, index: number) => item.tableNumber === tableNumber,
        );
        const findObj = find(
          tableOrderedItemResponseList?.orderedItemResponseList,
          res => res.orderedItemStatus === 'REJECTED',
        );
        if (findObj) {
          setEnablerequest(true);
        } else {
          setEnablerequest(false);
        }
      }
    } catch (error) {}
  };

  const rejectAll = async (orderId: number) => {
    try {
      const result: GetResponse = await rejectAllOrderItems(orderId);
      if (result.statusCode === '20000') {
        notificationController.success({message: result.message});
        await getAllChefOrders({});
      } else {
        notificationController.error({message: result.message});
      }
    } catch (error) {}
  };

  const onClickReject = async (item: IItems, post: IChefOrder, rejectReson: string) => {
    try {
      const result = await rejectOrderItemNew(item.id, rejectReson);
      if (result.statusCode === '20000') {
        notificationController.success({message: result.message});
        await getAllChefOrders({});
        setreason('');
      } else {
        notificationController.error({message: result.message});
      }

      setOpenReason(false);
      form.resetFields();
    } catch (error) {}
  };

  const onChangeCard = (value: number) => {
    dispatch(setChefSliderIndex({index: value}));
    const selectedData: any = filter(selectedItems, (item: any) => hasNewOrderedItem(item.orderedItemResponseList));

    const selectedItem = filter(selectedData, (item: IChefOrder, index: number) => value === index)[0];

    setSelectedOrder(selectedItem);
    setTableNumber(selectedItem.tableNumber);
    setCurrentItems(selectedItem);
    setCurrentIndex(value);
    const defaultData = [...selectedItems];
    const tableOrderedItemResponseList = find(
      defaultData,
      (item: any, index: number) => item.tableNumber === selectedItem.tableNumber,
    );
    const findObj = find(
      tableOrderedItemResponseList?.orderedItemResponseList,
      res => res.orderedItemStatus === 'REJECTED',
    );

    if (findObj) {
      setEnablerequest(true);
    } else {
      setEnablerequest(false);
    }
  };

  const handleClose = () => {
    setOpenReason(false);
    form.resetFields();
  };

  const handleAcceptAll = async (data: any) => {
    const result = await acceptAllOrders(data.id);

    if (result.statusCode === '20000') {
      notificationController.success({message: 'Order accepted successfully'});
      dispatch(setChefSliderIndex({index: 0}));
      await getAllChefOrders({});
      await getAllProcessChefOrders();
      await GetDashboardCounts();
    } else {
      notificationController.error({message: result.message});
    }
  };

  const hasNewOrderedItem = (orderedItemResponseList: any) => {
    return orderedItemResponseList.some(
      (item: any) => item.orderedItemStatus === 'NEW' || item.orderedItemStatus === 'REJECTED',
    );
  };

  const requestForRejection = async () => {
    const payload = {
      id: selectedOrder?.id,
      orderStatus: 'REQUEST_CONFIRMATION',
    };

    const result = await requestReConfirmation(selectedOrder?.id);

    if (result.statusCode === '20000') {
      notificationController.success({message: 'Request for reconfirm order has been sent'});
      await getAllChefOrders({});
      await GetDashboardCounts();
    } else {
      notificationController.error({message: result.message});
    }
  };

  const filteredItem = filter(
    filter(selectedItems, (item: any) => hasNewOrderedItem(item.orderedItemResponseList)),
    (item: IChefOrder, index: number) => currentIndex === index,
  )[0] as IChefOrder | undefined;

  const getTableTime = () => filteredItem?.orderTime;

  const tableLabel =
    tableNumber && selectedItems.length > 0 && filteredItem ? `Table - ${filteredItem.tableNumber}` : '';

  const newOrders = filter(selectedItems, (item: any) => hasNewOrderedItem(item.orderedItemResponseList));

  const orderMessage =
    newOrders.length === 0
      ? 'No Orders'
      : `New Order ${selectedItems.length === 0 ? 0 : currentIndex + 1} of ${newOrders.length}`;

  const handlePrepareTimeTable = async (formattedTime: string, context?: number) => {

    const payload = {
      id: context,
      prepareTime: formattedTime,
    };
    try {
      const res = await PrepareTimeTable(payload);
      if (res.statusCode === '20000') {
        notificationController.success({message: res.message});
      } else {
        notificationController.error({message: res.message});
      }
    } catch (error) {
      console.error('Failed to update prepare time:', error);
      notificationController.error({message: 'Something went wrong'});
    }
  };

  // const handlePrepareTimeCard = async (formattedTime: string, context?: number) => {
  //   console.log('Submitted Time:', formattedTime);
  //   console.log('Submitted Context:', context);

  //   const payload = {
  //     id: context,
  //     prepareTime: formattedTime,
  //   };
  //   try {
  //     const res = await PrepareTimeCard(payload);
  //     console.log('Prepare time updated successfully:', res);
  //     if (res.statusCode === '20000') {
  //       notificationController.success({message: res.message});
  //     } else {
  //       notificationController.error({message: res.message});
  //     }
  //   } catch (error) {
  //     console.error('Failed to update prepare time:', error);
  //     notificationController.error({message: 'Something went wrong'});
  //   }
  // };

  return (
    <ChefDashboardLayout>
      <DashboardStatsSection>
        <PageTitle>Chef</PageTitle>
        <DashboardStats
          dashboardCount={dashboardCount}
          newOrderItemCount={newOrderItemCount}
          reconfirmTables={reconfirmTables}
        />
      </DashboardStatsSection>
      <Row gutter={[6, 6]} style={{fontFamily: "'Inter', serif"}}>
        <Col
          xs={24}
          sm={24}
          md={24}
          lg={10}
          xl={10}
          style={{paddingTop: '17px', paddingRight: '10px', marginBottom: '20px'}}>
          <LeftSideCol>
            <div
              style={{
                color: '#000',
                height: '3rem',
                textAlign: 'center',
                margin: '10px 0',
              }}>
              <Row>
                <Col span={getTableTime() ? 8 : 4}>{getTableTime() && `Table Time : ${getTableTime()}`}</Col>
                <Col span={getTableTime() ? 11 : 14}>{orderMessage}</Col>
                <Col span={getTableTime() ? 5 : 6}>{tableLabel}</Col>
              </Row>
            </div>
            <Carousel
              selectedItem={chefSilderIndex}
              showStatus={false}
              onChange={onChangeCard}
              showIndicators={false}
              showThumbs={true}>
              {filter(selectedItems, (item: any, index: number) => hasNewOrderedItem(item.orderedItemResponseList)).map(
                (post: any, index: number) => {
                  const filterNewData = filter(
                    post.orderedItemResponseList,
                    (x: any) => x.orderedItemStatus === 'REJECTED',
                  );

                
                  const orderId = post.id;

                  const isEnab: boolean = filterNewData.length > 0 ? true : false;

                  return (
                    <div
                      key={index}
                      style={{
                        backgroundColor: '#ffffff',
                        borderRadius: '15px',
                        zIndex: 2,
                        paddingBottom: 2,
                        // boxShadow: '0px 0px 4px rgba(0, 0, 0, 0.30)',
                      }}>
                      <ScrollCardWrapper>
                        {filter(
                          post.orderedItemResponseList,
                          (item: IItems) =>
                            item.orderedItemStatus === 'NEW' ||
                            item.orderedItemStatus === 'CANCELLED' ||
                            item.orderedItemStatus === 'REJECTED',
                        ).map((item: IItems, index: number) => {
                          const orderItemId = item.id;
                           return (
                            <Badge.Ribbon
                              key={index}
                              text={
                                item.orderedItemStatus === 'NEW'
                                  ? 'New'
                                  : item.orderedItemStatus === 'REJECTED'
                                  ? 'Rejected'
                                  : item.orderedItemStatus
                              }
                              color={
                                item.orderedItemStatus === 'NEW'
                                  ? 'Green'
                                  : item.orderedItemStatus === 'REJECTED'
                                  ? 'Red'
                                  : item.orderedItemStatus
                              }
                              placement="start">
                              <div style={{width: '98%', marginLeft: 13, marginBottom: 5}}>
                                <OrderItem
                                  bodyStyle={{
                                    padding: 0,
                                    height: '100%',
                                    opacity: item.orderedItemStatus === 'REJECTED' ? 0.5 : 1,
                                  }}
                                  key={index}
                                  style={{width: '95%', marginLeft: '10px', padding: 0}}>
                                  <Row
                                    justify={'center'}
                                    style={{
                                      height: '100%',
                                      overflow: 'hidden',
                                    }}>
                                    <Col span={6}>
                                      <Avatar
                                        style={{
                                          borderTopLeftRadius: 5,
                                          borderBottomLeftRadius: 5,
                                          borderTopRightRadius: 0,
                                          borderBottomRightRadius: 0,
                                          height: '130px',
                                          width: '100%',
                                        }}
                                        shape="circle"
                                        src={item.image}
                                      />
                                    </Col>
                                    <Col span={18}>
                                      <div
                                        style={{
                                          display: 'grid',
                                          gridTemplateColumns: '1fr',
                                          gridTemplateRows: 'auto auto auto',
                                          gap: '8px',
                                          padding: '10px',
                                          fontFamily: 'Inter, sans-serif',
                                          height: '100%',
                                        }}>
                                        {/* Item Name Section */}
                                        <div style={{gridColumn: '1 / span 2'}}>
                                          <Tooltip title={item.itemName}>
                                            <div
                                              style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                              }}>
                                              <Text
                                                style={{
                                                  fontStyle: 'italic',
                                                  fontWeight: 600,
                                                  color: '#5c5c5c',
                                                  fontSize: '0.85rem',
                                                }}>
                                                Item :-
                                              </Text>
                                              <Text
                                                style={{
                                                  color: 'black',
                                                  marginLeft: '12px',
                                                  fontSize: '0.85rem',
                                                  overflow: 'hidden',
                                                  textOverflow: 'ellipsis',
                                                  whiteSpace: 'nowrap',
                                                  maxWidth: '70%',
                                                }}>
                                                {item.itemName}
                                              </Text>
                                            </div>
                                          </Tooltip>
                                        </div>

                                        {/* Remarks Section */}
                                        <div style={{gridColumn: '1 / span 2'}}>
                                          {item.remarks && (
                                            <div
                                              style={{
                                                display: 'flex',
                                                alignItems: 'flex-start',
                                              }}>
                                              <Text
                                                style={{
                                                  fontStyle: 'italic',
                                                  fontWeight: 600,
                                                  color: '#5c5c5c',
                                                  fontSize: '0.85rem',
                                                  minWidth: '4.2rem',
                                                }}>
                                                Remarks :
                                              </Text>
                                              <Text
                                                style={{
                                                  color: 'black',
                                                  fontSize: '0.85rem',
                                                  marginLeft: '12px',
                                                  overflow: 'hidden',
                                                  maxHeight: '4rem',
                                                  textAlign: 'left',
                                                }}>
                                                {item.remarks}
                                              </Text>
                                            </div>
                                          )}
                                        </div>

                                        {/* Quantity and Reject Button Section */}
                                        <div
                                          style={{
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            alignItems: 'center',
                                            marginTop: '8px',
                                          }}>
                                          <div style={{display: 'flex', alignItems: 'center'}}>
                                            <Text
                                              style={{
                                                fontSize: '0.75rem',
                                                color: '#4a4a4a',
                                                fontWeight: 600,
                                                fontStyle: 'italic',
                                              }}>
                                              Qty:
                                            </Text>
                                            <Text
                                              style={{
                                                fontSize: '0.85rem',
                                                color: '#000000',
                                                marginLeft: '8px',
                                              }}>
                                              {item.quantity}
                                            </Text>
                                          </div>
                                          <div
                                            style={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
                                            {/* Left Side Card Timer */}
                                            {/* <AllocateTime
                                              mode="Card"
                                              context={orderItemId}
                                              onPrepareTime={handlePrepareTimeCard}
                                            /> */}
                                            <RejectButton
                                              size="small"
                                              disabled={item.orderedItemStatus === 'REJECTED'}
                                              type="primary"
                                              danger
                                              onClick={() => {
                                                setOpenReason(true);
                                                setSelectedOrderItem(item);
                                                setSelectedOrder(post);
                                                form.resetFields();
                                              }}>
                                              {item.orderedItemStatus === 'REJECTED' ? 'Rejected' : 'Reject'}
                                            </RejectButton>
                                          </div>
                                        </div>
                                      </div>
                                    </Col>
                                  </Row>
                                </OrderItem>
                              </div>
                            </Badge.Ribbon>
                          );
                        })}
                      </ScrollCardWrapper>

                      <div style={{height: '40px', marginBottom: 14, borderRadius: 5}}>
                        <Row
                          style={{
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            gap: '10px',
                          }}>
                          <Col>
                            {/* Left Side Table Timer */}
                            <AllocateTime
                              mode="Table"
                              context={orderId}
                              onPrepareTime={handlePrepareTimeTable}
                              preparingTime={''}
                            />
                          </Col>
                          <Col>
                            <ReconfirmButton
                              disabled={post.orderStatus === 'REQUEST_CONFIRMATION' ? true : !isEnab}
                              style={{
                                border: 'none',
                                width: '100%',
                                backgroundColor:
                                  post.orderStatus === 'REQUEST_CONFIRMATION' ? 'gray' : !isEnab ? 'gray' : 'green',
                              }}
                              size="small"
                              onClick={() => {
                                requestForRejection();
                              }}
                              type="primary">
                              Reconfirm order
                            </ReconfirmButton>
                          </Col>
                          <Col>
                            <AcceptButton
                              size="small"
                              disabled={isEnab}
                              onClick={() => {
                                handleAcceptAll(post);
                              }}
                              style={{width: '100%', backgroundColor: isEnab ? 'gray' : 'green', border: 'none'}}
                              type="primary">
                              Accept Order
                            </AcceptButton>
                          </Col>
                        </Row>
                      </div>
                    </div>
                  );
                },
              )}
            </Carousel>
          </LeftSideCol>
        </Col>

        <Col xs={24} sm={24} md={24} lg={14} xl={14} style={{paddingTop: '17px'}}>
          <RightSideCol>
            <ChefOrderCard
              GetDashboardCounts={() => GetDashboardCounts()}
              getAllChefOrders={getAllProcessChefOrders}
              acceptedItems={selectedItemsTable}
            />
          </RightSideCol>
        </Col>
      </Row>
      <Modal
        title="Reason for rejecting order"
        width={'350px'}
        style={{
          top: '3rem',
        }}
        centered={true}
        open={openReason}
        onCancel={() => handleClose()}
        footer={false}>
        <div
          style={{
            width: '100%',
          }}>
          <S.Space />
          <BaseForm form={form}>
            <BaseForm.Item
              name="reason"
              rules={[
                {
                  required: true,
                  message: 'Reason is required',
                },
              ]}>
              <TextArea rows={4} placeholder="Reason" onChange={e => setreason(e.target.value)} />
            </BaseForm.Item>
          </BaseForm>

          <div
            style={{
              display: 'flex',
              justifyContent: 'end',
              alignItems: 'center',
              marginBottom: '1rem',
              height: '50px',
            }}>
            <a onClick={() => handleClose()}>Close</a>
            <Button
              size="small"
              htmlType="submit"
              type="default"
              onClick={() => onClickReject(selectedOrderItem, selectedOrder, reason)}
              style={{
                fontSize: '12px',
                marginLeft: '0.7rem',
              }}>
              Save
            </Button>
          </div>
        </div>
      </Modal>
    </ChefDashboardLayout>
  );
};

export default React.memo(ChefDashBoard);
