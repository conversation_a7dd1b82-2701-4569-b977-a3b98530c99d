// src/api/productRegistration.ts
import authInstance from '@app/api/authInstance';
import {LOGIN_SERVICE} from '@app/api/instance';

export interface ProductRegistration {
  name: string;
  email: string;
  title: string;
  address: string;
  cardLogo: string;
  cardImage: string;
  sideBarImage: string;
  titleImage: string;
  type: 'HOTEL' | 'RESTAURANT' | 'OTHER';
  vatNumber: string;
  prefix: string;
  groupId: number;
  webAddress: string;
  websiteLogo: string;
  backgroundImage: string;
  userEmail: string;
  country: string;
  userFirstName: string;
  userLastName: string;
  userContactNumber: string;
  licenseStatus: boolean;
  endDate: string;
  licenseId: number;
  serviceTypes: string[];
  licenseFeatureTypes: string[];
  licenseName: string;
}

export interface ProductRegistrationResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export const ProductRegistration = (registrationPayload: ProductRegistration): Promise<ProductRegistrationResponse> => {
  return authInstance
    .post<ProductRegistrationResponse>(`${LOGIN_SERVICE}api/v1/hotel/internal-product`, registrationPayload)
    .then(({data}) => data);
};
