import houseKeepingInstance from '@app/api/houseKeepingInstance';
import {HOUSE_KEEPING_SERVICE} from '@app/api/instance';

export const getAllTrollies = (hotelId: number): Promise<TrollyResponse> =>
  houseKeepingInstance
    .get<TrollyResponse>(HOUSE_KEEPING_SERVICE + `trolley/all/hotel/${hotelId}`)
    .then(({data}) => data);

export const getAllTrollyContentByUserId = (trolleyId: number): Promise<TrollyResponse> =>
  houseKeepingInstance
    .get<TrollyResponse>(HOUSE_KEEPING_SERVICE + `trolleyContent/all/LaundryItem?userId=&trolleyId=${trolleyId}`)
    .then(({data}) => data);

export const deliveryLaundryItems = (payload: IDeliveryPayload[]): Promise<TrollyResponse> => {
  return houseKeepingInstance
    .post<TrollyResponse>(HOUSE_KEEPING_SERVICE + 'laundry-order', payload)
    .then(({data}) => data);
};

export const getLaundryItemsByStatus = (
  status: string,
  trolleyId: number,
  deliveryDate: string,
  receivedDate: string,
): Promise<TrollyResponse> =>
  houseKeepingInstance
    .get<TrollyResponse>(
      HOUSE_KEEPING_SERVICE +
        `laundry-order/search?laundryOrderStatus=${status}&trolleyId=${trolleyId}&deliveryDate=${deliveryDate}&receivedDate=${receivedDate}`,
    )
    .then(({data}) => data);

export interface TrollyResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}
export interface FilterProps {
  roomNumber: string;
  unitCode: string;
  viewType: string;
  roomType: string;
  phoneExtention: string;
  roomName: string;
}

export interface IDeliveryPayload {
  laundryOrderStatus: string;
  quantity: number;
  deliveryDate: string;
  receivedDate: string;
  trolleyContentId: number;
}
