import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const CreateTax = (payload: CreateTaxProps): Promise<TaxResponse> => {
  return instance.post<TaxResponse>(HOTEL_SERVICE + 'tax', payload).then(({data}) => data);
};

export const UpdateTax = (payload: UpdateTaxProps): Promise<TaxResponse> => {
  return instance.put<TaxResponse>(HOTEL_SERVICE + 'tax', payload).then(({data}) => data);
};

export const getAllTaxes = (): Promise<TaxResponse> =>
  instance.get<TaxResponse>(HOTEL_SERVICE + 'tax').then(({data}) => data);

export const DeleteTax = (id: number): Promise<TaxResponse> =>
  instance.delete<TaxResponse>(HOTEL_SERVICE + `tax/${id}`).then(({data}) => data);

export const UpdateTaxSquence = (payload: TaxSequenceUpdateProps[]): Promise<TaxResponse> => {
  return instance.put<TaxResponse>(HOTEL_SERVICE + 'tax-sequence', payload).then(({data}) => data);
};

export const getAllTaxesByHotelId = (hotelId: number): Promise<TaxResponse> =>
  instance.get<TaxResponse>(HOTEL_SERVICE + `tax/hotel/${hotelId}`).then(({data}) => data);

export interface CreateTaxProps {
  name: string;
  rate: number;
  formula: string;
  sequence: number;
  serviceCharge: boolean;
  element?: string;
  active: boolean;
  hotelId: number;
  socialSecurityContributionLevy: boolean;
  vat: boolean;
}

export interface UpdateTaxProps {
  id: number;
  name: string;
  rate: number;
  formula: string;
  sequence: number;
  serviceCharge: boolean;
  element?: string;
  active: boolean;
  hotelId: number;
  socialSecurityContributionLevy: boolean;
  vat: boolean;
  startDate: string;
}

export interface TaxResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface TaxSequenceUpdateProps {
  id: number;
  sequenceNumber: number;
}
