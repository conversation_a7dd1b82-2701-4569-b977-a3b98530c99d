import authInstance from '@app/api/authInstance';
import {LOGIN_SERVICE} from '@app/api/instance';

// Role

export const getAllRoles = (): Promise<RoleResponse> =>
  authInstance.get<RoleResponse>(LOGIN_SERVICE + 'api/v1/role').then(({data}) => data);

export const CreateRole = (payload: CreateRoleProps): Promise<RoleResponse> => {
  return authInstance.post<RoleResponse>(LOGIN_SERVICE + 'api/v1/role', payload).then(({data}) => data);
};

export const UpdateRole = (payload: UpdateRoleProps): Promise<RoleResponse> => {
  return authInstance.put<RoleResponse>(LOGIN_SERVICE + 'api/v1/role', payload).then(({data}) => data);
};

export const DeleteRole = (id: number): Promise<RoleResponse> =>
  authInstance.delete<RoleResponse>(LOGIN_SERVICE + `api/v1/role/${id}`).then(({data}) => data);

// Group Role
export const getAllGroupRoles = (): Promise<RoleResponse> =>
  authInstance.get<RoleResponse>(LOGIN_SERVICE + 'api/v1/groups-role').then(({data}) => data);

export const CreateGroupRole = (payload: CreateGroupRoleProps): Promise<RoleResponse> => {
  return authInstance.post<RoleResponse>(LOGIN_SERVICE + 'api/v1/groups-role', payload).then(({data}) => data);
};

export const UpdateGroupRole = (payload: UpdateGroupRoleProps): Promise<RoleResponse> => {
  return authInstance.put<RoleResponse>(LOGIN_SERVICE + 'api/v1/groups-role', payload).then(({data}) => data);
};

export const DeleteGroupRole = (id: number): Promise<RoleResponse> =>
  authInstance.delete<RoleResponse>(LOGIN_SERVICE + `api/v1/groups-role/${id}`).then(({data}) => data);

export const getAllRolesByGroupId = (groupsId: number): Promise<RoleResponse> =>
  authInstance.get<RoleResponse>(LOGIN_SERVICE + `api/v1/groups-role/groupsId/${groupsId}`).then(({data}) => data);

// Hotel Role
export const getAllHotelRoles = (): Promise<RoleResponse> =>
  authInstance.get<RoleResponse>(LOGIN_SERVICE + 'api/v1/hotel-role').then(({data}) => data);

export const CreateHotelRole = (payload: CreateHotelRoleProps): Promise<RoleResponse> => {
  return authInstance.post<RoleResponse>(LOGIN_SERVICE + 'api/v1/hotel-role', payload).then(({data}) => data);
};

export const UpdateHotelRole = (payload: UpdateHotelRoleProps): Promise<RoleResponse> => {
  return authInstance.put<RoleResponse>(LOGIN_SERVICE + 'api/v1/hotel-role', payload).then(({data}) => data);
};

export const DeleteHotelRole = (id: number): Promise<RoleResponse> =>
  authInstance.delete<RoleResponse>(LOGIN_SERVICE + `api/v1/hotel-role/${id}`).then(({data}) => data);

export const getAllRolesByHotelId = (hotelId: number): Promise<RoleResponse> =>
  authInstance.get<RoleResponse>(LOGIN_SERVICE + `api/v1/hotel-role/hotelId/${hotelId}`).then(({data}) => data);

// Service Role
export const getAllServiceRoles = (): Promise<RoleResponse> =>
  authInstance.get<RoleResponse>(LOGIN_SERVICE + 'api/v1/service-role').then(({data}) => data);

export const CreateServiceRole = (payload: CreateServiceRoleProps): Promise<RoleResponse> => {
  return authInstance.post<RoleResponse>(LOGIN_SERVICE + 'api/v1/service-role', payload).then(({data}) => data);
};

export const UpdateServiceRole = (payload: UpdateServiceRoleProps): Promise<RoleResponse> => {
  return authInstance.put<RoleResponse>(LOGIN_SERVICE + 'api/v1/service-role', payload).then(({data}) => data);
};

export const DeleteServiceRole = (id: number): Promise<RoleResponse> =>
  authInstance.delete<RoleResponse>(LOGIN_SERVICE + `api/v1/service-role/${id}`).then(({data}) => data);

export const getAllRolesByServicelId = (serviceId: number): Promise<RoleResponse> =>
  authInstance.get<RoleResponse>(LOGIN_SERVICE + `api/v1/service-role/serviceId/${serviceId}`).then(({data}) => data);

export const getAllRolesByServicelIdList = (serviceId: number[]): Promise<RoleResponse> =>
  authInstance
    .get<RoleResponse>(LOGIN_SERVICE + `api/v1/service-role/service-id-list?serviceIdList=${serviceId}`)
    .then(({data}) => data);

export const getAllRolesByServicelIdAndHotelIdList = (
  hotelIds: number[],
  serviceIds: number[],
): Promise<RoleResponse> =>
  authInstance
    .get<RoleResponse>(
      LOGIN_SERVICE +
        `api/v1/service-role/service-id-list-hotel-id-list?hotelIdList=${hotelIds}&serviceIdList=${serviceIds}`,
    )
    .then(({data}) => data);

export const getAllRolesWithoutServiceId = (serviceId: number): Promise<RoleResponse> =>
  authInstance.get<RoleResponse>(LOGIN_SERVICE + `api/v1/role/service/${serviceId}`).then(({data}) => data);

export const getAllRolesWithoutHotelId = (hotelId: number): Promise<RoleResponse> =>
  authInstance.get<RoleResponse>(LOGIN_SERVICE + `api/v1/role/hotel/${hotelId}`).then(({data}) => data);

export const getAllUserPermissionByUserId = (userId: number): Promise<RoleResponse> =>
  authInstance.get<RoleResponse>(LOGIN_SERVICE + `api/v1/users/over-all-permission/${userId}`).then(({data}) => data);

export interface RoleResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface CreateRoleProps {
  name: string;
}

export interface UpdateRoleProps {
  id: number;
  name: string;
}

export interface CreateGroupRoleProps {
  roleId: number;
  groupsId: number;
}

export interface UpdateGroupRoleProps {
  id: number;
  roleId: number;
  groupsId: number;
}

export interface CreateHotelRoleProps {
  roleId: number;
  hotelId: number;
}

export interface UpdateHotelRoleProps {
  id: number;
  roleId: number;
  hotelId: number;
}

export interface CreateServiceRoleProps {
  roleId: number;
  serviceId: number;
}

export interface UpdateServiceRoleProps {
  id: number;
  roleId: number;
  serviceId: number;
}
