import houseKeepingInstance from '@app/api/houseKeepingInstance';
import instance, {HOTEL_SERVICE, HOUSE_KEEPING_SERVICE} from '@app/api/instance';

export const createCheckListActions = (payload: CreateCheckListActionProps): Promise<Response> => {
  return houseKeepingInstance.post<Response>(HOUSE_KEEPING_SERVICE + 'action', payload).then(({data}) => data);
};

export const updateCheckListActions = (payload: CreateCheckListActionProps): Promise<Response> => {
  return houseKeepingInstance.put<Response>(HOUSE_KEEPING_SERVICE + 'action', payload).then(({data}) => data);
};

export const getAllCheckListActions = (hotelId: number): Promise<Response> =>
  houseKeepingInstance.get<Response>(HOUSE_KEEPING_SERVICE + `action`).then(({data}) => data);

export const getAreasByRoomTypesId = (roomId: number): Promise<Response> =>
  instance.get<Response>(HOTEL_SERVICE + `room-type/area/room-type/${roomId}`).then(({data}) => data);

export const multiSearchCheckListsActions = (
  {name, actionType}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<PaginatedResponse> =>
  houseKeepingInstance
    .get<PaginatedResponse>(
      HOUSE_KEEPING_SERVICE +
        `action/search?page=${current}&size=${pageSize}&sortField=id&direction=ASC&name=${
          name ? name : ''
        }&actionType=${actionType ? actionType : ''}`,
    )
    .then(({data}) => data);

export const deleteCheckListAction = (id: number): Promise<Response> =>
  houseKeepingInstance.delete<Response>(HOUSE_KEEPING_SERVICE + `action/${id}`).then(({data}) => data);

export interface CreateCheckListActionProps {
  name: string;
  actionType: string;
  // hotelId: number;
}

export interface UpdateCheckListActionProps {
  id: number;
  name: string;
  actionType: string;
  // hotelId: number;
}

export interface Response {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface PaginatedResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
  pagination: {
    pageNumber: number;
    totalRecords: number;
  };
}

export interface FilterProps {
  name?: string;
  actionType?: string;
}
