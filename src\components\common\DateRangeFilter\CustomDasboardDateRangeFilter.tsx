import React, { useState } from 'react';
import * as S from './DateRangeFilter.style';
import { Button, DatePicker, Radio, RadioChangeEvent, Select } from 'antd';
import moment from 'moment';
import { DatePickerProps, RangePickerProps } from 'antd/lib/date-picker';

const { RangePicker } = DatePicker;

interface IDateRange {
    startDate: string;
    endDate: string;
}
interface IPropsFilter {
    getDateRange: (range: IDateRange) => void;
    getselectDateType: any
    getdateType: any
}

function CustomDasboardDateRangeFilter(props: IPropsFilter) {
    const { getDateRange, getselectDateType, getdateType } = props;
    const [dateType, setDateType] = useState<any>('year');
    const [singleDatePicker, setSingleDatePicker] = useState<moment.Moment | null>(moment());
    const [rangeDatePicker, setRangeDatePicker] = useState<[moment.Moment, moment.Moment] | [null, null]>([null, null]);
    const [selectedDateRange, setSelectedDateRange] = useState<IDateRange>({
        startDate: '',
        endDate: '',
    });
    const [selectDateType, setSelectDateType] = useState<'SINGLE' | 'RANGE' | undefined>('SINGLE');
    const [isAllDate, setIsAllDate] = useState(false);
    const [rangeMessage, setRangeMessage] = useState<string | null>(null);
    const onChange = (e: RadioChangeEvent) => {
        setDateType(e.target.value);
        setSingleDatePicker(null);
        setRangeDatePicker([null, null]);
        setSelectedDateRange({
            startDate: '',
            endDate: '',
        });
        setSelectDateType('SINGLE');
        if (e.target.value === 'all') {
            setIsAllDate(true);
        } else {
            setIsAllDate(false);
        }
    };

    const handleChangeDateSelectType = (value: 'SINGLE' | 'RANGE' | undefined) => {
        setSelectDateType(value);
    };

    const onChangeSingleDate: DatePickerProps['onChange'] = (date, dateString) => {
        setSingleDatePicker(date);
        if (date !== null) {
            if (dateType === 'month') {
                const startDate: any = date?.startOf('month').format('YYYY-MM-DD');
                const endDate: any = date?.endOf('month').format('YYYY-MM-DD');
                setSelectedDateRange({
                    startDate: startDate,
                    endDate: endDate,
                });
            } else if (dateType === 'year') {
                const startDate: any = date?.startOf('year').format('YYYY-MM-DD');
                const endDate: any = date?.endOf('year').format('YYYY-MM-DD');
                setSelectedDateRange({
                    startDate: startDate,
                    endDate: endDate,
                });
            } else if (dateType === 'date') {
                setSelectedDateRange({
                    startDate: dateString,
                    endDate: dateString,
                });
            } else {
            }
        } else {
            //   setShowReport(false);
            setSelectedDateRange({
                startDate: '',
                endDate: '',
            });
        }
    };

    const onChangeRangeDate: RangePickerProps['onChange'] = (date, dateString) => {
        if (date !== null) {
            setRangeDatePicker(date);
            if (date[0]?.isSame(date[1])) {
                setRangeMessage('Same range, change the range');
            } else {
                setRangeMessage(null);
            }
            if (date !== null) {
                setRangeDatePicker(date);
                if (dateType === 'month') {
                    const startMonth = date && date[0];
                    const endMonth = date && date[1];
                    const startDate: any = startMonth?.startOf('month').format('YYYY-MM-DD');
                    const endDate: any = endMonth?.endOf('month').format('YYYY-MM-DD');
                    setSelectedDateRange({
                        startDate: startDate,
                        endDate: endDate,
                    });
                } else if (dateType === 'year') {
                    const startYear = date && date[0];
                    const endYear = date && date[1];
                    const startDate: any = startYear?.startOf('year').format('YYYY-MM-DD');
                    const endDate: any = endYear?.endOf('year').format('YYYY-MM-DD');
                    setSelectedDateRange({
                        startDate: startDate,
                        endDate: endDate,
                    });
                } else if (dateType === 'date') {
                    setSelectedDateRange({
                        startDate: dateString[0],
                        endDate: dateString[1],
                    });
                } else {
                }
            } else {
                setRangeDatePicker([null, null]);
                setSelectedDateRange({
                    startDate: '',
                    endDate: '',
                });
            }
        }
    };
    return (
        <><S.DateSearchWrapper>
            <Radio.Group onChange={onChange} value={dateType} size="small">
                <Radio value={'date'}>Date</Radio>
                <Radio value={'month'}>Month</Radio>
                <Radio value={'year'}>Year</Radio>
            </Radio.Group>
            {dateType !== 'all' && (
                <Select
                    size="small"
                    placeholder="Type"
                    value={selectDateType}
                    style={{ width: 150 }}
                    onChange={handleChangeDateSelectType}
                    options={[
                        {
                            value: 'SINGLE',
                            label: `Single ${dateType}`,
                        },
                        {
                            value: 'RANGE',
                            label: `Range ${dateType}`,
                        },
                    ]} />
            )}
            {selectDateType === 'SINGLE' && dateType !== 'all' ? (
                <DatePicker size="small" value={singleDatePicker} showNow onChange={onChangeSingleDate} picker={dateType} />
            ) : (
                dateType !== 'all' && (
                    <RangePicker size="small" onChange={onChangeRangeDate} picker={dateType} value={rangeDatePicker} />
                )
            )}
            <Button
                size="small"
                type="primary"
                style={{ fontSize: '14px', width: '100px' }}
                onClick={() => {
                    getDateRange(selectedDateRange);
                    getselectDateType(selectDateType);
                    getdateType(dateType);
                }}
                // disabled={(selectDateType === 'SINGLE' && !singleDatePicker) ||
                //     (selectDateType === 'RANGE' && (!rangeDatePicker[0] || !rangeDatePicker[1]))}
                disabled={
                    (selectDateType === 'SINGLE' && !singleDatePicker) ||
                    (selectDateType === 'RANGE' &&
                        (!rangeDatePicker[0] || !rangeDatePicker[1] || rangeDatePicker[0]?.isSame(rangeDatePicker[1], dateType)))
                }
            >
                Apply
            </Button>
            <Button
                size="small"
                type="ghost"
                style={{ fontSize: '14px', width: '100px' }}
                onClick={() => {
                    setSelectDateType('SINGLE');
                    setSingleDatePicker(moment());
                    setDateType('year');
                    const currentYear = moment().year();
                    const resetDateRange = {
                        startDate: moment(`${currentYear}-01-01`).format('YYYY-MM-DD'),
                        endDate: moment(`${currentYear}-12-31`).format('YYYY-MM-DD'),
                    };
                    setSelectedDateRange(resetDateRange);
                    getDateRange(resetDateRange);
                }}
                disabled={
                    (selectDateType === 'SINGLE' && !singleDatePicker) ||
                    (selectDateType === 'RANGE' &&
                        (rangeDatePicker[0]?.isSame(rangeDatePicker[1], dateType)))
                }>
                Reset
            </Button>
        </S.DateSearchWrapper>
            {/* <div style={{ alignContent: 'flex-start' }}> */}
            <div style={{
                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'flex-start',
                width: '100%'
            }}>
                {rangeMessage && (
                    <span style={{
                        color: 'red', fontSize: '12px', textAlign: 'center',
                        fontWeight: 'bold', width: '100%'
                    }}>
                        {rangeMessage}
                    </span>
                )}</div></>
    );
}

export default CustomDasboardDateRangeFilter;
