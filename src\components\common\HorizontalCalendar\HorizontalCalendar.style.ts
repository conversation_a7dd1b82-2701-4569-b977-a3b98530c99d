import {BASE_COLORS, FONT_SIZE} from '@app/styles/themes/constants';
import {Typography} from 'antd';
import styled, {css} from 'styled-components';

interface Props {
  $maxWidth: number;
}

export const CalendarContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 1rem;
`;

export const MonthHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  width: 9vw;
  button {
    cursor: pointer;
    font-size: ${FONT_SIZE.md};
    padding: 0.3rem 0.5rem;
    border: none;
    background: transparent;
  }
`;

export const CalendarWrapper = styled.div<Props>`
  display: flex;
  flex-wrap: nowrap;
  max-width: 88vw;
  overflow-x: scroll;
`;

export const CalendarDay = styled.div<{isToday: boolean; isSelected: boolean; isDisabled: boolean}>`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border: 1px solid ${props => (props.isToday ? BASE_COLORS.primary : BASE_COLORS.white)};
  margin: 4px;
  background-color: ${BASE_COLORS.white};
  cursor: pointer;
  border-radius: 50px;
  text-align: center;
  font-size: 13px;
  font-weight: 600;
  &:hover {
    background-color: #f0f0f0;
  }

  ${props =>
    props.isSelected &&
    `
      background-color: ${BASE_COLORS.primary}; 
      font-weight: bold;
      border-color: ${BASE_COLORS.primary}
      color: ${BASE_COLORS.white}
    `}

  ${({isDisabled}) =>
    isDisabled &&
    css`
      pointer-events: none;
      opacity: 0.6;
    `}
`;

export const DaysContainer = styled.div`
  display: flex;
  flex-wrap: nowrap;
`;

export const DayText = styled(Typography)<{isSelected: boolean; isDisabled: boolean}>`
  font-size: ${FONT_SIZE.xs};
  color: ${props => (props.isSelected ? BASE_COLORS.white : BASE_COLORS.black)};
  ${({isDisabled}) =>
    isDisabled &&
    css`
      color: #ccc;
    `}
`;

export const MonthTitle = styled(Typography)`
  font-size: ${FONT_SIZE.md};
`;
