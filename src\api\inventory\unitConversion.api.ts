import inventoryInstance, {INVENTORY_SERVICE} from '@app/api/inventoryInstance';

export const CreateUnitConversion = (payload: CreateUnitConversionProps): Promise<UnitConversionResponse> => {
  return inventoryInstance
    .post<UnitConversionResponse>(INVENTORY_SERVICE + 'unitConversion', payload)
    .then(({data}) => data);
};

export const UpdateUnitConversion = (payload: UpdateUnitConversionProps): Promise<UnitConversionResponse> => {
  return inventoryInstance
    .put<UnitConversionResponse>(INVENTORY_SERVICE + 'unitConversion', payload)
    .then(({data}) => data);
};

export const getAllUnitConversion = (
  {toUnitSymbol, fromUnitSymbol}: any,
  pageSize: number | undefined,
  current: number,
): Promise<UnitConversionResponse> =>
  inventoryInstance
    .get<UnitConversionResponse>(
      INVENTORY_SERVICE +
        `unitConversion/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&toUnitSymbol=${
          toUnitSymbol ? toUnitSymbol : ''
        }&fromUnitSymbol=${fromUnitSymbol ? fromUnitSymbol : ''}`,
    )
    .then(({data}) => data);

export const DeleteUnitConversion = (id: number): Promise<UnitConversionResponse> =>
  inventoryInstance.delete<UnitConversionResponse>(INVENTORY_SERVICE + `unitConversion/${id}`).then(({data}) => data);

export interface CreateUnitConversionProps {
  value: number;
  fromUnitId: number;
  toUnitId: number;
}

export interface UpdateUnitConversionProps {
  id: number;
  value: number;
  fromUnitId: number;
  toUnitId: number;
}

export interface UnitConversionResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  toUnitSymbol: string;
  fromUnitSymbol: string;
}
