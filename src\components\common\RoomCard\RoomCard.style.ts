import {BASE_COLORS, BORDER_RADIUS, FONT_SIZE, FONT_WEIGHT, media} from '@app/styles/themes/constants';
import styled, {css} from 'styled-components';
import generateCalendar from 'antd/lib/calendar/generateCalendar';
import {AppDate} from '@app/constants/Dates';
import dayjsGenerateConfig from 'rc-picker/lib/generate/dayjs';

const AntCalendar = generateCalendar<AppDate>(dayjsGenerateConfig);

interface Event {
  $isPast: boolean;
}

export const TopLevelCardInfo = styled.div`
  font-size: 0.8rem;
  font-weight: 500;
  color: #fff;
  text-align: center;
  margin-top: 0.5rem;
  bottom: -20px;
  position: relative;
  transition: all 1s ease-in-out;
  transform: translateY(50px);
  opacity: 0;
`;

export const TopLevelCardTitle = styled.div`
  font-weight: 700;
  color: #fff;

  @media only screen and (max-width: 1400px) {
    font-size: 1rem;
  }
`;

type styleProps = {
  bgUrl: string;
};

export const TopLevelCardWrap = styled.div`
  width: 13rem;
  height: 16rem;
  border-radius: 1rem;
  background: #fff;
  padding: 1rem;
  background-size: cover !important;
  background: linear-gradient(0deg, rgb(0 0 0 / 54%), rgb(187 187 187 / 39%)),
    url(${(props: styleProps) => props.bgUrl});

  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-top: 1rem;
  margin-right: 1.6rem;
  margin-bottom: 1rem;
  flex-direction: column;
  transition: all 0.4s ease-in-out;

  &:hover {
    transform: scale(1.05);
    background-size: 180%;
  }

  &:hover ${TopLevelCardInfo} {
    opacity: 1;
    transform: translateY(0px);
  }

  @media only screen and (max-width: 1400px) {
    margin-right: 1.3rem;
  }

  @media only screen and (min-width: 2000px) {
    width: 20.5rem;
    height: 30rem;
    font-size: 1rem;
  }
`;

export const Wrapper = styled.div`
  text-align: center;
  display: flex;
  flex-direction: column;
  margin-top: 2.3rem;
`;

export const ImgWrapper = styled.div`
  width: 6.9375rem;
  margin: 0 auto 1.25rem auto;
  display: flex;
  justify-content: center;
  border-radius: 50%;

  background: #a26658;

  @media only screen and ${media.lg} {
  }
  @media only screen and ${media.xl} {
    width: 7rem;
    margin: 0 auto 1rem auto;
  }
  @media only screen and (min-width: 2400px) {
    width: 9rem;
  }

  & > span {
    margin: 3px;
    width: calc(100% - 6px);
    height: calc(100% - 6px);

    @media only screen and ${media.xl} {
      margin: 3px;
    }
  }
`;

//   background: conic-gradient(
//     from -35.18deg at 50% 50%,
//     #006ccf -154.36deg,
//     #ff5252 24.13deg,
//     #ffb155 118.76deg,
//     #006ccf 205.64deg,
//     #ff5252 384.13deg
//   );

export const Event = styled.div<Event>`
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color:${BASE_COLORS.green}
  font-weight: ${FONT_WEIGHT.bold};
  box-shadow: 0 5px 15px rgba(0, 89, 171, 0.3);
  ${props =>
    props.$isPast
      ? css`
          color: var(--text-light-color);
          background-color: ${BASE_COLORS.green};
        `
      : css`
          color: var(--white);
        `};

  border-radius: ${BORDER_RADIUS};
  background-color: ${BASE_COLORS.green};
`;

export const Calendar = styled(AntCalendar)`
  .ant-picker-calendar-header {
    display: none;
  }

  .ant-picker-panel {
    border-top: none;

    & .ant-picker-body {
      padding: 0;
    }
  }

  .ant-picker-date-panel .ant-picker-content th {
    font-weight: ${FONT_WEIGHT.medium};

    font-size: ${FONT_SIZE.xs};

    color: var(--primary-color);
  }

  .ant-picker-cell {
    &.ant-picker-cell-today {
      .ant-picker-cell-inner {
        &::before {
          border-color: var(--secondary-color);
        }
      }
    }

    &.ant-picker-cell-selected {
      .ant-picker-cell-inner {
        box-shadow: 0 5px 15px rgba(0, 89, 171, 0.3);

        background: var(--primary-color);
        .ant-picker-calendar-date-value,
        ${Event} {
          color: var(--white);
        }
        .ant-picker-calendar-date-value,
        .ant-picker-calendar-date-content > div {
          background: var(--primary-color);
        }
      }
    }

    .ant-picker-cell-inner {
      font-weight: ${FONT_WEIGHT.medium};
      font-size: ${FONT_SIZE.xs};
      height: 1.875rem;
      width: 1.875rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
    }
  }
`;
