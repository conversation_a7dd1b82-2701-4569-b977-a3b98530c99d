// import instance, {RESTAURANT_SERVICE} from '@app/api/instance';

import restInstance, {RESTAURANT_SERVICE} from '@app/api/resturantInstance';

export interface TableCategoryRequest {
  id?: number;
  name: string;
  description?: string;
  hotelServiceId: number;
}

export interface TableCategoryResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export const CreateTableCategory = (tableCategoryPayload: TableCategoryRequest): Promise<TableCategoryResponse> =>
  restInstance
    .post<TableCategoryResponse>(RESTAURANT_SERVICE + 'table-category', {...tableCategoryPayload})
    .then(({data}) => data);

export const getAllTableCategory = (id: number): Promise<TableCategoryResponse> =>
  restInstance.get<TableCategoryResponse>(RESTAURANT_SERVICE + `table-categories/${id}`).then(({data}) => data);

export const UpdateTableCategory = (tableCategoryPayload: TableCategoryRequest): Promise<TableCategoryResponse> =>
  restInstance
    .put<TableCategoryResponse>(RESTAURANT_SERVICE + 'table-category', {...tableCategoryPayload})
    .then(({data}) => data);

export const DeleteTableCategory = (id: number): Promise<TableCategoryResponse> =>
  restInstance.delete<TableCategoryResponse>(RESTAURANT_SERVICE + `table-category/${id}`).then(({data}) => data);
