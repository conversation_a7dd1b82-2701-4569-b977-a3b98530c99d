import React, {useState, useEffect} from 'react';
import {Modal, Table, Button, Space, InputNumber, Select, Tag, Typography, Row, Col} from 'antd';
import {PlusOutlined, DeleteOutlined, EditOutlined} from '@ant-design/icons';
import {ColumnsType} from 'antd/lib/table';
import {IFoodIngredient} from './interface';
import {notificationController} from '@app/controllers/notificationController';
import {BASE_COLORS} from '@app/styles/themes/constants';

const {Option} = Select;
const {Text} = Typography;

interface FoodIngredientsModalProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (ingredients: IFoodIngredient[]) => void;
  foodName: string;
  initialIngredients?: IFoodIngredient[];
}

// Mock ingredients data - replace with actual API call
const availableIngredients = [
  {id: 1, name: 'Tomatoes', unit: 'kg', costPerUnit: 2.50},
  {id: 2, name: 'Cheese', unit: 'kg', costPerUnit: 8.00},
  {id: 3, name: 'Flour', unit: 'kg', costPerUnit: 1.20},
  {id: 4, name: 'Onions', unit: 'kg', costPerUnit: 1.80},
  {id: 5, name: 'Bell Peppers', unit: 'kg', costPerUnit: 3.50},
  {id: 6, name: 'Mushrooms', unit: 'kg', costPerUnit: 4.20},
  {id: 7, name: 'Olive Oil', unit: 'L', costPerUnit: 12.00},
  {id: 8, name: 'Salt', unit: 'kg', costPerUnit: 0.80},
  {id: 9, name: 'Black Pepper', unit: 'kg', costPerUnit: 15.00},
  {id: 10, name: 'Garlic', unit: 'kg', costPerUnit: 6.00},
];

export const FoodIngredientsModal: React.FC<FoodIngredientsModalProps> = ({
  visible,
  onCancel,
  onSave,
  foodName,
  initialIngredients = [],
}) => {
  const [ingredients, setIngredients] = useState<IFoodIngredient[]>(initialIngredients);
  const [selectedIngredientId, setSelectedIngredientId] = useState<number | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  useEffect(() => {
    setIngredients(initialIngredients);
  }, [initialIngredients, visible]);

  const addIngredient = () => {
    if (!selectedIngredientId || quantity <= 0) {
      notificationController.error({message: 'Please select an ingredient and enter a valid quantity'});
      return;
    }

    const selectedIngredient = availableIngredients.find(ing => ing.id === selectedIngredientId);
    if (!selectedIngredient) return;

    // Check if ingredient already exists
    const existingIndex = ingredients.findIndex(ing => ing.id === selectedIngredientId);
    if (existingIndex !== -1) {
      notificationController.error({message: 'This ingredient is already added'});
      return;
    }

    const newIngredient: IFoodIngredient = {
      id: selectedIngredient.id,
      name: selectedIngredient.name,
      quantity: quantity,
      unit: selectedIngredient.unit,
      costPerUnit: selectedIngredient.costPerUnit,
      totalCost: quantity * selectedIngredient.costPerUnit,
    };

    setIngredients([...ingredients, newIngredient]);
    setSelectedIngredientId(null);
    setQuantity(1);
  };

  const updateIngredient = (index: number, newQuantity: number) => {
    if (newQuantity <= 0) return;

    const updatedIngredients = [...ingredients];
    updatedIngredients[index] = {
      ...updatedIngredients[index],
      quantity: newQuantity,
      totalCost: newQuantity * updatedIngredients[index].costPerUnit,
    };
    setIngredients(updatedIngredients);
    setEditingIndex(null);
  };

  const removeIngredient = (index: number) => {
    const updatedIngredients = ingredients.filter((_, i) => i !== index);
    setIngredients(updatedIngredients);
  };

  const getTotalCost = () => {
    return ingredients.reduce((total, ingredient) => total + ingredient.totalCost, 0);
  };

  const columns: ColumnsType<IFoodIngredient> = [
    {
      title: 'Ingredient',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number, record: IFoodIngredient, index: number) => {
        if (editingIndex === index) {
          return (
            <InputNumber
              min={0.1}
              step={0.1}
              defaultValue={quantity}
              onPressEnter={(e) => {
                const value = parseFloat((e.target as HTMLInputElement).value);
                updateIngredient(index, value);
              }}
              onBlur={(e) => {
                const value = parseFloat(e.target.value);
                updateIngredient(index, value);
              }}
              autoFocus
            />
          );
        }
        return (
          <span onClick={() => setEditingIndex(index)} style={{cursor: 'pointer'}}>
            {quantity} {record.unit}
          </span>
        );
      },
    },
    {
      title: 'Cost per Unit',
      dataIndex: 'costPerUnit',
      key: 'costPerUnit',
      render: (cost: number) => <span>${cost.toFixed(2)}</span>,
    },
    {
      title: 'Total Cost',
      dataIndex: 'totalCost',
      key: 'totalCost',
      render: (cost: number) => <Tag color="green">${cost.toFixed(2)}</Tag>,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: IFoodIngredient, index: number) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => setEditingIndex(index)}
            style={{color: BASE_COLORS.primary}}
          />
          <Button
            type="text"
            icon={<DeleteOutlined />}
            onClick={() => removeIngredient(index)}
            style={{color: BASE_COLORS.error}}
          />
        </Space>
      ),
    },
  ];

  const handleSave = () => {
    onSave(ingredients);
    onCancel();
  };

  const availableOptions = availableIngredients.filter(
    ing => !ingredients.some(selected => selected.id === ing.id)
  );

  return (
    <Modal
      title={`Manage Ingredients for ${foodName}`}
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          Save Ingredients
        </Button>,
      ]}>
      
      {/* Add Ingredient Section */}
      <div style={{marginBottom: 16, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 8}}>
        <Text strong style={{marginBottom: 8, display: 'block'}}>Add New Ingredient</Text>
        <Row gutter={16} align="middle">
          <Col span={10}>
            <Select
              placeholder="Select ingredient"
              value={selectedIngredientId}
              onChange={setSelectedIngredientId}
              style={{width: '100%'}}>
              {availableOptions.map(ingredient => (
                <Option key={ingredient.id} value={ingredient.id}>
                  {ingredient.name} (${ingredient.costPerUnit}/{ingredient.unit})
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <InputNumber
              placeholder="Quantity"
              min={0.1}
              step={0.1}
              value={quantity}
              onChange={(value) => setQuantity(value || 1)}
              style={{width: '100%'}}
            />
          </Col>
          <Col span={8}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={addIngredient}
              disabled={!selectedIngredientId || quantity <= 0}>
              Add Ingredient
            </Button>
          </Col>
        </Row>
      </div>

      {/* Ingredients Table */}
      <Table
        columns={columns}
        dataSource={ingredients}
        rowKey="id"
        pagination={false}
        size="small"
        locale={{emptyText: 'No ingredients added yet'}}
      />

      {/* Total Cost Summary */}
      {ingredients.length > 0 && (
        <div style={{marginTop: 16, textAlign: 'right'}}>
          <Text strong style={{fontSize: 16}}>
            Total Ingredient Cost: <Tag color="blue" style={{fontSize: 14}}>${getTotalCost().toFixed(2)}</Tag>
          </Text>
        </div>
      )}
    </Modal>
  );
};
