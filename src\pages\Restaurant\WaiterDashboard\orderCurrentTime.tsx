import React, {useEffect, useState} from 'react';

const OrderClock = () => {
  const [orderCurrentTime, setOrderCurrentTime] = useState(getFormattedTime());

  useEffect(() => {
    const interval = setInterval(() => {
      setOrderCurrentTime(getFormattedTime());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return <span>{orderCurrentTime}</span>;
};

const getFormattedTime = () => {
  return new Date().toLocaleString('en-GB', {
    weekday: 'long',
    day: '2-digit',
    month: 'long',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  });
};

export default OrderClock;
