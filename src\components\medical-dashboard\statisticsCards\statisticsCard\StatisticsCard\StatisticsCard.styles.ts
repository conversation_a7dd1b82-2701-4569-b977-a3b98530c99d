import styled from 'styled-components';
import {default as AntIcon} from '@ant-design/icons';
import {DashboardCard} from '@app/components/medical-dashboard/DashboardCard/DashboardCard';
import {StatisticColor} from '@app/constants/config/statistics';
import {Text} from '../StatisticsInfo/StatisticsInfo.styles';

interface StatisticsProps {
  $color?: StatisticColor;
}

export const IconWrapper = styled.div`
  margin-top: 0.25rem;
`;

export const Icon = styled(AntIcon)`
  font-size: 1.5rem;
`;

export const StatisticCard = styled(DashboardCard)<StatisticsProps>`
  line-height: 1;
  overflow: hidden;
  justify-content: space-between;
  align-items: center;
  display: flex;
  flex-direction: row;
  border-radius: 15px;
  height: 7rem;
  padding: 1rem;
`;
export const VerticalLine = styled.div`
  width: 5px;
  height: 4rem;
  background-color: green;
  border-radius: 10px;
`;
