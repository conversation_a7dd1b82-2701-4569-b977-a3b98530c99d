import React from 'react';
import {useNavigate} from 'react-router-dom';
import {FadeIn} from '../FoodCatagoryFoodLevel';
import TopLevelCard from '@app/components/cards/TopLevelCard';
import dinein from '../../../assets/restaurant/dine-in.jpg';
import takeaway from '../../../assets/restaurant/take-away.jpg';

const RestaurantHome = () => {
  const navigate = useNavigate();
  return (
    <div style={{display: 'flex', gap: '20px', alignItems: 'center', justifyContent: 'center'}}>
      <FadeIn onClick={() => navigate('/toplevel/reservation')}>
        <TopLevelCard key={1} bgUrl={dinein} logo={dinein} name={'Dine-in'} info={''} />
      </FadeIn>

      <FadeIn onClick={() => navigate('/category/main')}>
        <TopLevelCard key={2} bgUrl={takeaway} logo={takeaway} name={'Take-away'} info={''} />
      </FadeIn>
    </div>
  );
};

export default RestaurantHome;
