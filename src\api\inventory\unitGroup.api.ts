import inventoryInstance, {INVENTORY_SERVICE} from '@app/api/inventoryInstance';

export const CreateUnitGroup = (payload: CreateUnitGroupProps): Promise<UnitGroupResponse> => {
  return inventoryInstance.post<UnitGroupResponse>(INVENTORY_SERVICE + 'unit-group', payload).then(({data}) => data);
};

export const UpdateUnitGroup = (payload: UpdateUnitGroupProps): Promise<UnitGroupResponse> => {
  return inventoryInstance.put<UnitGroupResponse>(INVENTORY_SERVICE + 'unit-group', payload).then(({data}) => data);
};

export const getAllUnitGroups = (): Promise<UnitGroupResponse> =>
  inventoryInstance.get<UnitGroupResponse>(INVENTORY_SERVICE + 'unit-group').then(({data}) => data);

export const DeleteUnitGroup = (id: number): Promise<UnitGroupResponse> =>
  inventoryInstance.delete<UnitGroupResponse>(INVENTORY_SERVICE + `unit-group/${id}`).then(({data}) => data);

export interface CreateUnitGroupProps {
  name: string;
}

export interface UpdateUnitGroupProps {
  id: number;
  name: string;
}

export interface UnitGroupResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}
