import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const CreateSeason = (payload: CreateSeasonProps): Promise<SeasonResponse> => {
  return instance.post<SeasonResponse>(HOTEL_SERVICE + 'season', payload).then(({data}) => data);
};

export const UpdateSeason = (payload: UpdateSeasonProps): Promise<SeasonResponse> => {
  return instance.put<SeasonResponse>(HOTEL_SERVICE + 'season', payload).then(({data}) => data);
};

export const getAllSeasons = (hotelId: number): Promise<SeasonResponse> =>
  instance.get<SeasonResponse>(HOTEL_SERVICE + `season/${hotelId}`).then(({data}) => data);

export const getAllCreatedSeasons = (hotelId: number, channelId: number): Promise<SeasonResponse> =>
  instance.get<SeasonResponse>(HOTEL_SERVICE + `season/${hotelId}/channel/${channelId}`).then(({data}) => data);

export const multiSearchSeasons = (
  hotelId: number,
  {description}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<SeasonPaginatedResponse> =>
  instance
    .get<SeasonPaginatedResponse>(
      HOTEL_SERVICE +
        `season/search?page=${current}&size=${pageSize}&sortField=fromDate&direction=ASC&hotelId=${hotelId}&description=${
          description ? description : ''
        }`,
    )
    .then(({data}) => data);

export const DeleteSeason = (id: number): Promise<SeasonResponse> =>
  instance.delete<SeasonResponse>(HOTEL_SERVICE + `season/${id}`).then(({data}) => data);

export interface CreateSeasonProps {
  description: string;
  fromDate: string;
  toDate: string;
  hotelId: number;
  seasonYear: string;
}

export interface UpdateSeasonProps {
  id: number;
  description: string;
  fromDate: string;
  toDate: string;
  hotelId: number;
  seasonYear: string;
}

export interface SeasonResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface SeasonPaginatedResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
  pagination: {
    pageNumber: number;
    totalRecords: number;
  };
}

export interface FilterProps {
  description?: string;
}
