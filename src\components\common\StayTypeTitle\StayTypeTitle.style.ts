import {BASE_COLORS, FONT_WEIGHT} from '@app/styles/themes/constants';
import styled from 'styled-components';

interface Props {
  $fontSize: string;
  $isBold: boolean;
}

export const StayTitle = styled.div<Props>`
  display: flex;
  font-size: ${props => props.$fontSize};
  color: ${BASE_COLORS.black};
  font-weight: ${props => (props.$isBold ? FONT_WEIGHT.bold : '')};
  gap: 0.3rem;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
`;
export const TitleIconWrapper = styled.div`
  display: flex;
  gap: 1px;
  align-items: center;
  justify-content: center;
`;
