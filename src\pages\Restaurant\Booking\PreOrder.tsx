import React, {useState, useEffect} from 'react';
import {Row, Col, Card, Button, Typography, Space, Tag, Divider} from 'antd';
import {ShoppingCartOutlined, ArrowLeftOutlined, CheckOutlined} from '@ant-design/icons';
import {useNavigate, useLocation} from 'react-router-dom';
import {PageTitle} from '@app/components/common/PageTitle/PageTitle';
import {notificationController} from '@app/controllers/notificationController';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';

import {addFoodItems} from '../slices/waiterDasboardSlice';
import TopLevelFilter from '../WaiterDashboard/TopLevelFilter';
import {BASE_COLORS} from '@app/styles/themes/constants';
import {getCategoryStuctureByID} from '@app/api/resturant/tablecategory/categoryStucture.api';

const {Title, Text} = Typography;

interface IOrderItem {
  id: number;
  category: string;
  currency: string;
  itemImage: string;
  item: string;
  quantity: number;
  commentQuantity?: number;
  price: number;
  orderedItemStatus: string;
  totalPrice: number;
  active: boolean;
}

interface PreOrderItem extends IOrderItem {
  selectedQuantity: number;
}

const PreOrder: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();

  const reservationData = location.state?.reservationData;
  const foodItems = useAppSelector(state => state.waiterDasbordSlice.foodItems);
  const hotelServiceConfig = useAppSelector(state => state.hotelSlice.hotelServiceConfig);

  const [selectedItems, setSelectedItems] = useState<PreOrderItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);

  // Handle screen resize for responsive behavior
  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Determine if we're on a tablet (9 inch) or larger screen
  const isTablet = screenWidth >= 768 && screenWidth < 1024; // 9 inch tablets
  const isLargeScreen = screenWidth >= 1024; // Over 9 inch screens
  const isMobile = screenWidth < 768;

  // Get food items by category (similar to WaiterDashboard)
  const getAllFoodItemsByCategory = async (id: any, name?: string) => {
    try {
      const results = await getCategoryStuctureByID(id, hotelServiceConfig.serviceId, name);
      if (results?.statusCode === '20000') {
        let data: any[] = [];

        results.result.category &&
          results.result.category.map((post: any) => {
            data.push({
              id: post.id,
              category: '',
              currency: 'LKR',
              itemImage: post.image,
              item: post.name,
              quantity: 1,
              commentQuantity: 0,
              price: post.price,
              orderedItemStatus: 'NEW',
              totalPrice: post.price,
              active: post.active,
            });
          });

        dispatch(addFoodItems({foodItems: data}));
      } else {
        notificationController.error({message: results?.message});
      }
    } catch (error) {
      notificationController.error({message: 'Failed to load food items'});
    }
  };

  const addToPreOrder = (item: IOrderItem) => {
    const existingItem = selectedItems.find(selected => selected.id === item.id);

    if (existingItem) {
      // Increase quantity
      setSelectedItems(prev =>
        prev.map(selected =>
          selected.id === item.id
            ? {
                ...selected,
                selectedQuantity: selected.selectedQuantity + 1,
                totalPrice: (selected.selectedQuantity + 1) * selected.price,
              }
            : selected,
        ),
      );
    } else {
      // Add new item
      setSelectedItems(prev => [
        ...prev,
        {
          ...item,
          selectedQuantity: 1,
          totalPrice: item.price,
        },
      ]);
    }

    notificationController.success({message: `${item.item} added to pre-order`});
  };

  const removeFromPreOrder = (itemId: number) => {
    const existingItem = selectedItems.find(selected => selected.id === itemId);

    if (existingItem && existingItem.selectedQuantity > 1) {
      // Decrease quantity
      setSelectedItems(prev =>
        prev.map(selected =>
          selected.id === itemId
            ? {
                ...selected,
                selectedQuantity: selected.selectedQuantity - 1,
                totalPrice: (selected.selectedQuantity - 1) * selected.price,
              }
            : selected,
        ),
      );
    } else {
      // Remove item completely
      setSelectedItems(prev => prev.filter(selected => selected.id !== itemId));
    }
  };

  const getTotalAmount = () => {
    return selectedItems.reduce((total, item) => total + item.totalPrice, 0);
  };

  const handleCompletePreOrder = async () => {
    try {
      setLoading(true);

      const completeReservationData = {
        ...reservationData,
        preOrderItems: selectedItems,
        totalAmount: getTotalAmount(),
        completedAt: new Date().toISOString(),
      };

      // Store complete reservation data (replace with API call)
      localStorage.setItem('completedReservation', JSON.stringify(completeReservationData));

      notificationController.success({
        message: 'Reservation and Pre-order Completed!',
        description: `Total amount: $${getTotalAmount().toFixed(2)}`,
      });

      // Navigate to confirmation or home
      navigate('/toplevel/home');
    } catch (error) {
      notificationController.error({message: 'Failed to complete pre-order'});
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/booking/customer-info', {
      state: {bookingDetails: reservationData?.bookingDetails},
    });
  };

  if (!reservationData) {
    navigate('/toplevel/reservation');
    return null;
  }

  return (
    <div style={{padding: '20px', width: '100%', maxWidth: '100vw', overflowX: 'hidden'}}>
      <PageTitle>Pre-Order Your Meal</PageTitle>

      {/* Customer & Booking Info Header */}
      <Card style={{marginBottom: '20px'}}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space direction="vertical" size="small">
              <Text strong style={{fontSize: '16px'}}>
                {reservationData.customerInfo.name}
              </Text>
              <Text type="secondary">
                {reservationData.bookingDetails.guests} • {reservationData.bookingDetails.date} •{' '}
                {reservationData.bookingDetails.time}
              </Text>
            </Space>
          </Col>
          <Col>
            <Space>
              <Tag color="blue">Pre-Order</Tag>
              <Text strong style={{fontSize: '16px'}}>
                Total: ${getTotalAmount().toFixed(2)}
              </Text>
            </Space>
          </Col>
        </Row>
      </Card>

      <Row gutter={16}>
        {/* Left Side - Food Categories and Items */}
        <Col
          xs={24} // Full width on mobile
          sm={24} // Full width on small tablets
          md={16} // 16/24 on 9 inch tablets
          lg={18} // 18/24 on large screens (over 9 inch)
          xl={18} // 18/24 on extra large screens
          xxl={18} // 18/24 on extra extra large screens
        >
          <Card title="Select Your Food" style={{minHeight: '70vh', marginBottom: '16px'}}>
            {/* Category Filter */}
            <div style={{marginBottom: '16px'}}>
              <TopLevelFilter />
            </div>

            {/* Food Items Grid - 4 cards per row, no horizontal scroll */}
            <div style={{width: '100%'}}>
              <Row gutter={[12, 16]}>
                {foodItems.length === 0 ? (
                  <Col span={24}>
                    <div style={{textAlign: 'center', padding: '40px', color: '#888'}}>
                      Select a food category to view items
                    </div>
                  </Col>
                ) : (
                  foodItems.map((item: IOrderItem, index: number) => (
                    <Col
                      xs={12} // 2 cards per row on mobile
                      sm={12} // 2 cards per row on small tablets
                      md={8} // 3 cards per row on 9 inch tablets
                      lg={6} // 4 cards per row on large screens (over 9 inch)
                      xl={6} // 4 cards per row on extra large screens
                      xxl={6} // 4 cards per row on extra extra large screens
                      key={index}>
                      <Card
                        hoverable
                        style={{
                          backgroundColor: item.active ? 'white' : '#f5f5f5',
                          cursor: item.active ? 'pointer' : 'default',
                          height: '250px',
                        }}
                        cover={
                          <img alt={item.item} src={item.itemImage} style={{height: '120px', objectFit: 'cover'}} />
                        }
                        onClick={() => item.active && addToPreOrder(item)}>
                        <Card.Meta
                          title={
                            <Text ellipsis style={{fontSize: '12px', fontWeight: '600'}}>
                              {item.item}
                            </Text>
                          }
                          description={
                            <Space direction="vertical" size="small" style={{width: '100%'}}>
                              <Text strong style={{color: BASE_COLORS.primary, fontSize: '13px'}}>
                                ${item.price.toFixed(2)}
                              </Text>
                              {item.active && (
                                <Button
                                  type="primary"
                                  size="small"
                                  icon={<ShoppingCartOutlined />}
                                  style={{
                                    width: '100%',
                                    height: '24px',
                                    fontSize: '10px',
                                    padding: '0 6px',
                                  }}>
                                  Add
                                </Button>
                              )}
                            </Space>
                          }
                        />
                      </Card>
                    </Col>
                  ))
                )}
              </Row>
            </div>
          </Card>
        </Col>

        {/* Right Side - Selected Items */}
        <Col
          xs={24} // Full width on mobile (below food items)
          sm={24} // Full width on small tablets (below food items)
          md={8} // 8/24 on 9 inch tablets (side by side)
          lg={6} // 6/24 on large screens (over 9 inch)
          xl={6} // 6/24 on extra large screens
          xxl={6} // 6/24 on extra extra large screens
        >
          <Card
            title={
              <Space>
                <ShoppingCartOutlined />
                <span style={{fontSize: '14px'}}>Pre-Order ({selectedItems.length})</span>
              </Space>
            }
            style={{height: '70vh'}}>
            <div style={{height: 'calc(100% - 120px)', overflowY: 'auto'}}>
              {selectedItems.length === 0 ? (
                <div style={{textAlign: 'center', padding: '30px', color: '#888', fontSize: '12px'}}>
                  No items selected yet
                </div>
              ) : (
                selectedItems.map((item, index) => (
                  <div
                    key={index}
                    style={{marginBottom: '12px', padding: '8px', border: '1px solid #f0f0f0', borderRadius: '6px'}}>
                    <Row justify="space-between" align="middle">
                      <Col span={16}>
                        <Text strong style={{fontSize: '12px'}}>
                          {item.item}
                        </Text>
                        <br />
                        <Text type="secondary" style={{fontSize: '10px'}}>
                          ${item.price.toFixed(2)} each
                        </Text>
                      </Col>
                      <Col span={8} style={{textAlign: 'right'}}>
                        <Space direction="vertical" size="small" style={{width: '100%'}}>
                          <Space size="small">
                            <Button
                              size="small"
                              onClick={() => removeFromPreOrder(item.id)}
                              style={{
                                width: '20px',
                                height: '20px',
                                fontSize: '10px',
                                padding: 0,
                                minWidth: '20px',
                              }}>
                              -
                            </Button>
                            <Text style={{fontSize: '11px', minWidth: '15px', textAlign: 'center'}}>
                              {item.selectedQuantity}
                            </Text>
                            <Button
                              size="small"
                              onClick={() => addToPreOrder(item)}
                              style={{
                                width: '20px',
                                height: '20px',
                                fontSize: '10px',
                                padding: 0,
                                minWidth: '20px',
                              }}>
                              +
                            </Button>
                          </Space>
                          <Text strong style={{fontSize: '11px'}}>
                            ${item.totalPrice.toFixed(2)}
                          </Text>
                        </Space>
                      </Col>
                    </Row>
                  </div>
                ))
              )}
            </div>

            {/* Total and Actions */}
            <div style={{position: 'absolute', bottom: '16px', left: '16px', right: '16px'}}>
              <Divider />
              <Row justify="space-between" align="middle" style={{marginBottom: '12px'}}>
                <Col>
                  <Text strong style={{fontSize: '16px'}}>
                    Total: ${getTotalAmount().toFixed(2)}
                  </Text>
                </Col>
              </Row>

              <Space style={{width: '100%'}} direction="vertical" size="small">
                <Button
                  type="primary"
                  icon={<CheckOutlined />}
                  onClick={handleCompletePreOrder}
                  loading={loading}
                  disabled={selectedItems.length === 0}
                  style={{width: '100%', height: '32px', fontSize: '12px'}}
                  size="small">
                  Complete Pre-Order
                </Button>
                <Button
                  icon={<ArrowLeftOutlined />}
                  onClick={handleBack}
                  style={{width: '100%', height: '28px', fontSize: '11px'}}
                  size="small">
                  Back
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default PreOrder;
