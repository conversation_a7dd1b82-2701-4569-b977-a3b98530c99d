import React, {useEffect} from 'react';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {notificationController} from '@app/controllers/notificationController';
import {Col, FormInstance, Row, Select, Switch, InputNumber} from 'antd';
import {Input, TextArea} from '@app/components/common/inputs/Input/Input';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {setIsTouchAction, setLoading, setModalVisible} from '@app/store/slices/commonSlice';
import {IFoodIngredientData} from './ManageFoodIngredients';

interface Props {
  form: FormInstance;
  reloadData: () => void;
  rowData?: IFoodIngredientData;
}

const {Option} = Select;

const FoodIngredientForm: React.FC<Props> = ({form, reloadData, rowData}) => {
  const dispatch = useAppDispatch();
  const isEditAction = useAppSelector(state => state.commonSlice.isEditAction);
  const hotelConfig = useAppSelector(state => state.hotelSlice.hotelConfig);
  const hotelServiceConfig = useAppSelector(state => state.hotelSlice.hotelServiceConfig);

  // Common allergens list
  const commonAllergens = [
    'Dairy',
    'Eggs',
    'Fish',
    'Shellfish',
    'Tree Nuts',
    'Peanuts',
    'Wheat',
    'Gluten',
    'Soy',
    'Sesame',
  ];

  // Common ingredient categories
  const ingredientCategories = [
    'Vegetables',
    'Fruits',
    'Meat',
    'Poultry',
    'Seafood',
    'Dairy',
    'Grains',
    'Spices',
    'Herbs',
    'Oils',
    'Condiments',
    'Beverages',
    'Other',
  ];

  // Common units
  const units = ['kg', 'g', 'lb', 'oz', 'L', 'ml', 'cups', 'tbsp', 'tsp', 'pieces', 'dozen'];

  useEffect(() => {
    if (isEditAction && rowData) {
      form.setFieldsValue({
        name: rowData.name,
        description: rowData.description,
        unit: rowData.unit,
        costPerUnit: rowData.costPerUnit,
        category: rowData.category,
        allergens: rowData.allergens,
        active: rowData.active,
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        active: true,
      });
    }
  }, [isEditAction, rowData, form]);

  const onFinish = async (values: any) => {
    try {
      dispatch(setLoading(true));

      const ingredientData: IFoodIngredientData = {
        id: isEditAction ? rowData?.id || 0 : 0,
        name: values.name,
        description: values.description || '',
        unit: values.unit,
        costPerUnit: values.costPerUnit,
        category: values.category,
        allergens: values.allergens || [],
        active: values.active !== undefined ? values.active : true,
      };

      if (isEditAction) {
        // TODO: Implement update API call
        // await UpdateFoodIngredient(ingredientData);
        notificationController.success({message: 'Ingredient updated successfully'});
      } else {
        // TODO: Implement create API call
        // await CreateFoodIngredient(ingredientData);
        notificationController.success({message: 'Ingredient created successfully'});
      }

      form.resetFields();
      dispatch(setModalVisible(false));
      reloadData();
    } catch (error: any) {
      notificationController.error({
        message: error.message || `Failed to ${isEditAction ? 'update' : 'create'} ingredient`,
      });
    } finally {
      dispatch(setLoading(false));
    }
  };

  return (
    <BaseForm
      form={form}
      layout="vertical"
      onFinish={onFinish}
      onFieldsChange={() => {
        dispatch(setIsTouchAction(true));
      }}>
      <Row gutter={{xs: 10, md: 15, xl: 30}}>
        <Col xs={24} md={12}>
          <BaseForm.Item
            name="name"
            label="Ingredient Name"
            rules={[{required: true, message: 'Ingredient name is required'}]}>
            <Input placeholder="Enter ingredient name" />
          </BaseForm.Item>
        </Col>
        <Col xs={24} md={12}>
          <BaseForm.Item name="category" label="Category" rules={[{required: true, message: 'Category is required'}]}>
            <Select placeholder="Select category">
              {ingredientCategories.map(category => (
                <Option key={category} value={category}>
                  {category}
                </Option>
              ))}
            </Select>
          </BaseForm.Item>
        </Col>
      </Row>

      <Row gutter={{xs: 10, md: 15, xl: 30}}>
        <Col xs={24} md={12}>
          <BaseForm.Item
            name="unit"
            label="Unit of Measurement"
            rules={[{required: true, message: 'Unit is required'}]}>
            <Select placeholder="Select unit">
              {units.map(unit => (
                <Option key={unit} value={unit}>
                  {unit}
                </Option>
              ))}
            </Select>
          </BaseForm.Item>
        </Col>
        <Col xs={24} md={12}>
          <BaseForm.Item
            name="costPerUnit"
            label="Cost per Unit"
            rules={[{required: true, message: 'Cost per unit is required'}]}>
            <InputNumber style={{width: '100%'}} placeholder="Enter cost per unit" min={0} step={0.01} precision={2} />
          </BaseForm.Item>
        </Col>
      </Row>

      <Row gutter={{xs: 10, md: 15, xl: 30}}>
        <Col xs={24}>
          <BaseForm.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Enter ingredient description" />
          </BaseForm.Item>
        </Col>
      </Row>

      <Row gutter={{xs: 10, md: 15, xl: 30}}>
        <Col xs={24} md={12}>
          <BaseForm.Item name="allergens" label="Allergens">
            <Select mode="multiple" placeholder="Select allergens (if any)" allowClear>
              {commonAllergens.map(allergen => (
                <Option key={allergen} value={allergen}>
                  {allergen}
                </Option>
              ))}
            </Select>
          </BaseForm.Item>
        </Col>
        <Col xs={24} md={12}>
          <BaseForm.Item name="active" label="Active" valuePropName="checked">
            <Switch />
          </BaseForm.Item>
        </Col>
      </Row>
    </BaseForm>
  );
};

export default FoodIngredientForm;
