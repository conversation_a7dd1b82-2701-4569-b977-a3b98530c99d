import instance, {HOTEL_SERVICE} from '@app/api/instance';
import {RoomResponse} from '../room.api';
import {LeadType} from '@app/pages/MasterPages/hotel/leadType/LeadTypeContent';

export const getAllLeadTypes = (hotelId: number): Promise<RoomResponse> =>
  instance.get<RoomResponse>(HOTEL_SERVICE + `lead-type/hotel/${hotelId}`).then(({data}) => data);

export const createLeadType = (payload: LeadType): Promise<RoomResponse> =>
  instance.post<RoomResponse>(HOTEL_SERVICE + `lead-type`, payload).then(({data}) => data);

export const updateLeadType = (payload: LeadType): Promise<RoomResponse> =>
  instance.put<RoomResponse>(HOTEL_SERVICE + `lead-type`, payload).then(({data}) => data);

export const deleteLeadType = (id: number): Promise<RoomResponse> =>
  instance.delete<RoomResponse>(HOTEL_SERVICE + `lead-type/${id}`).then(({data}) => data);
