import React, {useEffect, useState} from 'react';
import {Input, InputProps} from 'antd';

const validateNumber = (number: string) => {
  const pattern = /^\d{1,3}(,\d{3})*(\.\d+)?$/;
  return pattern.test(number);
};

// Utility to format value with commas and allow decimals
const formatDisplayValue = (value: string): string => {
  const toStringValue = value.toString();

  // Remove all invalid characters except digits and a single dot
  const cleanedValue = toStringValue?.replace(/[^\d.]/g, '');

  // Split into integer and decimal parts
  const [integerPart, decimalPart] = cleanedValue?.split('.');

  // Format the integer part with commas
  const formattedInteger = integerPart ? parseInt(integerPart, 10).toLocaleString('en-US') : '';

  // Combine integer and decimal parts if available
  return decimalPart !== undefined ? `${formattedInteger}.${decimalPart}` : formattedInteger;
};

// Utility to retain raw numeric value (allows only valid decimals)
const getRawValue = (value: string): string => {
  // Remove invalid characters and restrict to one dot
  const cleanedValue = value?.replace(/[^0-9.]/g, '');
  const parts = cleanedValue?.split('.');
  if (parts.length > 2) {
    // If more than one dot, ignore extra dots
    return `${parts[0]}.${parts.slice(1).join('')}`;
  }
  return cleanedValue;
};

// Custom Currency Input Component
// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface CurrencyInputProps extends InputProps {}

const CurrencyInput: React.FC<CurrencyInputProps> = ({value, onChange, ...restProps}) => {
  const [displayValue, setDisplayValue] = useState<string>(formatDisplayValue((value as string) || ''));

  useEffect(() => {
    setDisplayValue(formatDisplayValue((value as string) || ''));
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;

    // Get raw numeric value
    const rawValue = getRawValue(input);

    // Format for display
    const formattedValue = formatDisplayValue(rawValue);
    setDisplayValue(formattedValue);

    // Trigger parent's onChange with raw value
    if (onChange) {
      const simulatedEvent = {
        ...e,
        target: {...e.target, value: rawValue},
      };
      onChange(simulatedEvent as React.ChangeEvent<HTMLInputElement>);
    }
  };

  return (
    <Input
      {...restProps}
      value={displayValue} // Display formatted value
      onChange={handleInputChange}
    />
  );
};

export default CurrencyInput;
