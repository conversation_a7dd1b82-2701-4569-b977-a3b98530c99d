import React from 'react';

export const SunIcon: React.FC = () => (
  <svg width="1em" height="1em" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M9.46756 5.14105C8.3521 5.14105 7.28233 5.58417 6.49358 6.37291C5.70483 7.16166 5.26172 8.23143 5.26172 9.34689C5.26172 10.4623 5.70483 11.5321 6.49358 12.3209C7.28233 13.1096 8.3521 13.5527 9.46756 13.5527C10.583 13.5527 11.6528 13.1096 12.4415 12.3209C13.2303 11.5321 13.6734 10.4623 13.6734 9.34689C13.6734 8.23143 13.2303 7.16166 12.4415 6.37291C11.6528 5.58417 10.583 5.14105 9.46756 5.14105Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.46769 0.0940552C9.69078 0.0940552 9.90474 0.182678 10.0625 0.340427C10.2202 0.498177 10.3089 0.712131 10.3089 0.935223V1.77639C10.3089 1.99948 10.2202 2.21344 10.0625 2.37119C9.90474 2.52894 9.69078 2.61756 9.46769 2.61756C9.2446 2.61756 9.03064 2.52894 8.87289 2.37119C8.71514 2.21344 8.62652 1.99948 8.62652 1.77639V0.935223C8.62652 0.712131 8.71514 0.498177 8.87289 0.340427C9.03064 0.182678 9.2446 0.0940552 9.46769 0.0940552V0.0940552ZM2.14364 2.02285C2.30138 1.86516 2.5153 1.77657 2.73835 1.77657C2.96139 1.77657 3.17531 1.86516 3.33305 2.02285L4.5948 3.2846C4.74803 3.44325 4.83282 3.65573 4.8309 3.87628C4.82898 4.09683 4.74052 4.30781 4.58456 4.46377C4.4286 4.61973 4.21762 4.70819 3.99707 4.71011C3.77652 4.71203 3.56404 4.62724 3.40539 4.47402L2.14364 3.21226C1.98595 3.05452 1.89736 2.84061 1.89736 2.61756C1.89736 2.39451 1.98595 2.1806 2.14364 2.02285V2.02285ZM16.7917 2.02285C16.9494 2.1806 17.038 2.39451 17.038 2.61756C17.038 2.84061 16.9494 3.05452 16.7917 3.21226L15.53 4.47402C15.4524 4.55436 15.3596 4.61844 15.2569 4.66252C15.1543 4.70661 15.0439 4.72981 14.9323 4.73078C14.8206 4.73175 14.7098 4.71047 14.6064 4.66818C14.503 4.62588 14.4091 4.56342 14.3301 4.48444C14.2512 4.40546 14.1887 4.31154 14.1464 4.20817C14.1041 4.10479 14.0828 3.99403 14.0838 3.88234C14.0848 3.77065 14.108 3.66027 14.1521 3.55764C14.1962 3.45502 14.2602 3.3622 14.3406 3.2846L15.6023 2.02285C15.7601 1.86516 15.974 1.77657 16.197 1.77657C16.4201 1.77657 16.634 1.86516 16.7917 2.02285ZM0.214844 9.3469C0.214844 9.12381 0.303466 8.90986 0.461216 8.75211C0.618966 8.59436 0.83292 8.50573 1.05601 8.50573H1.89718C2.12027 8.50573 2.33423 8.59436 2.49197 8.75211C2.64972 8.90986 2.73835 9.12381 2.73835 9.3469C2.73835 9.56999 2.64972 9.78395 2.49197 9.9417C2.33423 10.0994 2.12027 10.1881 1.89718 10.1881H1.05601C0.83292 10.1881 0.618966 10.0994 0.461216 9.9417C0.303466 9.78395 0.214844 9.56999 0.214844 9.3469ZM16.197 9.3469C16.197 9.12381 16.2857 8.90986 16.4434 8.75211C16.6012 8.59436 16.8151 8.50573 17.0382 8.50573H17.8794C18.1025 8.50573 18.3164 8.59436 18.4742 8.75211C18.6319 8.90986 18.7205 9.12381 18.7205 9.3469C18.7205 9.56999 18.6319 9.78395 18.4742 9.9417C18.3164 10.0994 18.1025 10.1881 17.8794 10.1881H17.0382C16.8151 10.1881 16.6012 10.0994 16.4434 9.9417C16.2857 9.78395 16.197 9.56999 16.197 9.3469ZM4.5948 14.2198C4.7525 14.3775 4.84109 14.5914 4.84109 14.8145C4.84109 15.0375 4.7525 15.2515 4.5948 15.4092L3.33305 16.6709C3.17441 16.8242 2.96193 16.909 2.74137 16.907C2.52082 16.9051 2.30985 16.8167 2.15389 16.6607C1.99793 16.5047 1.90946 16.2938 1.90755 16.0732C1.90563 15.8527 1.99042 15.6402 2.14364 15.4815L3.40539 14.2198C3.56314 14.0621 3.77705 13.9735 4.0001 13.9735C4.22315 13.9735 4.43706 14.0621 4.5948 14.2198V14.2198ZM14.3406 14.2198C14.4983 14.0621 14.7122 13.9735 14.9353 13.9735C15.1583 13.9735 15.3722 14.0621 15.53 14.2198L16.7917 15.4815C16.945 15.6402 17.0297 15.8527 17.0278 16.0732C17.0259 16.2938 16.9375 16.5047 16.7815 16.6607C16.6255 16.8167 16.4146 16.9051 16.194 16.907C15.9735 16.909 15.761 16.8242 15.6023 16.6709L14.3406 15.4092C14.1829 15.2515 14.0943 15.0375 14.0943 14.8145C14.0943 14.5914 14.1829 14.3775 14.3406 14.2198ZM9.46769 16.0762C9.69078 16.0762 9.90474 16.1649 10.0625 16.3226C10.2202 16.4804 10.3089 16.6943 10.3089 16.9174V17.7586C10.3089 17.9817 10.2202 18.1956 10.0625 18.3534C9.90474 18.5111 9.69078 18.5997 9.46769 18.5997C9.2446 18.5997 9.03064 18.5111 8.87289 18.3534C8.71514 18.1956 8.62652 17.9817 8.62652 17.7586V16.9174C8.62652 16.6943 8.71514 16.4804 8.87289 16.3226C9.03064 16.1649 9.2446 16.0762 9.46769 16.0762V16.0762Z"
      fill="currentColor"
    />
  </svg>
);
