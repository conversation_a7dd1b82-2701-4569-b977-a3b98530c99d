/* eslint-disable react/jsx-key */
import React, {ReactNode, useState, useEffect, useCallback, useMemo} from 'react';
import {useTranslation} from 'react-i18next';
import {RangeValue} from 'rc-picker/lib/interface.d';
import {Tag, ITag} from '@app/components/common/Tag/Tag';
import {AuthorValidator, TitleValidator, DatesValidator, TagsValidator} from '../Validator';
import {useResponsive} from '@app/hooks/useResponsive';
import {newsTags as defaultTags} from '@app/constants/newsTags';
import {AppDate, Dates} from '@app/constants/Dates';
import * as S from './RoomFilter.styles';
import ButtonGroup from 'antd/lib/button/button-group';
import {Button, Select} from 'antd';
import {MinusOutlined, PlusOutlined} from '@ant-design/icons';
import {BaseButtonsForm} from '@app/components/common/forms/BaseButtonsForm/BaseButtonsForm';
import {Option} from 'antd/lib/mentions';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import dayjs from 'dayjs';
import {fetchReservation, setCheckedDate} from '@app/store/slices/reservationSlice';
import {FilterProps} from '@app/components/apps/roomFeed/interface';
import {Input} from '@app/components/common/inputs/Input/Input.styles';
import {getAllRoomTypes} from '@app/api/hotel/roomType.api';

interface NewsFilterProps {
  newsTags?: ITag[];
  children: ({reservation}: {reservation: any}) => ReactNode;
  handlePressRoomStatus?: () => void;
}

interface Filter {
  author?: string;
  title: string;
  newsTagData?: ITag[];
  onTagClick?: (tag: ITag) => void;
  selectedTagsIds?: Array<string>;
  selectedTags?: ITag[];
  dates: [AppDate | null, AppDate | null];
  updateFilteredField: (field: string, value: [AppDate | null, AppDate | null] | string) => void;
  onApply: () => void;
  onReset: () => void;
  handlePressRoomStatus?: () => void;
}

export const Filter: React.FC<Filter> = ({
  author,
  title,
  newsTagData,
  onTagClick,
  selectedTagsIds,
  selectedTags,
  dates,
  onApply,
  onReset,
  updateFilteredField,
  handlePressRoomStatus,
}) => {
  const {t} = useTranslation();
  const {mobileOnly} = useResponsive();
  const dispatch = useAppDispatch();
  const {hotelId} = useAppSelector(state => state.hotelSlice.hotelConfig);

  const [selectedDates, setselectedDates] = useState({
    checkIn: '',
    checkOut: '',
  });

  const [adultCount, setadultCount] = useState(1);
  const [childCount, setchildCount] = useState(0);
  const [roomTypes, setroomTypes] = useState<Array<{title: string; value: string}>>([]);
  const [selectedType, setselectedType] = useState('');

  const descreaseAdult = () => {
    adultCount > 1 && setadultCount(adultCount - 1);
  };

  const increaseAdult = () => {
    setadultCount(adultCount + 1);
  };

  const descreaseChild = () => {
    childCount > 0 && setchildCount(childCount - 1);
  };

  const increaseChild = () => {
    setchildCount(childCount + 1);
  };

  const applyFilter = () => {
    const checkIn = selectedDates?.checkIn
      ? selectedDates?.checkIn
      : dispatch(setCheckedDate({checkedIn: selectedDates?.checkIn, checkedOut: selectedDates?.checkOut}));
    const filterPayload: FilterProps = {
      checkedIn: selectedDates?.checkIn,
      checkedOut: selectedDates?.checkOut,
      roomTypeId: selectedType,
    };
    dispatch(fetchReservation({hotelId, filterPayload}));
  };

  const resetFilter = () => {
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    const formatDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const formattedToday = formatDate(today);
    const formattedTomorrow = formatDate(tomorrow);
    dispatch(setCheckedDate({checkedIn: formattedToday, checkedOut: formattedTomorrow}));
    const filterPayload: FilterProps = {
      checkedIn: formattedToday,
      checkedOut: formattedTomorrow,
      roomTypeId: '',
    };
    dispatch(fetchReservation({hotelId, filterPayload}));
  };

  const listRoomTypes = async () => {
    try {
      const result = await getAllRoomTypes(hotelId, '', 100, 0);
      const dataList =
        result?.result?.roomType?.map((type: {roomTypeName: string; id: number}) => ({
          title: type.roomTypeName,
          value: type.id,
        })) || [];
      const allTypes = {title: 'All Types', value: ''};
      setroomTypes([allTypes, ...dataList]);
    } catch (error) {}
  };

  useEffect(() => {
    listRoomTypes();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <S.LeftBoxWrapper>
        <S.FilterWrapper>
          {!mobileOnly && (
            <>
              <S.HeaderWarapper>
                <S.FilterTitle>{t('reservation.searchRoom')}</S.FilterTitle>
                <S.RoomStatusButton onClick={handlePressRoomStatus}>Room Status</S.RoomStatusButton>
              </S.HeaderWarapper>
              <S.Description>{t('reservation.searchRoomSubTitle')}</S.Description>
            </>
          )}
          <S.RangePicker
            popupClassName="range-picker"
            placeholder={[t('reservation.checkIn'), t('reservation.checkOut')]}
            value={dates}
            onChange={(dates: RangeValue<AppDate>) => {
              updateFilteredField('dates', [dates?.length ? dates[0] : null, dates?.length ? dates[1] : null]);

              if (dates) {
                const convertedCheckedInDate = dayjs(dates[0]).format('YYYY-MM-DD');
                const convertedCheckedOutDate = dayjs(dates[1]).format('YYYY-MM-DD');
                setselectedDates({checkIn: convertedCheckedInDate, checkOut: convertedCheckedOutDate});
              }
            }}
          />

          <BaseButtonsForm.Item
            rules={[{required: true, message: t('forms.validationFormLabels.colorError'), type: 'array'}]}>
            <Select onSelect={value => setselectedType(value)} placeholder={t('reservation.selectRoomType')}>
              {roomTypes?.map((types, idx) => (
                <Option value={types.value}>{types.title}</Option>
              ))}
            </Select>
          </BaseButtonsForm.Item>

          <S.AdultWrapper>
            <S.AdultContainer>
              <S.FilterLabel>{t('reservation.adult')}</S.FilterLabel>
              <S.MiniDescription>Number of adult</S.MiniDescription>
            </S.AdultContainer>
            <ButtonGroup>
              <Button onClick={descreaseAdult} icon={<MinusOutlined />} />
              <S.CountInput value={adultCount} contentEditable={false} />
              <Button onClick={increaseAdult} icon={<PlusOutlined />} />
            </ButtonGroup>
          </S.AdultWrapper>

          <S.AdultWrapper>
            <S.AdultContainer>
              <S.FilterLabel>{t('reservation.children')}</S.FilterLabel>
              <S.MiniDescription>Number of child</S.MiniDescription>
            </S.AdultContainer>
            <ButtonGroup>
              <Button onClick={descreaseChild} icon={<MinusOutlined />} />
              <S.CountInput value={childCount} contentEditable={false} />
              <Button onClick={increaseChild} icon={<PlusOutlined />} />
            </ButtonGroup>
          </S.AdultWrapper>

          {/* {!!selectedTags.length && (
            <S.TagsWrapper>
              {selectedTags.map(tag => (
                <Tag key={tag.id} title={tag.title} bgColor={tag.bgColor} removeTag={() => onTagClick(tag)} />
              ))}
            </S.TagsWrapper>
          )} */}

          <S.BtnWrapper>
            <S.Btn onClick={() => resetFilter()}>{t('newsFeed.reset')}</S.Btn>
            <S.Btn onClick={() => applyFilter()} type="primary">
              {t('reservation.search')}
            </S.Btn>
          </S.BtnWrapper>
        </S.FilterWrapper>
      </S.LeftBoxWrapper>
    </>
  );
};

export const RoomFilter: React.FC<NewsFilterProps> = ({newsTags, children, handlePressRoomStatus}) => {
  const [filterFields, setFilterFields] = useState<{
    author: string;
    title: string;
    selectedTags: ITag[];
    dates: [AppDate | null, AppDate | null];
  }>({
    author: '',
    title: '',
    selectedTags: [],
    dates: [null, null],
  });
  const {author, title, selectedTags, dates} = filterFields;
  const [overlayOpen, setOverlayOpen] = useState<boolean>(false);
  const {mobileOnly} = useResponsive();
  const {t} = useTranslation();

  const newsTagData = Object.values(newsTags || defaultTags);
  const selectedTagsIds = useMemo(() => selectedTags.map(item => item.id), [selectedTags]);
  const {reservation} = useAppSelector(state => state.reservationSlice);

  const onTagClick = useCallback(
    (tag: ITag) => {
      const isExist = selectedTagsIds.includes(tag.id);

      if (isExist) {
        setFilterFields({
          ...filterFields,
          selectedTags: selectedTags.filter(item => item.id !== tag.id),
        });
      } else {
        setFilterFields({
          ...filterFields,
          selectedTags: [...selectedTags, tag],
        });
      }
    },
    [selectedTags, selectedTagsIds, filterFields],
  );

  const handleClickApply = useCallback(() => {
    if (mobileOnly) {
      setOverlayOpen(false);
    }
  }, []);

  const handleClickReset = useCallback(() => {
    setFilterFields({author: '', title: '', dates: [null, null], selectedTags: []});
    if (mobileOnly) {
      setOverlayOpen(false);
    }
  }, [setFilterFields, mobileOnly]);

  const updateFilteredField = (field: string, value: string | [AppDate | null, AppDate | null]) => {
    setFilterFields({...filterFields, [field]: value});
  };

  return (
    <div style={{padding: '15px'}}>
      <S.TitleWrapper>
        {mobileOnly && (
          <S.FilterPopover
            trigger="click"
            open={overlayOpen}
            onOpenChange={open => setOverlayOpen(open)}
            content={
              <Filter
                author={author}
                title={title}
                newsTagData={newsTagData}
                onTagClick={onTagClick}
                selectedTagsIds={selectedTagsIds}
                selectedTags={selectedTags}
                dates={dates}
                onApply={handleClickApply}
                onReset={handleClickReset}
                updateFilteredField={updateFilteredField}
                handlePressRoomStatus={handlePressRoomStatus}
              />
            }>
            <S.FilterButton>{t('newsFeed.filter')}</S.FilterButton>
          </S.FilterPopover>
        )}
      </S.TitleWrapper>

      <S.ContentWrapper>
        {!mobileOnly && (
          <Filter
            author={author}
            title={title}
            newsTagData={newsTagData}
            onTagClick={onTagClick}
            selectedTagsIds={selectedTagsIds}
            selectedTags={selectedTags}
            dates={dates}
            onApply={handleClickApply}
            onReset={handleClickReset}
            updateFilteredField={updateFilteredField}
            handlePressRoomStatus={handlePressRoomStatus}
          />
        )}
        <S.NewsWrapper>{children({reservation: reservation})}</S.NewsWrapper>
      </S.ContentWrapper>
    </div>
  );
};
