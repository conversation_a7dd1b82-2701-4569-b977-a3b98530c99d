import styled from 'styled-components';
import {Input as AntInput, Button, Checkbox} from 'antd';
import {SearchOutlined, PlusCircleFilled, CloseOutlined} from '@ant-design/icons';
import {DayjsDatePicker} from '@app/components/common/pickers/DayjsDatePicker';
import {BASE_COLORS, BORDER_RADIUS, FONT_SIZE, FONT_WEIGHT, media} from '@app/styles/themes/constants';
import {Popover} from '@app/components/common/Popover/Popover';

interface RoomTypeFilterOutlineProps {
  selected: boolean;
}

export const AddTagText = styled.span`
  color: var(--text-main-color);
  font-size: ${FONT_SIZE.md};
  font-weight: ${FONT_WEIGHT.semibold};
  line-height: 1.375rem;
`;

export const TitleWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const FilterButton = styled.div`
  height: 3.125rem;
  width: 6.125rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--background-color);
  border-radius: ${BORDER_RADIUS};
  font-size: ${FONT_SIZE.xs};
  font-weight: ${FONT_WEIGHT.semibold};
  line-height: 1.25rem;
`;

export const DateLabels = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const DateLabel = styled.div`
  width: 50%;
  line-height: 1.25rem;
  margin-bottom: 0.4375rem;
  font-size: ${FONT_SIZE.xs};
`;

export const AddTagWrapper = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 1rem;
`;

export const TagsWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1rem;
  gap: 0.625rem;
`;

export const BtnWrapper = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
`;

export const PlusIcon = styled(PlusCircleFilled)`
  color: var(--lightgrey);
  font-size: 1.5625rem;
  margin-right: 1rem;
`;

export const SearchIcon = styled(SearchOutlined)`
  position: absolute;
  z-index: 2;
  left: 1.25rem;
  top: 1.0625rem;
`;

export const InputWrapper = styled.div`
  display: flex;
  height: 3.125rem;
  border-radius: 3.125rem;
  background: var(--secondary-background-color);
  margin-bottom: 1.25rem;
  position: relative;
`;

export const ContentWrapper = styled.div`
  display: flex;
  gap: 3rem;
`;

export const TitleHeader = styled.div`
  display: inline-block;
  padding: 1rem;
  margin-bottom: 1.25rem;
  box-shadow: 0px 4px 40px rgba(0, 0, 0, 0.07);
  cursor: pointer;
  background-color: var(--background-color);
  border-radius: ${BORDER_RADIUS};
`;

export const Input = styled(AntInput)`
  height: 3.125rem;
  border: none;
  background: var(--secondary-background-color);
  border-radius: 3.125rem;
  padding-left: 3rem;
  color: var(--text-main-color);
  font-size: ${FONT_SIZE.md};
  font-weight: ${FONT_WEIGHT.semibold};
  line-height: 1.375rem;
`;

export const RangePicker = styled(DayjsDatePicker.RangePicker)`
  width: 100%;
  margin-bottom: 0.875rem;

  & input {
    color: var(--text-main-color);
    font-size: ${FONT_SIZE.xs};
    font-weight: ${FONT_WEIGHT.semibold};
  }
`;

export const Btn = styled(Button)`
  display: block;
  width: 100%;
`;

export const NewsWrapper = styled.div`
  max-width: 38rem;

  @media only screen and ${media.xs} {
    width: 100%;
  }

  @media only screen and ${media.md} {
    width: calc(100% - 21.25rem);
  }
`;

export const FilterWrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: min-content;
  box-shadow: var(--box-shadow-nft-color);
  @media only screen and ${media.md} {
    position: sticky;
    top: 0.5rem;
    padding: 1.25rem 1rem;
    background: var(--background-color);
    border-radius: ${BORDER_RADIUS};
  }
`;

export const LeftBoxWrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: min-content;
  @media only screen and ${media.md} {
    position: sticky;
    top: 0.5rem;
    border-radius: ${BORDER_RADIUS};
  }
`;

export const FilterTitle = styled.div`
  display: flex;
  justify-content: flex-start;
  line-height: 1.5625rem;
  font-size: ${FONT_SIZE.lg};
  font-weight: ${FONT_WEIGHT.bold};
  color: var(--text-main-color);
  margin-bottom: 0.5rem;
`;

export const RoomStatusButton = styled.div`
  font-size: ${FONT_SIZE.xs};
  color: var(--primary-color);
  cursor: pointer;
  font-weight: ${FONT_WEIGHT.semibold};
  text-align: center;
  @media only screen and ${media.xxl} {
    font-size: 1rem;
  }
`;

export const HeaderWarapper = styled.div`
  display: flex;
  justify-content: space-between;
  line-height: 1.5625rem;
  font-size: ${FONT_SIZE.lg};
  font-weight: ${FONT_WEIGHT.bold};
  color: var(--text-main-color);
  margin-bottom: 0.5rem;
`;

export const TagPopoverLine = styled.span`
  line-height: 1.25rem;
  display: flex;
  &:last-child {
    padding-bottom: 0;
  }
  align-items: center;
  cursor: pointer;
`;

export const PopoverCheckbox = styled(Checkbox)`
  margin-right: 1rem;
  & .ant-checkbox .ant-checkbox-inner {
    border-radius: 3px;
    height: 1.375rem;
    width: 1.375rem;
    border-color: var(--primary-color);
  }
  & .ant-checkbox-checked .ant-checkbox-inner::after {
    left: 0.375rem;
  }
`;

export const ClosePopoverWrapper = styled.div`
  position: absolute;
  right: 1rem;
  top: 1rem;
  cursor: pointer;
`;

export const ClosePopover = styled(CloseOutlined)`
  color: var(--primary-color);
  width: 0.875rem;
  height: 0.875rem;
`;

export const FilterPopover = styled(Popover)`
  & .ant-popover-inner-content {
    padding: 0;
  }
`;
export const Description = styled.div`
  font-size: ${FONT_SIZE.xs};
  color: var(--text-main-color);
  margin-bottom: 1rem;

  @media only screen and ${media.xxl} {
    font-size: 1rem;
  }
`;
export const AdultWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`;
export const MiniDescription = styled.div`
  font-size: ${FONT_SIZE.xxs};
  color: var(--text-main-color);
  margin-bottom: 1rem;

  @media only screen and ${media.xxl} {
    font-size: 1rem;
  }
`;
export const AdultContainer = styled.div`
  display: flex;
  flex-direction: column;
`;
export const FilterLabel = styled.div`
  width: 50%;
  line-height: 1.25rem;
  margin-bottom: 0.4375rem;
  font-size: ${FONT_SIZE.xs};
  font-weight: 700;
`;
export const Header = styled.div`
  line-height: 1.25rem;
  margin-bottom: 0.4375rem;
  font-size: ${FONT_SIZE.xxl};
  font-weight: 700;
`;
export const SubHeader = styled.div`
  line-height: 1.25rem;
  margin-bottom: 0.4375rem;
  font-size: ${FONT_SIZE.xs};
  font-weight: 500;
`;
export const Info = styled.div`
  line-height: 1.25rem;
  font-size: ${FONT_SIZE.xxs};
  font-weight: ${FONT_WEIGHT.semibold};
  color: ${BASE_COLORS.red};
`;
export const RoomTypeFilterRow = styled.div`
  display: -webkit-box;
  flex-direction: row;
  gap: 10px;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  overflow: scroll;
`;
export const RoomTypeFilterOutline = styled.div<RoomTypeFilterOutlineProps>`
  display: flex;
  border: 1px solid;
  padding-left: 0.7rem;
  padding-right: 0.7rem;
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
  border-radius: 3px;
  cursor: pointer;
  background-color: ${props => (props.selected ? BASE_COLORS.lightBlue : 'transparent')};
  border-color: ${props => (props.selected ? 'var(--primary-color);' : BASE_COLORS.lightgrey)};
`;
export const RoomTypeFilterLabel = styled.div`
  font-size: ${FONT_SIZE.xxs};
  color: var(--text-main-color);
  @media only screen and ${media.xxl} {
    font-size: 1rem;
  }
`;
export const AffixButton = styled(Button)`
  position: absolute;
  right: 0;
  align-self: right;
  border-bottom-left-radius: 5px;
  border-top-left-radius: 5px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  height: 2.5rem;
  padding-left: 0.7rem;
  padding-right: 0.7rem;
  box-shadow: var(--box-shadow-nft-color);
  z-index: 1;
  flex-direction: row;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border: none;
  background-color: var(--primary-color);
`;
export const HeaderWrapper = styled.div`
  display: flex;
  flex-direction: row;
`;

export const HeaderLeftWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;
export const BtnLabel = styled.div`
  font-size: ${FONT_SIZE.xxs};
  color: var(--white);
  font-weight: none;
  @media only screen and ${media.xxl} {
    font-size: 1rem;
  }
`;
export const CountInput = styled(AntInput)`
  width: 3rem;
  border-radius: 0px;
  border: 0;
  border-top: 1px solid var(--border-base-color);
  border-bottom: 1px solid var(--border-base-color);
  font-weight: ${FONT_WEIGHT.semibold};
  text-align: center;
`;
