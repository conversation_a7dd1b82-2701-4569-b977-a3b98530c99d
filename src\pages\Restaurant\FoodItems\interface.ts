import {FormInstance, UploadFile} from 'antd';

export interface IFoodData {
  id: number;
  name: string;
  price: number;
  image: any;
  categoryStucture: any[];
  ticketType: string;
  itemType: string;
  active: boolean;
}

export interface FieldData {
  name: string | number;
  value?: string;
}

export interface Props {
  form: FormInstance;
  reloadData: () => void;
  rowData: IFoodData;
  fileList?: UploadFile[];
  setFileList?: string[];
}
