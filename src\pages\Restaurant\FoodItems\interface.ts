import {FormInstance, UploadFile} from 'antd';

export interface IFoodIngredient {
  id: number;
  name: string;
  quantity: number;
  unit: string;
  costPerUnit: number;
  totalCost: number;
}

export interface IFoodData {
  id: number;
  name: string;
  price: number;
  image: any;
  categoryStucture: any[];
  ticketType: string;
  itemType: string;
  active: boolean;
  ingredients?: IFoodIngredient[];
}

export interface FieldData {
  name: string | number;
  value?: string;
}

export interface Props {
  form: FormInstance;
  reloadData: () => void;
  rowData: IFoodData;
  fileList?: UploadFile[];
  setFileList?: string[];
}
