import React, {useState} from 'react';
import {Form, Input, Button, Card, Row, Col, Typography, Space, Checkbox} from 'antd';
import {UserOutlined, PhoneOutlined, CalendarOutlined, TeamOutlined, ClockCircleOutlined} from '@ant-design/icons';
import {useNavigate, useLocation} from 'react-router-dom';
import {PageTitle} from '@app/components/common/PageTitle/PageTitle';
import {notificationController} from '@app/controllers/notificationController';

const {Title, Text} = Typography;

interface CustomerInfoForm {
  customerName: string;
  phoneNumber: string;
  wantPreOrder: boolean;
}

interface BookingDetails {
  guests: string;
  date: string;
  time: string;
}

const CustomerInfo: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Get booking details from navigation state
  const bookingDetails: BookingDetails = location.state?.bookingDetails || {
    guests: '1 Person',
    date: '2025-03-06',
    time: '9:00 am',
  };

  const onFinish = async (values: CustomerInfoForm) => {
    try {
      setLoading(true);

      // Validate phone number (basic validation)
      if (values.phoneNumber.length < 10) {
        notificationController.error({message: 'Please enter a valid phone number'});
        setLoading(false);
        return;
      }

      // Store customer info and booking details
      const reservationData = {
        customerInfo: {
          name: values.customerName,
          phone: values.phoneNumber,
        },
        bookingDetails: bookingDetails,
        wantPreOrder: values.wantPreOrder,
        timestamp: new Date().toISOString(),
      };

      // Store in localStorage for now (replace with API call)
      localStorage.setItem('currentReservation', JSON.stringify(reservationData));

      if (values.wantPreOrder) {
        // Navigate to pre-order page
        navigate('/toplevel/pre-order', {
          state: {reservationData},
        });
      } else {
        // Navigate to confirmation page or complete booking
        notificationController.success({
          message: `Reservation confirmed for ${values.customerName}!`,
          description: `Table for ${bookingDetails.guests} on ${bookingDetails.date} at ${bookingDetails.time}`,
        });

        // Navigate back to home or reservation page
        navigate('/toplevel/home');
      }
    } catch (error) {
      notificationController.error({message: 'Failed to process reservation. Please try again.'});
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/toplevel/reservation');
  };

  return (
    <div style={{padding: '20px', maxWidth: '800px', margin: '0 auto'}}>
      <PageTitle>Complete Your Reservation</PageTitle>

      {/* Booking Summary */}
      <Card
        style={{marginBottom: '24px'}}
        title={
          <Space>
            <CalendarOutlined />
            <span>Booking Summary</span>
          </Space>
        }>
        <Row gutter={24}>
          <Col span={8}>
            <Space direction="vertical" size="small">
              <Text type="secondary">Guests</Text>
              <Text strong>
                <TeamOutlined style={{marginRight: 8}} />
                {bookingDetails.guests}
              </Text>
            </Space>
          </Col>
          <Col span={8}>
            <Space direction="vertical" size="small">
              <Text type="secondary">Date</Text>
              <Text strong>
                <CalendarOutlined style={{marginRight: 8}} />
                {bookingDetails.date}
              </Text>
            </Space>
          </Col>
          <Col span={8}>
            <Space direction="vertical" size="small">
              <Text type="secondary">Time</Text>
              <Text strong>
                <ClockCircleOutlined style={{marginRight: 8}} />
                {bookingDetails.time}
              </Text>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Customer Information Form */}
      <Card
        title={
          <Space>
            <UserOutlined />
            <span>Customer Information</span>
          </Space>
        }>
        <Form form={form} layout="vertical" onFinish={onFinish} size="large">
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="customerName"
                label="Full Name"
                rules={[
                  {required: true, message: 'Please enter your full name'},
                  {min: 2, message: 'Name must be at least 2 characters'},
                ]}>
                <Input prefix={<UserOutlined />} placeholder="Enter your full name" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="phoneNumber"
                label="Phone Number"
                rules={[
                  {required: true, message: 'Please enter your phone number'},
                  {pattern: /^[0-9+\-\s()]+$/, message: 'Please enter a valid phone number'},
                ]}>
                <Input prefix={<PhoneOutlined />} placeholder="Enter your phone number" />
              </Form.Item>
            </Col>
          </Row>

          {/* Pre-order Option */}
          <Form.Item name="wantPreOrder" valuePropName="checked" style={{marginTop: '24px'}}>
            <Checkbox>
              <Space direction="vertical" size="small">
                <Text strong>I want to pre-order food</Text>
                <Text type="secondary" style={{fontSize: '14px'}}>
                  Select your meals in advance to save time when you arrive
                </Text>
              </Space>
            </Checkbox>
          </Form.Item>

          {/* Action Buttons */}
          <Form.Item style={{marginTop: '32px', marginBottom: 0}}>
            <Space size="large">
              <Button size="large" onClick={handleBack} style={{minWidth: '120px'}}>
                Back
              </Button>
              <Button type="primary" htmlType="submit" loading={loading} size="large" style={{minWidth: '120px'}}>
                {form.getFieldValue('wantPreOrder') ? 'Continue to Pre-Order' : 'Complete Reservation'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default CustomerInfo;
