/**
 * <AUTHOR>
 * @email [johnh<PERSON><EMAIL>]
 * @create date 2024-12-11 17:22:21
 * @modify date 2024-12-11 17:22:21
 * @desc [hotel data api]
 */
import instance, {HOTEL_SERVICE} from '../instance';

export interface Hotel {
  id: number;
  name: string;
  websiteLogo: string;
  backgroundImage: string;
  maxAdultCount: number;
  maxChildCount: number;
}

interface HotelDataResponse {
  status: string;
  statusCode: string;
  message: string;
  result: {
    hotel: Hotel[];
  };
}
export const getHotelData = (): Promise<HotelDataResponse> =>
  instance.get<HotelDataResponse>(`${HOTEL_SERVICE}hotel/group/room/1`).then(({data}) => data);
