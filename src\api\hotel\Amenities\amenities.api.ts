import instance, {HOTEL_SERVICE} from '@app/api/instance';
import {IconList} from 'react-fa-icon-picker';

export const CreateAmenity = (payload: CreateAmenityProps): Promise<AmenityResponse> => {
  return instance.post<AmenityResponse>(HOTEL_SERVICE + 'amenities', payload).then(({data}) => data);
};

export const UpdateAmenity = (payload: UpdateAmenityProps): Promise<AmenityResponse> => {
  return instance.put<AmenityResponse>(HOTEL_SERVICE + 'amenities', payload).then(({data}) => data);
};

export const getAllAmenities = (): Promise<AmenityResponse> =>
  instance.get<AmenityResponse>(HOTEL_SERVICE + `amenities`).then(({data}) => data);

export const searchAmenities = (name: string): Promise<AmenityResponse> =>
  instance.get<AmenityResponse>(HOTEL_SERVICE + `amenities/filter/search?name=${name}`).then(({data}) => data);

export const getAllAmenitieswithPagination = (
  hotelId: number,
  {name}: FilterPropsAmenity,
  pageSize: number | undefined,
  current: number,
): Promise<AmenityResponse> =>
  instance
    .get<AmenityResponse>(
      HOTEL_SERVICE +
        `amenities/search?page=${current}&size=${pageSize}&sortField=amenityType&direction=ASC&name=${
          name ? name : ''
        }&hotelId=${hotelId}`,
    )
    .then(({data}) => data);

export const DeleteAmenity = (id: number): Promise<AmenityResponse> =>
  instance.delete<AmenityResponse>(HOTEL_SERVICE + `amenities/${id}`).then(({data}) => data);

export interface CreateAmenityProps {
  name: string;
  amenityType: string;
  amenityIcon: IconList;
}

export interface UpdateAmenityProps {
  id: number;
  name: string;
  amenityType: string;
  amenityIcon: IconList;
}

export interface AmenityResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterPropsAmenity {
  name: string;
  // amenityType: string;
  // amenityIcon: IconList;
}
