import inventoryInstance, {INVENTORY_SERVICE} from '@app/api/inventoryInstance';

export const CreateItem = (payload: CreateItemProps): Promise<ItemResponse> => {
  return inventoryInstance.post<ItemResponse>(INVENTORY_SERVICE + 'item', payload).then(({data}) => data);
};

export const UpdateItem = (payload: UpdateItemProps): Promise<ItemResponse> => {
  return inventoryInstance.put<ItemResponse>(INVENTORY_SERVICE + 'item', payload).then(({data}) => data);
};

export const getAllItems = (
  groupId: number,
  groupInventoryServiceId: number | undefined,
  {name, subCategoryName}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<ItemResponse> =>
  inventoryInstance
    .get<ItemResponse>(
      INVENTORY_SERVICE +
        `item/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${
          name ? name : ''
        }&subCategoryName=${
          subCategoryName ? subCategoryName : ''
        }&groupId=${groupId}&groupInventoryServiceId=${groupInventoryServiceId}`,
    )
    .then(({data}) => data);

export const DeleteItem = (id: number): Promise<ItemResponse> =>
  inventoryInstance.delete<ItemResponse>(INVENTORY_SERVICE + `item/${id}`).then(({data}) => data);

export const getAllUnitsWithoutSearch = (): Promise<ItemResponse> =>
  inventoryInstance.get<ItemResponse>(INVENTORY_SERVICE + `units`).then(({data}) => data);

export const getAllSubCategoriesWithoutSearch = (groupInventoryServiceId: number | undefined): Promise<ItemResponse> =>
  inventoryInstance
    .get<ItemResponse>(INVENTORY_SERVICE + `subCategory/group-inventory-service/${groupInventoryServiceId}`)
    .then(({data}) => data);

export const getAllSubHotelCategoriesWithoutSearch = (
  groupInventoryServiceId: number | undefined,
  hotelId: number,
): Promise<ItemResponse> =>
  inventoryInstance
    .get<ItemResponse>(
      INVENTORY_SERVICE + `hotel-subCategory/group-inventory-service/${groupInventoryServiceId}/hotel/${hotelId}`,
    )
    .then(({data}) => data);

export const getAllItemsForDropDown = (
  groupId: number,
  groupInventoryServiceId: number | undefined,
  {name}: DropDownFilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<ItemResponse> =>
  inventoryInstance
    .get<ItemResponse>(
      INVENTORY_SERVICE +
        `item/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${
          name ? name : ''
        }&groupId=${groupId}&groupInventoryServiceId=${groupInventoryServiceId}`,
    )
    .then(({data}) => data);

export const getAllItemsForGroupnWithoutPagination = (
  groupId: number,
  groupInventoryServiceId: number | undefined,
  {name}: DropDownFilterProps,
): Promise<ItemResponse> =>
  inventoryInstance
    .get<ItemResponse>(
      INVENTORY_SERVICE +
        `group-opening-stock/item/search?name=${
          name ? name : ''
        }&groupId=${groupId}&groupInventoryServiceId=${groupInventoryServiceId}`,
    )
    .then(({data}) => data);

export const getAllItemsForGroupWithoutPagination = (
  hotelId: number,
  inventoryServiceId: number | undefined,
  {name}: DropDownFilterProps,
  groupId: number,
): Promise<ItemResponse> =>
  inventoryInstance
    .get<ItemResponse>(
      INVENTORY_SERVICE +
        `group-opening-stock/without-pagination/search?name=${
          name ? name : ''
        }&groupInventoryServiceId=${inventoryServiceId}&groupId=${groupId}`,
    )
    .then(({data}) => data);

export const getAllItemsForHotelWithoutPagination = (
  hotelId: number,
  inventoryServiceId: number | undefined,
  {name}: DropDownFilterProps,
  groupId: number,
): Promise<ItemResponse> =>
  inventoryInstance
    .get<ItemResponse>(
      INVENTORY_SERVICE +
        `opening-stock/item/search?name=${
          name ? name : ''
        }&hotelId=${hotelId}&inventoryServiceId=${inventoryServiceId}&groupId=${groupId}`,
    )
    .then(({data}) => data);

export const getAllItemsForDropDownWithHotelId = (
  groupId: number,
  groupInventoryServiceId: number | undefined,
  hotelId: number,
  {name}: DropDownFilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<ItemResponse> =>
  inventoryInstance
    .get<ItemResponse>(
      INVENTORY_SERVICE +
        `item/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${
          name ? name : ''
        }&groupId=${groupId}&hotelId=${hotelId}&groupInventoryServiceId=${groupInventoryServiceId}`,
    )
    .then(({data}) => data);
export const getAllItemsForPurchaseOrder = (
  hotelId: number,
  groupId: number,
  groupInventoryServiceId: number | undefined,
  {name}: DropDownFilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<ItemResponse> =>
  inventoryInstance
    .get<ItemResponse>(
      INVENTORY_SERVICE +
        `item/purchase-order/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&name=${
          name ? name : ''
        }&groupId=${groupId}&groupInventoryServiceId=${groupInventoryServiceId}&hotelId=${hotelId}`,
    )
    .then(({data}) => data);

export const getAllGRNNumbers = (hotelId: number, inventoryServiceId: number): Promise<any> =>
  inventoryInstance
    .get<any>(INVENTORY_SERVICE + `grn-number/hotel/${hotelId}/inventory-service/${inventoryServiceId}`)
    .then(({data}) => data);

export const getItemsByStockTransfer = (transferId: number): Promise<any> =>
  inventoryInstance.get<any>(INVENTORY_SERVICE + `group-stock-transfer/${transferId}`).then(({data}) => data);

export const getReportStockDataGroup = (
  hotelId: number,
  inventoryServiceId: number,
  date: string,
  itemId: number,
  groupId: number,
): Promise<any> =>
  inventoryInstance
    .get<any>(
      INVENTORY_SERVICE +
        `group-opening-stock/stock-report?sortField=id&direction=ASC&size=10&page=0&date=${date}&hotelId=${hotelId}&inventoryServiceId=${inventoryServiceId}&itemId=${itemId}&groupId=${groupId}`,
    )
    .then(({data}) => data);
export const getReportStockData = (
  hotelId: number,
  inventoryServiceId: number,
  date: string,
  itemId: number,
  groupId: number,
): Promise<any> =>
  inventoryInstance
    .get<any>(
      INVENTORY_SERVICE +
        `opening-stock/stock-report?sortField=id&direction=ASC&size=10&page=0&date=${date}&hotelId=${hotelId}&inventoryServiceId=${inventoryServiceId}&itemId=${itemId}&groupId=${groupId}`,
    )
    .then(({data}) => data);

export interface CreateItemProps {
  name: string;
  itemType: string;
  subCategoryId: number;
  barCode: string;
  unitId: number;
  unitPrice: number;
  reorderLevel: number;
  notifyExpireDate: boolean;
  notifyExpireDays: number;
  groupInventoryServiceId: number | undefined;
  hotelId: number;
  laundryItem: boolean;
  groupId: number;
}

export interface UpdateItemProps {
  id: number;
  name: string;
  itemType: string;
  subCategoryId: number;
  barCode: string;
  unitId: number;
  unitPrice: number;
  reorderLevel: number;
  notifyExpireDate: boolean;
  notifyExpireDays: number;
  groupInventoryServiceId: number | undefined;
  hotelId: number;
  laundryItem: boolean;
  groupId: number;
}

export interface ItemResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  name: string;
  subCategoryName: string;
}

export interface DropDownFilterProps {
  name: string;
}
