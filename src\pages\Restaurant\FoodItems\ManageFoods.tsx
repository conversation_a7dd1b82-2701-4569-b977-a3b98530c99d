/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-05-03 16:53:46
 * @modify date 2023-05-03 16:53:46
 * @desc [room page]
 */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Tables} from '@app/components/tables/Tables/Tables';
import {PageTitle} from '@app/components/common/PageTitle/PageTitle';
import * as S from './Food.style';
import {Avatar, Card, Col, Popover, Row, Space, Tag, UploadFile} from 'antd';
import {DeleteOutlined, EditOutlined, FileImageOutlined} from '@ant-design/icons';
import {BASE_COLORS, ROOM_STATUS_COLOR} from '@app/styles/themes/constants';
import {ColumnsType} from 'antd/lib/table';
import {FoodForm} from './FoodForm';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {setIsEditAction, setLoading, setModalVisible} from '@app/store/slices/commonSlice';
import {TablePaginationConfig} from 'antd/es/table';
import {Button} from '@app/components/common/buttons/Button/Button';
import {Popconfirm} from '@app/components/common/Popconfirm/Popconfirm';

import {notificationController} from '@app/controllers/notificationController';
import {IFoodData} from './interface';
import {FaUserAlt} from 'react-icons/fa';
import Meta from 'antd/lib/card/Meta';
import Paragraph from 'antd/lib/typography/Paragraph';
import {DeleteFoodItem, getAllFoodItems, FilterProps} from '@app/api/resturant/tablecategory/foodItems.api';
import {RESTAURANT_ADMIN_MODULE_NAME, modulePermission} from '@app/utils/permissions';
import _ from 'lodash';

const urlToFile = async (url: string) => {
  const response = await fetch(url);
  const blob = await response.blob();
  const file = new File([blob], `image.png`, {type: blob.type});
  return file;
};

const ManageFood: React.FC = () => {
  //get permission
  const userPermission = useAppSelector(state => state.user.permissions);
  const permissions = modulePermission(userPermission, RESTAURANT_ADMIN_MODULE_NAME.FOOD_ITEMS);
  const {t} = useTranslation();
  const [form] = BaseForm.useForm();
  const loading = useAppSelector(state => state.commonSlice.loading);
  const isEditAction = useAppSelector(state => state.commonSlice.isEditAction);
  const isTouch = useAppSelector(state => state.commonSlice.isTouch);
  const hotelConfig = useAppSelector(state => state.hotelSlice.hotelConfig);
  const hotelServiceConfig = useAppSelector(state => state.hotelSlice.hotelServiceConfig);
  const [fileList, setFileList]: any = React.useState<UploadFile[]>([]);
  const [rowData, setRowData] = useState<IFoodData>({
    id: 0,
    categoryStucture: [],
    image: '',
    name: '',
    price: 0,
    ticketType: '',
    itemType: '',
    active: true,
  });
  let [searchObj]: any = useState({});

  const [rooms, setrooms] = useState<IFoodData[]>([]);
  const [pagination, setPagination] = React.useState<TablePaginationConfig>({current: 0, pageSize: 10, total: 0});

  const dispatch = useAppDispatch();

  const content = (record: IFoodData) => {
    return (
      <div>
        <Card
          style={{width: 300}}
          bodyStyle={{padding: '0px'}}
          cover={<img style={{width: 300, height: 300, objectFit: 'cover'}} alt="" src={record.image} />}></Card>
      </div>
    );
  };

  const columns: ColumnsType<IFoodData> = [
    {
      title: 'Name',
      dataIndex: 'name',
      align: 'left',
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: 'Price',
      dataIndex: 'price',
      align: 'center',
      render: (text: string) => <span>{Number(text).toFixed(2)}</span>,
    },
    {
      title: 'Image',
      dataIndex: 'image',
      showSorterTooltip: false,
      align: 'center',
      render: (_text: string, record: IFoodData) => {
        return (
          <Space>
            <div>
              <Popover content={() => content(record)} title={record.name} trigger="hover" placement="left">
                <Avatar icon={<FileImageOutlined />} src={record.image} />
              </Popover>
            </div>
          </Space>
        );
      },
    },
    {
      title: 'Categories',
      dataIndex: 'categoryStucture',
      align: 'center',
      render: (_text: string, record: IFoodData) => {
        return (
          <div style={{display: 'flex'}}>
            <div
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                justifyContent: 'center',
              }}>
              {record?.categoryStucture?.map((post: any, i: number) => {
                if (i < 4) {
                  return (
                    <div
                      key={i}
                      style={{
                        display: 'flex',
                        justifyContent: 'space-around',
                        alignItems: 'center',
                        border: '1px solid gray',
                        borderRadius: 5,
                        padding: '0px 6px',
                        marginBottom: 2,
                        marginRight: 4,
                      }}>
                      <div
                        style={{
                          fontSize: 10,
                          textOverflow: 'ellipsis',
                        }}>
                        {post.title}
                      </div>
                    </div>
                  );
                }
              })}
              {record.categoryStucture.length > 5 && (
                <Popover
                  placement="right"
                  content={
                    <Row
                      style={{
                        width: 250,
                      }}
                      gutter={5}>
                      {record.categoryStucture.map((post: {title: string; imageUrl: string}, i: number) => {
                        return (
                          <Col key={i} md={8}>
                            <div
                              style={{
                                display: 'flex',

                                alignItems: 'center',
                                // border: '1px solid gray',
                                borderRadius: 5,
                                padding: '0px 6px',
                                width: '100%',
                                marginBottom: 2,
                                marginRight: 4,
                              }}>
                              {/* <div
                                style={{
                                  paddingTop: 5,
                                }}></div> */}

                              <div
                                style={{
                                  fontSize: 12,
                                  textOverflow: 'ellipsis',
                                }}>
                                {post.title}
                              </div>
                            </div>
                          </Col>
                        );
                      })}
                    </Row>
                  }
                  trigger="click">
                  <div
                    style={{
                      textDecoration: 'underline',
                      fontSize: 11,
                      cursor: 'pointer',
                    }}>
                    View more
                  </div>
                </Popover>
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Ticket Type',
      dataIndex: 'ticketType',
      align: 'center',
    },

    {
      title: t('tables.actions'),
      dataIndex: 'actions',
      render: (_text: string, record: IFoodData) => {
        return (
          <Space>
            <div hidden={!permissions.EDIT}>
              <EditOutlined
                style={{color: BASE_COLORS.blue}}
                onClick={async () => {
                  dispatch(setModalVisible(true));
                  dispatch(setIsEditAction(true));

                  try {
                    const imageList: UploadFile[] = [];

                    const categoryStructureIdList = record.categoryStucture.map((post: any) => post.id);

                    // form.setFieldValue('categoryStucture', categoryStructureIdList);
                    const file = await urlToFile(record?.image);

                    const editData: any = {
                      id: record?.id,
                      categoryStucture: categoryStructureIdList,
                      image: file,
                      name: record.name,
                      price: record.price,
                      ticketType: record.ticketType,
                      itemType: record.itemType,
                      active: record.active,
                    };
                    form.setFieldsValue(editData);
                    setRowData(editData);
                  } catch (error) {}
                }}
              />
            </div>
            <div hidden={!permissions.DELETE}>
              <Popconfirm
                title="Are you sure to delete this data?"
                onConfirm={() => {
                  deleteFood(record.id);
                }}
                onCancel={() => console.log('onCancel')}
                okText="Yes"
                cancelText="No">
                <DeleteOutlined style={{color: BASE_COLORS.red}} />
              </Popconfirm>
            </div>
          </Space>
        );
      },
      align: 'center',
    },
  ];

  const listFoodItems = async (searchQuery: FilterProps, pageSize: number | undefined, current: number) => {
    try {
      const data: IFoodData[] = [];

      const result: any = await getAllFoodItems(
        hotelConfig.hotelId,
        hotelServiceConfig.serviceId,
        searchQuery,
        pageSize,
        current,
      );

      result?.result?.item.map((post: any, index: number) => {
        data.push({
          categoryStucture: post.categoryList,
          id: post.id,
          image: post.image,
          itemType: post.itemType,
          name: post.name,
          price: post.price,
          ticketType: post.ticketType,
          active: post.active,
        });
      });
      setrooms(data);
      setPagination({
        pageSize: pageSize,
        current: result.pagination.pageNumber + 1,
        total: result.pagination.totalRecords,
      });
    } catch (error) {}
  };

  const deleteFood = async (id: number) => {
    try {
      const result = await DeleteFoodItem(id);
      if (result.statusCode === '20000') {
        notificationController.success({message: result.message});
        listFoodItems(searchObj, pagination.pageSize, pagination.current ? pagination.current - 1 : 0);
      } else {
        notificationController.error({message: result.message});
      }
    } catch (error) {}
  };

  useEffect(() => {
    listFoodItems(searchObj, pagination.pageSize, 0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const resetForm = () => {
    form.resetFields();
    dispatch(setLoading(false));
    setFileList([]);
  };
  const handlePagination = (pagination: TablePaginationConfig) => {
    setPagination({
      pageSize: pagination.pageSize,
      current: pagination.current ? pagination.current - 1 : 0,
      total: pagination.total,
    });
    listFoodItems(searchObj, pagination.pageSize, pagination.current ? pagination.current - 1 : 0);
  };

  const onChangeTableSearch = (values: any) => {
    const obj: any = {...searchObj, ...values};
    searchObj = obj;
    listFoodItems(obj, pagination.pageSize, 0);
  };

  !permissions.DELETE && !permissions.EDIT && _.remove(columns, (col: any) => col.dataIndex === 'actions');

  return (
    <div style={{fontFamily: "'Inter', serif"}}>
      <PageTitle>Foods</PageTitle>
      <S.CurrencyWrapper>
        <S.TableWrapper style={{borderRadius: '12px', overflow: 'hidden'}}>
          <Tables
            isCreate={permissions.ADD}
            title="Food"
            tableData={rooms}
            columns={columns}
            searchFields={['name']}
            onChangeFilter={handlePagination}
            onChangeSearch={onChangeTableSearch}
            modalChildren={
              <FoodForm
                fileList={fileList}
                setFileList={setFileList}
                form={form}
                rowData={rowData}
                reloadData={() =>
                  listFoodItems(searchObj, pagination.pageSize, pagination.current ? pagination.current - 1 : 0)
                }
              />
            }
            modalSize="medium"
            onCancelModal={() => resetForm()}
            modalTitle={isEditAction ? 'Update Food' : 'Create Food'}
            pagination={{
              defaultPageSize: 10,
              defaultCurrent: 0,
              current: pagination.current,
              total: pagination.total,
              showSizeChanger: true,
              pageSizeOptions: ['10', '20'],
            }}
            modalFooter={
              <Space>
                {isEditAction ? (
                  <Button
                    danger
                    title="Cancel"
                    type="ghost"
                    onClick={() => {
                      dispatch(setIsEditAction(false));
                      dispatch(setModalVisible(false));
                    }}>
                    Cancel
                  </Button>
                ) : (
                  <Button
                    danger
                    title="Clear"
                    type="ghost"
                    onClick={() => {
                      resetForm();
                    }}>
                    Clear
                  </Button>
                )}
                <Button
                  title=""
                  disabled={isEditAction ? !isTouch : false}
                  type="primary"
                  loading={loading}
                  // eslint-disable-next-line @typescript-eslint/no-unused-vars
                  onClick={_e => {
                    form.submit();
                  }}>
                  {isEditAction ? 'Update' : 'Save'}
                </Button>
              </Space>
            }
          />
        </S.TableWrapper>
      </S.CurrencyWrapper>
    </div>
  );
};

export default ManageFood;
