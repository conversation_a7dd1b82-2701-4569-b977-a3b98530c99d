import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const getAllAdditionalServices = (
  hotelId: number,
  {service}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<AdditionalServiceResponse> =>
  instance
    .get<AdditionalServiceResponse>(
      HOTEL_SERVICE +
        `additional-service/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&hotelId=${hotelId}&service=${
          service ? service : ''
        }`,
    )
    .then(({data}) => data);

export const CreateAdditionalServices = (payload: CreateAdditionalServiceProps): Promise<AdditionalServiceResponse> => {
  return instance.post<AdditionalServiceResponse>(HOTEL_SERVICE + 'additional-service', payload).then(({data}) => data);
};

export const UpdateAdditionalServices = (payload: UpdateAdditionalServiceProps): Promise<AdditionalServiceResponse> => {
  return instance.put<AdditionalServiceResponse>(HOTEL_SERVICE + 'additional-service', payload).then(({data}) => data);
};

export const DeleteAdditionalServiceResponse = (id: number): Promise<AdditionalServiceResponse> =>
  instance.delete<AdditionalServiceResponse>(HOTEL_SERVICE + `additional-service/${id}`).then(({data}) => data);

export interface AdditionalServiceResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}
export interface FilterProps {
  service: string;
}

export interface CreateAdditionalServiceProps {
  service: string;
  count: number;
  hourLkrPrice: number;
  hourUsdPrice: number;
  halfDayLkrPrice: number;
  halfDayUsdPrice: number;
  fullDayLkrPrice: number;
  fullDayUsdPrice: number;
  hotelId: number;
  hallId?: number;
  taxInclude: boolean;
}

export interface UpdateAdditionalServiceProps {
  id: number;
  service: string;
  count: number;
  hourLkrPrice: number;
  hourUsdPrice: number;
  halfDayLkrPrice: number;
  halfDayUsdPrice: number;
  fullDayLkrPrice: number;
  fullDayUsdPrice: number;
  hotelId: number;
  hallId: number;
  taxInclude: boolean;
}
