import React, {useEffect, useState} from 'react';
import * as S from './HorizontalCalendar.style';

const HorizontalCalendar = ({onDateClick}: {onDateClick: (date: Date) => void}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [wrapperWidth, setWrapperWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWrapperWidth(window.innerWidth);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleDayClick = (date: Date) => {
    setSelectedDate(date);
    onDateClick(date);
  };

  const daysInMonth = (month: number, year: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const prevMonth = () => {
    setCurrentDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(prevDate.getMonth() - 1);
      return newDate;
    });
  };

  const nextMonth = () => {
    setCurrentDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(prevDate.getMonth() + 1);
      return newDate;
    });
  };

  const renderCalendarDays = () => {
    const days = [];
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const numDays = daysInMonth(month, year);

    const today = new Date();
    const endOfMonth = new Date(year, month + 1, 0);

    for (let day = 1; day <= numDays; day++) {
      const date = new Date(year, month, day);
      const weekday = date.toLocaleString('default', {weekday: 'short'});
      const isToday = date.toDateString() === today.toDateString();
      const isSelected = selectedDate ? date.toDateString() === selectedDate.toDateString() : false;
      const isDisabled = date > today || date > endOfMonth;

      days.push(
        <S.CalendarDay
          onClick={() => !isDisabled && handleDayClick(date)}
          key={date.toISOString()}
          isToday={isToday}
          isSelected={isSelected}
          isDisabled={isDisabled}>
          <S.DayText isSelected={isSelected} isDisabled={isDisabled}>
            {weekday}
          </S.DayText>
          <S.DayText isSelected={isSelected} isDisabled={isDisabled}>
            {day}
          </S.DayText>
        </S.CalendarDay>,
      );
    }

    return <S.DaysContainer>{days}</S.DaysContainer>;
  };

  return (
    <S.CalendarContainer>
      {/* <S.MonthHeader>
        <button onClick={prevMonth}>&lt;</button>
        <S.MonthTitle>{currentDate.toLocaleString('default', {month: 'long', year: 'numeric'})}</S.MonthTitle>
        <button onClick={nextMonth}>&gt;</button>
      </S.MonthHeader> */}
      <S.MonthHeader>
        <button onClick={prevMonth}>&lt;</button>
        <S.MonthTitle>{currentDate.toLocaleString('default', {month: 'long', year: 'numeric'})}</S.MonthTitle>
        <button onClick={nextMonth}>&gt;</button>
      </S.MonthHeader>
      <S.CalendarWrapper $maxWidth={wrapperWidth}>{renderCalendarDays()}</S.CalendarWrapper>
    </S.CalendarContainer>
  );
};

export default HorizontalCalendar;
