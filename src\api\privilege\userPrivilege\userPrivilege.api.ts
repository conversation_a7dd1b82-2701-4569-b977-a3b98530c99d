import authInstance from '@app/api/authInstance';
import {LOGIN_SERVICE} from '@app/api/instance';

export const getAllUserPermissionByUserIdAndServiceRoleId = (
  userId: number,
  serviceRoleId: number,
): Promise<UserPrivilegeResponse> =>
  authInstance
    .get<UserPrivilegeResponse>(LOGIN_SERVICE + `api/v1/user-permission/user/${userId}/serviceRole/${serviceRoleId}`)
    .then(({data}) => data);

export const userPermissionUpdate = (payload: UserPrivilegePayload): Promise<UserPrivilegeResponse> =>
  authInstance.put<UserPrivilegeResponse>(LOGIN_SERVICE + `api/v1/user-permission`, payload).then(({data}) => data);

export interface UserPrivilegeResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}

export interface UserPrivilegePayload {
  id: any;
  userId: number;
  servicePermissionId: number;
  status: string;
}
