import React, {useState} from 'react';
import * as S from './PassportUpload.style';

const OtherGuestPassport = ({
  roomIndex,
  guestIndex,
  uploadImages,
  onMainGuestImageChange,
  savedImage,
}: // eslint-disable-next-line @typescript-eslint/no-explicit-any
any) => {
  const [imageChanged, setImageChanged] = useState(false);
  const keyValue = `${roomIndex}${guestIndex}`;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleImageChange = (event: any) => {
    onMainGuestImageChange(event, roomIndex, guestIndex);
    setImageChanged(true);
  };

  const handleFileInputClick = () => {
    document.getElementById(`passportImage-${roomIndex}${guestIndex}`)?.click();
  };

  return (
    <S.FileInputContainer>
      {uploadImages[keyValue] || savedImage ? (
        <S.UploadedImage
          src={uploadImages[keyValue] ? URL.createObjectURL(uploadImages[keyValue]) : savedImage}
          alt="Main Guest's Passport Image"
        />
      ) : (
        <S.UploadImageContainer>
          <S.UploadIcon />
          <S.UploadImageText>Select Image to upload</S.UploadImageText>
        </S.UploadImageContainer>
      )}
      <S.CustomPopover
        style={{padding: '0px'}}
        placement="leftTop"
        content={
          <img
            width={500}
            height="auto"
            src={uploadImages[roomIndex] ? URL.createObjectURL(uploadImages[roomIndex]) : savedImage}
          />
        }
        trigger="hover">
        <S.ZoomIcon />
      </S.CustomPopover>
      <S.FileInputLabel onClick={handleFileInputClick}>
        {uploadImages[keyValue] || savedImage ? 'Change Image' : 'Upload Image'}
      </S.FileInputLabel>
      <S.FileInput
        accept="image/*"
        type="file"
        id={`passportImage-${roomIndex}${guestIndex}`}
        onChange={event => handleImageChange(event)}
      />
      {/* {imageChanged && (
        <S.ImageStatusBox>
          <S.ImageStatus>Not Saved</S.ImageStatus>
        </S.ImageStatusBox>
      )} */}
    </S.FileInputContainer>
  );
};

export default OtherGuestPassport;
