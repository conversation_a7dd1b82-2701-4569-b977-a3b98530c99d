.header {
  background: #2ca062;
  color: white;
  padding: 16px;
  margin: -24px -24px 16px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.contestTitle {
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 5px;
  text-align: center;
  font-weight: bold;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-family: '<PERSON>ra', sans-serif;
}

.totalPriceLabel {
  font-size: 12px;
  color: #e0e0e0;
  text-align: center;
  margin-bottom: 6px;
  letter-spacing: 1.5px;
  font-weight: normal;
}

.priceAmount {
  font-size: 36px;
  font-weight: normal;
  color: white;
  text-align: center;
  margin-bottom: 12px;
  letter-spacing: 1px;
  font-family: 'Lora', sans-serif;
}

.standardPackage {
  font-size: 12px;
  color: #ffffff;
  text-align: center;
  letter-spacing: 1px;
  font-weight: normal;
  font-family: '<PERSON>ra', sans-serif;
}

.paymentMethodsContainer {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.cardDetailsContainer {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 24px;
}

.cardInfoRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.cardInfoLabel {
  font-size: 14px;
  color: #757575;
}

.cardInfoValue {
  color: #333;
  font-size: 14px;
}

.visaText {
  color: #bdbdbd;
  font-size: 14px;
}

.cardNumber {
  color: #333;
  margin-bottom: 16px;
}

.securePayment {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #757575;
  font-family: 'Lora', sans-serif;
  font-weight: normal;
}

.lockIcon {
  margin-right: 4px;
}

.payButton {
  background: #2ca062;
  color: white;
  height: 40px;
  width: 100%;
  font-weight: 500;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Lora', sans-serif;
  letter-spacing: 1px;
  font-size: 16px;
  margin-bottom: 8px;
}
