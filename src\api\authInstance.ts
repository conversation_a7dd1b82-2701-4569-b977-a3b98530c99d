import axios from 'axios';
import {API_KEY_ENV} from './instance';

const authInstance = axios.create({
  baseURL: `${process.env.NODE_ENV === 'production' ? API_KEY_ENV : process.env.REACT_APP_BASE_URL}:9080/`,
});

authInstance.interceptors.request.use((config: any) => {
  const token = localStorage.getItem('accessToken');
  if (token) {
    config.headers = {...config.headers};
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default authInstance;
