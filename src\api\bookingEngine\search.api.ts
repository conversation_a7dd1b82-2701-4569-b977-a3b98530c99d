import {ReservationResponse} from '@app/components/apps/roomFeed/interface';
import instance, {HOTEL_SERVICE} from '../instance';
import {IChangeRoomPayload} from '@app/pages/Hotel/RoomReservation/interface/interface';

export interface BookingRoomRequest {
  childCount: number;
  adultCount: number;
}

export interface SearchRequestPayload {
  checkInDate: string;
  checkOutDate: string;
  hotelId: string;
  resident: boolean;
  channelId: number;
  hotelType: string;
  bookingRoomRequestList: BookingRoomRequest[];
}

export interface SearchResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export const getSearchByHotelId = (payload: SearchRequestPayload): Promise<SearchResponse> => {
  return instance.put<SearchResponse>(`${HOTEL_SERVICE}reservation/bookings/search`, payload).then(({data}) => data);
};

export const getAvailableWebRoomsById = (
  id: number | undefined,
  checkedIn: string,
  checkOutDate: string,
  roomId: number,
): Promise<ReservationResponse> =>
  instance
    .get<ReservationResponse>(
      HOTEL_SERVICE +
        `reserved-room/available-room-web/${id}?checkInDate=${checkedIn}&checkOutDate=${checkOutDate}&roomId=${roomId}
      `,
    )
    .then(({data}) => data);

export const updateRoomWeb = (payload: IChangeRoomPayload): Promise<ReservationResponse> =>
  instance.put<ReservationResponse>(HOTEL_SERVICE + `reserved-room/change-room-web`, payload).then(({data}) => data);
