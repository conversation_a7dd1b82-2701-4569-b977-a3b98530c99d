import inventoryInstance, {INVENTORY_SERVICE} from '@app/api/inventoryInstance';

export const CreateStock = (payload: CreateHotelStockProps): Promise<StockResponse> => {
  return inventoryInstance.post<StockResponse>(INVENTORY_SERVICE + 'opening-stock', payload).then(({data}) => data);
};
export const createGroupInventoryStock = (payload: CreateGroupStockProps): Promise<StockResponse> => {
  return inventoryInstance
    .post<StockResponse>(INVENTORY_SERVICE + 'group-opening-stock', payload)
    .then(({data}) => data);
};

export const UpdateStock = (payload: UpdateHotelStockProps): Promise<StockResponse> => {
  return inventoryInstance.put<StockResponse>(INVENTORY_SERVICE + 'opening-stock', payload).then(({data}) => data);
};

export const updateGroupInventoryStock = (payload: UpdateGroupStockProps): Promise<StockResponse> => {
  return inventoryInstance
    .put<StockResponse>(INVENTORY_SERVICE + 'group-opening-stock', payload)
    .then(({data}) => data);
};

export const getAllStocksByHotelId = (
  groupId: number,
  hotelId: number,
  inventoryServiceId: number | undefined,
  {itemName, unitName}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<StockResponse> =>
  inventoryInstance
    .get<StockResponse>(
      INVENTORY_SERVICE +
        `opening-stock/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&itemName=${
          itemName ? itemName : ''
        }&unitName=${
          unitName ? unitName : ''
        }&groupId=${groupId}&hotelId=${hotelId}&inventoryServiceId=${inventoryServiceId}`,
    )
    .then(({data}) => data);

export const getAllGroupStocks = (
  groupId: number,
  inventoryServiceId: number | undefined,
  {itemName, unitName}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<StockResponse> =>
  inventoryInstance
    .get<StockResponse>(
      INVENTORY_SERVICE +
        `group-opening-stock/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&itemName=${
          itemName ? itemName : ''
        }&unitName=${unitName ? unitName : ''}&groupId=${groupId}&groupInventoryServiceId=${inventoryServiceId}`,
    )
    .then(({data}) => data);

export const DeleteStock = (id: number): Promise<StockResponse> =>
  inventoryInstance.delete<StockResponse>(INVENTORY_SERVICE + `opening-stock/${id}`).then(({data}) => data);

export const DeleteGroupStock = (id: number): Promise<StockResponse> =>
  inventoryInstance.delete<StockResponse>(INVENTORY_SERVICE + `group-opening-stock/${id}`).then(({data}) => data);

export const getAllItems = (
  groupId: number,
  groupInventoryServiceId: number | undefined,
  name?: string,
): Promise<StockResponse> =>
  inventoryInstance
    .get<StockResponse>(
      INVENTORY_SERVICE +
        `items/search?name=${name ? name : ''}&groupId=${groupId}&groupInventoryServiceId=${groupInventoryServiceId}`,
    )
    .then(({data}) => data);

export const getAllHotelItems = (
  hotelId: number,
  groupInventoryServiceId: number | undefined,
  name?: string,
): Promise<StockResponse> =>
  inventoryInstance
    .get<StockResponse>(
      INVENTORY_SERVICE +
        `items/hotel/search?name=${
          name ? name : ''
        }&hotelId=${hotelId}&groupInventoryServiceId=${groupInventoryServiceId}`,
    )
    .then(({data}) => data);

export const getAllLocations = (): Promise<StockResponse> =>
  inventoryInstance.get<StockResponse>(INVENTORY_SERVICE + `location`).then(({data}) => data);

export interface CreateHotelStockProps {
  date: string;
  itemId: string;
  expireDate: string;
  quantity: number;
  hotelId: number | string;
  inventoryServiceId: number | undefined;
  groupId: number | undefined;
  reorderLevel: number;
}

export interface CreateGroupStockProps {
  date: string;
  itemId: string;
  expireDate: string;
  quantity: number;
  groupId: number;
  groupInventoryServiceId: number | undefined;
  reorderLevel: number;
}

export interface UpdateHotelStockProps {
  id: number;
  date: string;
  itemId: string;
  expireDate: string;
  quantity: number;
  hotelId: number | string;
  inventoryServiceId: number | undefined;
  groupId: number;
  reorderLevel: number;
}

export interface UpdateGroupStockProps {
  id: number;
  date: string;
  itemId: string;
  expireDate: string;
  quantity: number;
  groupId: number;
  groupInventoryServiceId: number | undefined;
  reorderLevel: number;
}

export interface StockResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  itemName: string;
  unitName: string;
}
