/* Modal Styles */
.modal-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 8px;
}

.modal-button {
  margin-left: 8px;
}

/* Card Styles */
.card {
  width: 100%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s;
}

.card:hover {
  transform: scale(1.02);
}

.card-header {
  background-color: #f0f0f0;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.card-title {
  margin: 0;
  color: #333;
  font-size: small;
}

.card-content {
  padding: 16px;
}

.card-content-refundAmount {
  padding: 1px;
}
/* Receipt Details Styles */
.receipt-detail {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.receipt-label {
  display: flex;
  align-items: center;
}

.receipt-label span {
  font-weight: bold;
  font-size: xx-small;
}

.receipt-label-refundAmount {
  font-weight: bold;
  font-size: 12px;
  color: #334a7c;
}

.receipt-label-refundAmount-color {
  font-weight: bold;
  font-size: 12px;
  color: #ffffff;
}

.receipt-value {
  font-weight: bold;
  color: #4caf50; /* Green */
}

.receipt-value-refundAmount {
  font-weight: 500;
  font-size: smaller;
  color: #334a7c;
}

.receipt-value-refundAmount-color {
  font-weight: 500;
  font-size: smaller;
  color: #ffffff;
}

/* Icon Styles */
.icon {
  margin-right: 8px;
}

.icon-money {
  color: #2196f3; /* Blue */
}

.icon-calendar {
  color: #ff9800; /* Orange */
}

.icon-pdf {
  font-size: 24px;
  color: #4caf50; /* Green */
}
