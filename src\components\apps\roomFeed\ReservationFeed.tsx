import React, {useEffect, useState} from 'react';
import {Empty} from 'antd';
import {AvailableRoomCard} from '@app/components/common/ArticleCard/AvailableRoomCard';
import {Feed} from '@app/components/common/Feed/Feed';
import {RoomFilter} from '@app/components/apps/roomFeed/RoomFilter/RoomFilter';
import * as S from './RoomFilter/RoomFilter.styles';
import {NodeExpandOutlined} from '@ant-design/icons';
import {AvailableRoomCardProps, FilterProps, Props} from './interface';
import {getAllRoomTypes} from '@app/api/hotel/roomType.api';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {fetchReservation} from '@app/store/slices/reservationSlice';
import {isEmpty} from 'lodash';
import {Result} from '@app/components/common/Result/Result';

export const ReservationFeed: React.FC<Props> = ({handleReservation, handleViewRoomDetails, handlePressRoomStatus}) => {
  const dispatch = useAppDispatch();

  const [hasMore] = useState<boolean>(true);
  const [loaded, setLoaded] = useState<boolean>(false);
  const [selectedType, setselectedType] = useState('');
  const [roomTypes, setroomTypes] = useState<Array<{title: string; value: string}>>([]);

  const {hotelId} = useAppSelector(state => state.hotelSlice.hotelConfig);
  const {pagination} = useAppSelector(state => state.reservationSlice);
  const {selectedRooms, checkedDate} = useAppSelector(state => state.reservationSlice);

  const next = () => {
    // getNews().then(newNews => setNews(news.concat(newNews)));
  };

  const listRoomTypes = async () => {
    try {
      const result = await getAllRoomTypes(hotelId, '', 100, 0);
      const dataList =
        result?.result?.roomType?.map((type: {roomTypeName: string; id: number}) => ({
          title: type.roomTypeName,
          value: type.id,
        })) || [];
      const allTypes = {title: 'All Types', value: ''};
      setroomTypes([allTypes, ...dataList]);
    } catch (error) {}
  };

  useEffect(() => {
    listRoomTypes();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleFilter = async (id: string) => {
    setselectedType(id);
    const filterPayload: FilterProps = {
      checkedIn: checkedDate?.checkedIn,
      checkedOut: checkedDate?.checkedOut,
      roomTypeId: id,
    };
    dispatch(fetchReservation({hotelId, filterPayload}));
  };

  const isMakeReservationDisable = () => {
    return isEmpty(selectedRooms) ? true : false;
  };

  return (
    <RoomFilter handlePressRoomStatus={handlePressRoomStatus}>
      {({reservation}) =>
        reservation?.length || !loaded ? (
          <Feed next={next} hasMore={hasMore}>
            <S.HeaderWrapper>
              <S.HeaderLeftWrapper>
                <S.Header>Available Rooms</S.Header>
                <S.Info>
                  Here available rooms showing based on {checkedDate?.checkedIn} to {checkedDate?.checkedOut}
                </S.Info>
                <S.SubHeader>{pagination?.totalRecords} Rooms Available</S.SubHeader>
              </S.HeaderLeftWrapper>
              <S.AffixButton disabled={isMakeReservationDisable()} size="small" type="primary">
                <S.BtnLabel onClick={handleReservation}>Make Reservation</S.BtnLabel>
                <NodeExpandOutlined />
              </S.AffixButton>
            </S.HeaderWrapper>
            <S.RoomTypeFilterRow>
              {roomTypes.map((room, idx) => {
                return (
                  <S.RoomTypeFilterOutline
                    onClick={() => handleFilter(room.value)}
                    selected={selectedType === room.value}
                    key={idx}>
                    <S.RoomTypeFilterLabel>{room.title}</S.RoomTypeFilterLabel>
                  </S.RoomTypeFilterOutline>
                );
              })}
            </S.RoomTypeFilterRow>
            {isEmpty(reservation) ? (
              <Result status="404" title="404" subTitle="Sorry, the page you visited does not exist." />
            ) : (
              reservation.map((room: AvailableRoomCardProps, index: number) => (
                <AvailableRoomCard
                  key={index}
                  amount={(room.stayTypeWithRoomPriceResponse && room.stayTypeWithRoomPriceResponse[0]?.roomPrice) ?? 0}
                  stayTypes={room.stayTypeWithRoomPriceResponse}
                  description={room.amenities}
                  imgUrl={room.roomImageUrl[0]}
                  name={room.roomName}
                  view={room.viewTypeName}
                  handleReservation={handleReservation}
                  handleViewRoomDetails={handleViewRoomDetails}
                  roomId={room.roomId}
                  room={room}
                />
              ))
            )}
          </Feed>
        ) : (
          <Empty />
        )
      }
    </RoomFilter>
  );
};
