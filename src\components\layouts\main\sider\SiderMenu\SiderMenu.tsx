/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {Link, useLocation} from 'react-router-dom';
import * as S from './SiderMenu.styles';
import {sidebarNavigation, SidebarNavigationItem} from '../sidebarNavigation';
import {
  GroupInventorySidebarNavigation,
  HotelHouseKeepingInventorySidebarNavigation,
  HotelInventorySidebarNavigation,
} from '../InventorySidebarNavigation';
import {RestaurantSidebarNavigation} from '../RestaurantSidebarNavigation';
import {GroupAdminSidebarNavigation} from '../GroupAdminSidebarNavigation';
import {HotelAdminSidebarNavigation} from '../HotelAdminSidebarNavigation';
import {InventoryAdminSidebarNavigation} from '../InventoryAdminSidebarNavigation';
import {useAppSelector} from '@app/hooks/reduxHooks';
import {HOTEL_SERVICE_MODULE_NAME, modulePermission} from '@app/utils/permissions';
import {HouseKeepingSidebarNavigation} from '../HouseKeepingSidebarNavigation';
import {ServiceAdminSidebarNavigation} from '../ServiceAdminSidebarNavigation';
import {remove} from 'lodash';

interface SiderContentProps {
  setCollapsed: (isCollapsed: boolean) => void;
  expandMenu: (isExpand: boolean, navKey: string) => void;
}

const sideNavBar = (navigation: SidebarNavigationItem[]) => {
  const sidebarNavFlat = navigation.reduce(
    (result: SidebarNavigationItem[], current) =>
      result.concat(current.children && current.children.length > 0 ? current.children : current),
    [],
  );

  return sidebarNavFlat;
};

const findMenuItemByPathMatch = (
  items: SidebarNavigationItem[],
  currentPath: string,
): SidebarNavigationItem | undefined => {
  // First try to find exact match
  const exactMatch = sideNavBar(items).find(item => item.url === currentPath);
  if (exactMatch) return exactMatch;

  // If no exact match, find items by base path
  for (const item of items) {
    if (item.children) {
      // Check if any child's base path matches the current path
      for (const child of item.children) {
        if (child.url) {
          const basePath = child.url.split('/')[1]; // Get the base path segment
          if (currentPath.includes(`/${basePath}/`)) {
            return child;
          }
        }
      }
    }
  }
  return undefined;
};

const findParentKey = (items: SidebarNavigationItem[], childKey: string): string | undefined => {
  for (const item of items) {
    if (item.children && item.children.some(child => child.key === childKey)) {
      return item.key;
    }
  }
  return undefined;
};

const SiderMenu: React.FC<SiderContentProps> = ({setCollapsed, expandMenu}) => {
  const userPermission = useAppSelector(state => state.user.permissions);
  const {hotelType, hotelId} = useAppSelector(state => state.hotelSlice.hotelConfig);
  const {serviceType} = useAppSelector(state => state.serviceSlice.serviceConfig);
  const {t} = useTranslation();
  const location = useLocation();

  const [selectedKeys, setSelectedKeys] = React.useState<string[]>([]);
  const [openKeys, setOpenKeys] = React.useState<string[]>([]);

  const path: string = window.location.pathname;
  const splitPath = path.split('/');
  const mainPath: string = splitPath && splitPath[1] ? splitPath[1] : path;

  const getSubmenu = (isSubMenu: any, nav: SidebarNavigationItem) =>
    isSubMenu &&
    nav.children &&
    nav.children
      .map(childNav => {
        const permission = modulePermission(userPermission, childNav.key);

        if (permission.VIEW)
          return {
            key: childNav.key,
            label: <Link to={childNav.url || ''}>{t(childNav.title)}</Link>,
            title: t(childNav.title),
            isSubMenu: childNav.isSubMenu,
            url: childNav.url,
          };
      })
      .filter(Boolean);

  const getHotelSidebarNavigation = () => {
    const defaultSideBar: SidebarNavigationItem[] = [...sidebarNavigation];
    hotelType === 'HOTEL' && remove(defaultSideBar, nav => nav.key === HOTEL_SERVICE_MODULE_NAME.EVENT);
    // const filterdPermitedMenu = filter(defaultSideBar, o => o.key === 'invoice');
    // hotelType === 'HOTEL' &&
    //   remove(filterdPermitedMenu[0]?.children, nav => nav.key === HOTEL_SERVICE_MODULE_NAME.EVENT_INVOICE);

    return defaultSideBar;
  };

  const hotelInventorySidebar =
    serviceType === 'HOUSEKEEPING' ? HotelHouseKeepingInventorySidebarNavigation : HotelInventorySidebarNavigation;

  const pageNavigation: SidebarNavigationItem[] =
    mainPath === 'inventory'
      ? hotelId === 0
        ? GroupInventorySidebarNavigation
        : hotelInventorySidebar
      : mainPath === 'inventory-admin'
      ? InventoryAdminSidebarNavigation
      : mainPath === 'restaurant-admin'
      ? RestaurantSidebarNavigation
      : mainPath === 'group-admin'
      ? GroupAdminSidebarNavigation
      : mainPath === 'hotel-admin'
      ? HotelAdminSidebarNavigation
      : mainPath === 'housekeeping'
      ? HouseKeepingSidebarNavigation
      : mainPath === 'service-admin'
      ? ServiceAdminSidebarNavigation
      : getHotelSidebarNavigation();

  useEffect(() => {
    const path = location.pathname;
    const currentMenuItem = findMenuItemByPathMatch(pageNavigation, path);
    const newSelectedKeys = currentMenuItem ? [currentMenuItem.key] : [];

    // Find parent key if it exists
    const parentKey = currentMenuItem ? findParentKey(pageNavigation, currentMenuItem.key) : undefined;
    const newOpenKeys = parentKey ? [parentKey] : [];

    setSelectedKeys(newSelectedKeys);
    if (newOpenKeys.length > 0) {
      setOpenKeys(newOpenKeys);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  const permittedMenu: SidebarNavigationItem[] = pageNavigation
    .map((nav: SidebarNavigationItem) => {
      const isSubMenu = !!nav.children?.length;
      const permission = modulePermission(userPermission, nav.key);

      if (permission.VIEW) {
        return {
          key: nav.key,
          title: t(nav.title),
          label: isSubMenu ? t(nav.title) : <Link to={nav.url || ''}>{t(nav.title)}</Link>,
          icon: nav.icon,
          url: nav.url,
          isSubMenu: isSubMenu,
          children: getSubmenu(isSubMenu, nav),
        } as SidebarNavigationItem; // Explicitly cast to SidebarNavigationItem
      }
      return null; // Ensure the map always returns a valid type
    })
    .filter((item): item is SidebarNavigationItem => item !== null); // Type guard to remove null values

  return (
    <S.Menu
      mode="inline"
      selectedKeys={selectedKeys}
      openKeys={openKeys}
      onOpenChange={keys => setOpenKeys(keys)}
      onClick={() => setCollapsed(true)}
      items={permittedMenu.map(nav => {
        const isSubMenu = nav.children?.length;
        const isValidNav = nav.isSubMenu && nav.children?.length === 0 ? false : true;

        if (isValidNav) {
          return {
            key: nav.key,
            title: t(nav.title),
            onTitleClick: () => {
              expandMenu && expandMenu(true, nav.key);
            },
            label: isSubMenu ? t(nav.title) : <Link to={nav.url || ''}>{t(nav.title)}</Link>,
            icon: nav.icon,
            children:
              isSubMenu &&
              nav.children &&
              nav.children !== undefined &&
              nav.children.map(childNav => ({
                key: childNav.key,
                label: <Link to={childNav.url || ''}>{t(childNav.title)}</Link>,
                title: t(childNav.title),
              })),
          };
        }
        return null;
      })}
    />
  );
};

export default SiderMenu;
