/* eslint-disable @typescript-eslint/no-explicit-any */
import instance, {HOTEL_SERVICE} from '@app/api/instance';

export const createChannelPrice = (payload: CreateChannelPriceProps): Promise<ChannelPriceResponse> => {
  return instance.post<ChannelPriceResponse>(HOTEL_SERVICE + 'channel-price', payload).then(({data}) => data);
};

export const updateChannelPrice = (payload: UpdateChannelPriceProps[]): Promise<ChannelPriceResponse> => {
  return instance.put<ChannelPriceResponse>(HOTEL_SERVICE + 'channel-prices', payload).then(({data}) => data);
};
export const updateSingleChannelPrice = (payload: UpdateChannelPriceProps): Promise<ChannelPriceResponse> => {
  return instance.put<ChannelPriceResponse>(HOTEL_SERVICE + 'channel-price', payload).then(({data}) => data);
};

export const getAllChannelPrices = (
  hotelId: number,
  {ageRange, channelName, currencyPrefix, roomTypeName, stayTypeName}: FilterProps,
  pageSize: number | undefined,
  current: number | undefined,
): Promise<ChannelPriceResponse> => {
  return instance
    .get<ChannelPriceResponse>(
      HOTEL_SERVICE +
        `channel-price/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&roomType=${
          roomTypeName ? roomTypeName : ''
        }&stayType=${stayTypeName ? stayTypeName : ''}&currencyPrefix=${
          currencyPrefix ? currencyPrefix : ''
        }&ageRange=${ageRange ? ageRange : ''}&channelPrice=&channelId=&channelName=${
          channelName ? channelName : ''
        }&hotelId=${hotelId}`,
    )
    .then(({data}) => data);
};

export const getChannelPricesByChannelId = (
  hotelId: number,
  {ageRange, channelName, currencyPrefix, roomTypeName, stayTypeName}: FilterProps,
  pageSize: number | undefined,
  current: number | undefined,
  channelId: string,
): Promise<ChannelPriceResponse> => {
  return instance
    .get<ChannelPriceResponse>(
      HOTEL_SERVICE + `channel-price/room-price/search?channelId=${channelId}&hotelId=${hotelId}`,
    )
    .then(({data}) => data);
};

export const getSeasonalPricesByChannelId = (
  hotelId: number,
  {ageRange, channelName, currencyPrefix, roomTypeName, stayTypeName}: FilterProps,
  pageSize: number | undefined,
  current: number | undefined,
  seasonId: number,
  channelId: number,
): Promise<ChannelPriceResponse> => {
  return instance
    .get<ChannelPriceResponse>(
      HOTEL_SERVICE + `seasonalPrice/room-price/search?channelId=${channelId}&seasonId=${seasonId}&hotelId=${hotelId}`,
    )
    .then(({data}) => data);
};

export const deleteChannelPrice = (id: number): Promise<ChannelPriceResponse> =>
  instance.delete<ChannelPriceResponse>(HOTEL_SERVICE + `channel-price/${id}`).then(({data}) => data);

export const updateDiscountPrice = (payload: UpdateDiscountPriceProps): Promise<ChannelPriceResponse> => {
  return instance.put<ChannelPriceResponse>(HOTEL_SERVICE + 'channel-price-discount', payload).then(({data}) => data);
};
export const updateSeasonalPrice = (payload: UpdateSeasonalPriceProps): Promise<ChannelPriceResponse> => {
  return instance.put<ChannelPriceResponse>(HOTEL_SERVICE + 'seasonalPrice', payload).then(({data}) => data);
};

export const getDayPricesByChannelId = (
  hotelId: number,
  channelId: string,
  startDate: string,
  endDate: string,
): Promise<ChannelPriceResponse> => {
  return instance
    .get<ChannelPriceResponse>(
      HOTEL_SERVICE +
        `dayPrice/search?channelId=${channelId}&hotelId=${hotelId}&startDate=${startDate}&endDate=${endDate}`,
    )
    .then(({data}) => data);
};

export const updateDayPrice = (payload: UpdateDayPriceProps): Promise<ChannelPriceResponse> => {
  return instance.put<ChannelPriceResponse>(HOTEL_SERVICE + 'dayPrice', payload).then(({data}) => data);
};

export interface CreateChannelPriceProps {
  price: string;
  channelId: number;
  updateTourOperatorPrices: boolean;
  currencyId: number;
  roomTypeId: number;
  stayTypeId: number;
  childPolicyId: number;
}

export interface UpdateChannelPriceProps {
  id: number;
  price: string;
  channelId: number;
  updateTourOperatorPrices: boolean;
  currencyId: number;
  roomTypeId: number;
  stayTypeId: number;
  childPolicyId: number;
}
export interface UpdateDayPriceProps {
  id: number;
  lkrPrice: string;
  usdPrice: string;
  stayTypeId: number;
  channelId: number;
  date: string;
}
export interface UpdateSeasonalPriceProps {
  id: number;
  seasonalLKRprice: number;
  seasonalUSDRprice: number;
}

export interface UpdateDiscountPriceProps {
  channelId: number;
  priceType: 'CHANNEL_PRICE' | 'SEASOANL_PRICE' | 'DAY_PRICE';
  discountField: 'LKR_PRICE' | 'USD_PRICE';
  discountType: 'PERCENTAGE' | 'AMOUNT';
  discountAmountType?: 'INCREASE' | 'DECREASE';
  stayTypeId?: number;
  discount?: number;
  allChannel?: boolean;
  reservationTypeId?: number;
  seasonId?: number;
  hotelId?: number;
  channelIdList: number[];
  stayTypeIdList: number[];
  startDateList?: string[];
  applicableForReservationTypes?: boolean;
  reservationTypeIdList?: number[];
}

export interface ChannelPriceResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface FilterProps {
  roomTypeName: string;
  stayTypeName: string;
  currencyPrefix: string;
  ageRange: string;
  channelName: string;
}
