/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {FC, useEffect, useState} from 'react';
import * as S from './PriceCalculator.style';
import {isEmpty} from 'lodash';
import {BsCashStack} from 'react-icons/bs';
import {BASE_COLORS} from '@app/styles/themes/constants';
import {convertNumberFormatWithDecimal} from '@app/utils/utils';
import {calculateTotalTaxWithSequence} from '@app/utils/functions';
import {Col, Divider, Row} from 'antd';

interface Props {
  currency: string;
  totalAmount: number;
  isVatApplicable: boolean;
  taxList: any[];
  outStanding?: number;
  totalAmountExcExtraHour: number;
}

const RetrivePriceCalculator: FC<Props> = ({
  currency,
  totalAmount,
  isVatApplicable,
  taxList,
  outStanding,
  totalAmountExcExtraHour,
}) => {
  const [dueAmount, setDueAmount] = useState<number>(0);

  useEffect(() => {
    const taxDetails = calculateTotalTaxWithSequence(totalAmount, taxList, isVatApplicable);
    const taxDetailsForExcExtraHour = calculateTotalTaxWithSequence(totalAmountExcExtraHour, taxList, isVatApplicable);
    const totalWithTax = totalAmount + taxDetails.totalTaxAmount;
    const totalExcExtraHourWithTax = totalAmountExcExtraHour + taxDetailsForExcExtraHour.totalTaxAmount;
    const calculatedDueAmount = totalWithTax - totalExcExtraHourWithTax + Number(outStanding);
    setDueAmount(calculatedDueAmount);
  }, [totalAmount, totalAmountExcExtraHour, outStanding, taxList, isVatApplicable]);

  const taxDetails = calculateTotalTaxWithSequence(totalAmount, taxList, isVatApplicable);
  const taxDetailsForExcExtraHour = calculateTotalTaxWithSequence(totalAmountExcExtraHour, taxList, isVatApplicable);
  const taxArray = taxDetails?.taxArray;
  const taxAmount = taxDetails?.totalTaxAmount;

  return (
    <S.BlurCardWrapper>
      <S.BlueCard>
        <S.Padding>
          <S.BlueCard>
            <S.Padding>
              {outStanding ? (
                <S.PriceWrapper>
                  <S.DuePaymentTitle>Due Payment</S.DuePaymentTitle>
                  <S.DuePaymentTitle>{`${currency} ${convertNumberFormatWithDecimal(dueAmount, 2)}`}</S.DuePaymentTitle>
                </S.PriceWrapper>
              ) : null}
            </S.Padding>
          </S.BlueCard>
          <Divider>Price Summary Exc. Extra Hours</Divider>
          <Row justify="space-between" gutter={[20, 20]}>
            <Col xl={12}>
              <S.TotalPriceTitle>Total Amount</S.TotalPriceTitle>
            </Col>
            <Col xl={12}>
              <Row justify="end">
                <S.TotalPriceTitle>{`${currency} ${convertNumberFormatWithDecimal(
                  taxDetailsForExcExtraHour.totalTaxAmount + totalAmountExcExtraHour,
                  2,
                )}`}</S.TotalPriceTitle>
              </Row>
              <Row justify="end" align="middle">
                <S.TotalPriceMiniTitle>{`${convertNumberFormatWithDecimal(
                  totalAmountExcExtraHour,
                  2,
                )}`}</S.TotalPriceMiniTitle>
                &nbsp; + &nbsp;
                <S.TotalPriceMiniTitle>{`${convertNumberFormatWithDecimal(
                  taxDetailsForExcExtraHour.totalTaxAmount,
                  2,
                )}`}</S.TotalPriceMiniTitle>
              </Row>
            </Col>
          </Row>
          <Divider>Price Summary Inc. Extra Hours</Divider>
          <Row gutter={[20, 20]}>
            <Col xl={12}>
              <S.TotalPriceTitle>Total Amount</S.TotalPriceTitle>
            </Col>
            <Col xl={12}>
              <Row justify="end">
                <S.TotalPriceTitle>{`${currency} ${convertNumberFormatWithDecimal(
                  taxAmount + totalAmount,
                  2,
                )}`}</S.TotalPriceTitle>
              </Row>
              <Row align="middle" justify="end">
                <S.TotalPriceMiniTitle>{`${convertNumberFormatWithDecimal(totalAmount, 2)}`}</S.TotalPriceMiniTitle>
                &nbsp; + &nbsp;
                <S.TotalPriceMiniTitle>{`${convertNumberFormatWithDecimal(taxAmount, 2)}`}</S.TotalPriceMiniTitle>
              </Row>
            </Col>
          </Row>

          {!isEmpty(taxList) ? (
            <S.TaxInformationWapper>
              <S.TaxRightWrapper>
                <S.TaxInfoText>{`Excludes ${currency} ${convertNumberFormatWithDecimal(
                  taxAmount,
                  2,
                )} in taxes and charges`}</S.TaxInfoText>
                <S.ListTaxWrapper>
                  {taxArray.map((tax, idx) => {
                    return (
                      <S.ListTaxRow key={`tax-list${idx}`}>
                        <S.TaxInfoText>{` ${tax.name}`}</S.TaxInfoText>
                        <S.TaxInfoText>{`${currency} ${convertNumberFormatWithDecimal(
                          tax.taxAmount,
                          2,
                        )}`}</S.TaxInfoText>
                      </S.ListTaxRow>
                    );
                  })}
                </S.ListTaxWrapper>
              </S.TaxRightWrapper>
            </S.TaxInformationWapper>
          ) : null}
        </S.Padding>
      </S.BlueCard>
    </S.BlurCardWrapper>
  );
};

export default RetrivePriceCalculator;
