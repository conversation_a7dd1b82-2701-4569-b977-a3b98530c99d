import instance, {HOTEL_SERVICE} from '@app/api/instance';

// export const CreateReservationType = (payload: CreateReservationTypeProps): Promise<ReservationTypeResponse> => {
//   return instance.post<ReservationTypeResponse>(HOTEL_SERVICE + 'reservation-type', payload).then(({data}) => data);
// };

export const CreateReservationType = (payload: CreateReservationTypeProps): Promise<ReservationTypeResponse> => {
  const reservertionTypeFormData = new FormData();
  reservertionTypeFormData.append('reservationType', JSON.stringify(payload.reservationType));
  reservertionTypeFormData.append('reservationTypeImage', payload.reservationTypeImage);
  return instance
    .post<ReservationTypeResponse>(HOTEL_SERVICE + 'reservation-types', reservertionTypeFormData)
    .then(({data}) => data);
};

export const UpdateReservationType = (payload: UpdateReservationTypeProps): Promise<ReservationTypeResponse> => {
  const reservertionTypeFormData = new FormData();
  reservertionTypeFormData.append('reservationType', JSON.stringify(payload.reservationType));
  reservertionTypeFormData.append('reservationTypeImage', payload.reservationTypeImage);
  return instance
    .put<ReservationTypeResponse>(HOTEL_SERVICE + 'reservation-type', reservertionTypeFormData)
    .then(({data}) => data);
};

export const getAllReservationTypes = (): Promise<ReservationTypeResponse> =>
  instance.get<ReservationTypeResponse>(HOTEL_SERVICE + 'reservation-type').then(({data}) => data);

export const DeleteReservationType = (id: number): Promise<ReservationTypeResponse> =>
  instance.delete<ReservationTypeResponse>(HOTEL_SERVICE + `reservation-type/${id}`).then(({data}) => data);

export const getAllHolidays = (id: number): Promise<ReservationTypeResponse> =>
  instance.get<ReservationTypeResponse>(HOTEL_SERVICE + `holiday-config/hotel/${id}`).then(({data}) => data);

export const createHolidayConfig = (payload: CreateHolidayProps): Promise<ReservationTypeResponse> =>
  instance.post<ReservationTypeResponse>(HOTEL_SERVICE + `holiday-config`, payload).then(({data}) => data);

export const deleteHoliday = (id: number): Promise<ReservationTypeResponse> =>
  instance.delete<ReservationTypeResponse>(HOTEL_SERVICE + `holiday-config/${id}`).then(({data}) => data);

export interface CreateReservationTypeProps {
  reservationType: {name: string; notifyColor: string; description: string; onlineBooking: boolean};
  reservationTypeImage: any;
}

export interface CreateHolidayProps {
  id: number | null;
  name: string;
  description: string;
  date: string;
  hotelId: number;
}

export interface UpdateReservationTypeProps {
  reservationType: {id: number; name: string; notifyColor: string; description: string; onlineBooking: boolean};
  reservationTypeImage: any;
}

export interface ReservationTypeResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}
