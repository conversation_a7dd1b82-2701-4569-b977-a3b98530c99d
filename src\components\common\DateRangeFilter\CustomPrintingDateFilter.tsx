import React, { useState } from 'react';
import * as S from './DateRangeFilter.style';
import { Button, DatePicker, Radio, RadioChangeEvent, Select } from 'antd';
import moment from 'moment';
import { DatePickerProps, RangePickerProps } from 'antd/lib/date-picker';
import { log } from 'handlebars';

const { RangePicker } = DatePicker;

interface IDateRange {
    startDate: string;
    endDate: string;
}

interface IPropsFilter {
    getDateRange: (range: IDateRange) => void;
    getSelectedDateType: any;
    getSelectedType: any;
    selectedType: 'SINGLE' | 'RANGE';
    selectedDatetypepoint: string;
}

function CustomPrintingDateFilter(props: IPropsFilter) {
    const { getDateRange, getSelectedType, getSelectedDateType, selectedType: initialSelectedType, selectedDatetypepoint } = props;
    const [dateType, setDateType] = useState<any>(selectedDatetypepoint);
    const [singleDatePicker, setSingleDatePicker] = useState<moment.Moment | null>(moment());
    const [rangeDatePicker, setRangeDatePicker] = useState<[moment.Moment, moment.Moment] | [null, null]>([null, null]);
    const [selectedDateRange, setSelectedDateRange] = useState<IDateRange>({
        startDate: '',
        endDate: '',
    });
    console.log('selectedType:----05223212', initialSelectedType);

    const [selectDateType, setSelectDateType] = useState<'SINGLE' | 'RANGE' | undefined>('SINGLE');
    const [isAllDate, setIsAllDate] = useState(false);
    const isSelectedTypeInitialized = React.useRef(false);
    React.useEffect(() => {
        if (!isSelectedTypeInitialized.current) {
            setSelectDateType(initialSelectedType);
            isSelectedTypeInitialized.current = true;
        }
    }, [initialSelectedType]);

    const onChange = (e: RadioChangeEvent) => {
        setDateType(e.target.value);

        getSelectedDateType(e.target.value)
        setSingleDatePicker(null);
        setRangeDatePicker([null, null]);
        setSelectedDateRange({
            startDate: '',
            endDate: '',
        });
        setSelectDateType('SINGLE');
        if (e.target.value === 'all') {
            setIsAllDate(true);
        } else {
            setIsAllDate(false);
        }
    };

    const handleChangeDateSelectType = (value: 'SINGLE' | 'RANGE' | undefined) => {
        setSelectDateType(value);
        getSelectedType(value)

    };

    return (
        <S.DateSearchWrapper>
            <Radio.Group onChange={onChange} value={dateType} size="small">
                <Radio value={'date'}>Date</Radio>
                <Radio value={'month'}>Month</Radio>
                <Radio value={'year'}>Year</Radio>
            </Radio.Group>
            {dateType !== 'all' && (
                <Select
                    size="small"
                    placeholder="Type"
                    value={selectDateType}
                    style={{ width: 150 }}
                    onChange={handleChangeDateSelectType}
                    options={[
                        {
                            value: 'SINGLE',
                            label: `Single ${dateType}`,
                        },
                        {
                            value: 'RANGE',
                            label: `Range ${dateType}`,
                        },
                    ]}
                />
            )}
        </S.DateSearchWrapper>
    );
}

export default CustomPrintingDateFilter;
