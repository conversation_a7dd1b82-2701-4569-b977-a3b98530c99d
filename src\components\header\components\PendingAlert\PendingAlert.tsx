import React from 'react';
import {useNavigate} from 'react-router-dom';
import {BAR_CHART} from '../../../../assets';
import {BASE_COLORS} from '@app/styles/themes/constants';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {Tooltip} from 'antd';
import './PendingAlert.Style.css';
import {getPendingResStatus, setPendingAlertOpen} from '@app/store/slices/reservationSlice';

export const PendingAlert: React.FC = () => {
  const hotelServiceConfig = useAppSelector(state => state.hotelSlice.hotelServiceConfig);
  const hotelConfig = useAppSelector(state => state.hotelSlice.hotelConfig);
  const pendingStaus = useAppSelector(state => state.reservationSlice.isPendingStatus);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  return (
    <Tooltip color="white" title={<div style={{color: 'black', fontSize: '12px'}}>Pending Reservation</div>}>
      <div
        hidden={hotelServiceConfig.serviceType === 'HOTEL' && pendingStaus ? false : true}
        style={{
          borderRadius: '50%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: '3px',
          marginRight: '0.6rem',
          cursor: 'pointer',
        }}>
        <button
          onClick={async () => {
            dispatch(setPendingAlertOpen(true));
          }}
          className="button-alert"></button>
      </div>
    </Tooltip>
  );
};
