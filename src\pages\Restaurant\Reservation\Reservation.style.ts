import styled from 'styled-components';
import Button from 'antd/es/button';

interface HeaderProps {
  bgImage: string;
}

interface InfoCardProps {
  bgImage: string;
}

interface ModalProps {
  isOpen: boolean;
}

interface ModalContentProps {
  bgImage: string;
}

// Header Styled Components
export const Header = styled.header<HeaderProps>`
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url(${props => props.bgImage});
  background-size: cover;
  background-position: center;
`;

export const Navigation = styled.nav`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  box-sizing: border-box;
`;

export const NavLinks = styled.div`
  display: flex;
  gap: 1.5rem;
`;

export const NavLink = styled.div`
  color: white;
  text-decoration: none;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 1px;
  font-weight: 500;
  cursor: pointer;
  position: relative;
  display: inline-block;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 0;
    height: 2px;
    background-color: #c8935f;
    transition: width 0.3s ease-in-out;
  }

  &:hover::after {
    width: 100%;
  }
`;

export const ReservationButton = styled(Button)`
  color: white;
  background: none;
  border: 1px solid white;
  padding: 0.75rem 1.5rem;
  text-decoration: none;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 1px;
  font-weight: 500;
  transition: all 0.3s ease;
  &:hover {
    background-color: white;
    color: black;
    border: 1px solid white;
    height: 45px;
  }
`;

export const Title = styled.h1`
  font-size: 4rem;
  margin-bottom: 1rem;
  font-weight: 300;
  text-transform: uppercase;
  letter-spacing: 8px;
  color: white;
`;

export const Divider = styled.div`
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin: 1rem 0;
`;

export const Star = styled.span`
  color: #cfb53b;
  font-size: 1rem;
`;

export const HeaderText = styled.p`
  max-width: 800px;
  margin: 0 auto;
  font-size: 1rem;
  line-height: 1.6;
`;

// Info Section Styled Components
export const InfoSection = styled.section`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  background-color: #0e0d0a;
  color: white;
  width: 100%;
  box-sizing: border-box;
  gap: 50px;
  padding: 110px 30px 30px 30px;

  @media (max-width: 1024px) {
    gap: 20px;
  }

  @media (max-width: 834px) {
    grid-template-columns: repeat(2, 1fr);
    padding-top: 30px;
    & > :nth-child(3) {
      grid-column: span 2;
      justify-self: center;
      margin-top: 20px;
    }
  }
`;

export const InfoCard = styled.div<InfoCardProps>`
  padding: 3rem 2rem;
  border: 1px solid #c8935f;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  background-color: #0e0d0a;
  min-height: 580px;
  width: 350px;

  &:hover {
    background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url(${props => props.bgImage});
    background-size: cover;
    background-position: center;
  }

  @media (max-width: 1024px) {
    min-height: 500px;
    width: 300px;
  }
`;

export const CardTitle = styled.h2`
  color: #c8935f;
  font-size: 1.5rem;
  margin-bottom: 2rem;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 2px;
`;

export const AddressText = styled.p`
  margin: 0.25rem 0;
  font-size: 0.9rem;
  line-height: 1.6;
  text-align: center;
`;

export const DirectionsButton = styled.button`
  background: none;
  border: none;
  color: #ffff;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
  padding: 0.5rem;
  margin-top: 1.5rem;
  cursor: pointer;
  position: relative;

  &:after {
    content: '';
    position: absolute;
    width: 95%;
    height: 1px;
    background: #c8935f;
    bottom: 0;
    right: 0;
    transition: width 0.3s ease-out;
  }

  &:hover {
    opacity: 0.8;

    &::after {
      width: 0%;
    }
  }
`;

// Reservation Form Styled Components
export const ReservationForm = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
`;

export const FormTitle = styled.h3`
  color: #c8935f;
  font-size: 1rem;
  margin-bottom: 1rem;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 2px;
`;

export const BookingTitle = styled.h2`
  color: white;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  font-weight: 300;
  text-transform: uppercase;
  letter-spacing: 4px;
`;

export const SelectContainer = styled.div`
  position: relative;
  width: 100%;
  margin-bottom: 1.5rem;
`;

export const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  background: transparent;
  color: white;
  cursor: pointer;
  border: none;
  border-bottom: 1px solid white;
  option {
    color: black;
  }
`;

export const BookNowButton = styled.button`
  background-color: #c8935f;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 22px;
  &:hover {
    background-color: #c8935f;
  }
`;

// Hours Styled Components
export const HoursSection = styled.div`
  text-align: center;
`;

export const MealTime = styled.h4`
  color: white;
  font-size: 1rem;
  margin: 1.5rem 0 0.5rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

export const Hours = styled.p`
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
`;

// Modal Styled Components
export const Modal = styled.div<ModalProps>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: ${props => (props.isOpen ? 'flex' : 'none')};
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

export const ModalContent = styled.div<ModalContentProps>`
  position: relative;
  width: 90%;
  max-width: 800px;
  padding: 3rem;
  background: linear-gradient(rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0.85)), url(${props => props.bgImage});
  background-size: cover;
  background-position: center;
  color: white;
  text-align: center;
  box-sizing: border-box;
`;

export const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
`;

export const ModalText = styled.p`
  margin-bottom: 2rem;
  line-height: 1.6;
`;

export const ModalForm = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  width: 100%;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

export const RestaurantLogo = styled.div`
  position: absolute;
  right: 1rem;
  top: 30%;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;

  &:after {
    content: '';
    position: absolute;
    width: 50px;
    height: 50px;
    border: 1px solid #c8935f;
    border-radius: 50%;
  }
`;
