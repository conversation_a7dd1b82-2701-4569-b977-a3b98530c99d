import React, {useEffect, useState} from 'react';
import {useTimer} from 'react-timer-hook';
import styled from 'styled-components';
import * as S from '@app/pages/BookingEngine/components/TimerDisplay/TimerDisplay.style';
import {Tooltip} from 'antd';

interface TimerDisplayProps {
  expiryTimestamp: Date | null;
  mode: 'Table' | 'Card';
}

const TimerDisplay: React.FC<TimerDisplayProps> = ({expiryTimestamp, mode}) => {
  const [timerExpiry, setTimerExpiry] = useState<Date | null>(null);
  const [isExpired, setIsExpired] = useState(false);

  const {minutes, hours, restart} = useTimer({
    expiryTimestamp: timerExpiry || new Date(),
    onExpire: () => {
      setIsExpired(true);
    },
  });

  useEffect(() => {
    if (expiryTimestamp) {
      setTimerExpiry(expiryTimestamp);
      restart(expiryTimestamp);
      setIsExpired(false);
    }
  }, [expiryTimestamp, restart]);

  const formatTime = (time: number) => String(time).padStart(2, '0');

  return mode === 'Table' ? (
    <Tooltip title="Estimated Preparation Time">
      <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
        <span style={{fontWeight: 'medium', fontSize: '14px', marginRight: '4px', color: 'red'}}>
          {formatTime(hours)} hr
        </span>
        <span style={{fontWeight: 'medium', fontSize: '14px', color: 'red'}}>{formatTime(minutes)} mins </span>
      </div>
    </Tooltip>
  ) : (
    <Tooltip title="Estimated Preparation Time">
      <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center', marginTop: '10px'}}>
        <span style={{fontWeight: 'medium', fontSize: '10px', marginRight: '4px', color: 'red'}}>
          {formatTime(hours)} hr
        </span>
        <span style={{fontWeight: 'medium', fontSize: '10px', color: 'red'}}>{formatTime(minutes)} mins </span>
      </div>
    </Tooltip>
  );
};

export default TimerDisplay;
