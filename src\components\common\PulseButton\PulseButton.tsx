import React from 'react';
import styled, {css, keyframes} from 'styled-components';

const buttonB = '.125em';
const buttonW = '8.5em';
const buttonH = '2em';
const moveAct: any = '.125em';

const contrastAnimation = keyframes`
from {
  filter: contrast(0.65);
}
to {
  filter: contrast(1.5);
}
`;
const PulseButton = styled.button<{pressOn?: any; invert?: any; isOnDemand: boolean}>`
  position: relative;
  margin: 0.5em;
  border: solid 0.125em transparent;
  padding: 0;
  width: ${buttonW};
  height: ${buttonH};
  border-radius: calc(0.5 * ${buttonH});
  color: #fff;
  text-shadow: 1px 1px var(--c-sh-txt, rgba(0, 0, 0, 0.5));
  font: 900 1.5em calligraffitti, system-ui;
  filter: contrast(0.65);
  // animation: ${contrastAnimation} 0.75s alternate infinite;
  transition: 0.2s ease-out;
  cursor: pointer;

  &:hover,
  &:focus {
    outline: none;
    filter: none;
  }

  &:active {
    transform: translateY(${moveAct});
  }

  ${props =>
    props.isOnDemand &&
    props.id === 'b00' &&
    css`
      animation: ${contrastAnimation} 0.75s alternate infinite;
    `}

  ${props =>
    props.pressOn &&
    css`
      &:after {
        content: '';
        position: absolute;
        top: calc(100% + ${2 * moveAct});
        right: -1em;
        left: -1em;
        height: 4px;
        background: radial-gradient(rgba(0, 0, 0, 0.25), transparent 50%);
        transition: inherit;
      }

      &:active:after {
        transform: translateY(-${moveAct}) scale(1.125, 1.25);
      }
    `}

  ${props =>
    props.invert &&
    css`
      color: #000;
      --c-sh-txt: rgba(255, 255, 255, 0.5);
    `}

  ${props =>
    props.id === 'b00' &&
    css`
      box-shadow: inset 0 0 0.125em rgba(255, 255, 255, 0.75);
      background: linear-gradient(#f8e7e8, #e30001, #bc0000) content-box,
        linear-gradient(-80deg, #f11f20, #9c1c1c, #f11f20) 0/37% 100% border-box;
    `}

  ${props =>
    props.id === 'b10' &&
    css`
      box-shadow: inset 0 0 0.125em rgba(255, 255, 255, 0.75);
      background: linear-gradient(#f8e7e8, #b2b2b2, #b2b2b2) content-box,
        linear-gradient(-80deg, #b2b2b2, #b2b2b2, #b2b2b2) 0/37% 100% border-box;
    `}
`;

export default PulseButton;
