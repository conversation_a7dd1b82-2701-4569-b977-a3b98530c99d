import React from 'react';
import {CategoryFilterCards} from './MainLayout.styles';

interface Props {
  title: string;
  backgroundImage: string;
  background?: string;
  onClick: () => void;
}

const CatFilterCards: React.FC<Props> = ({backgroundImage, background, title, onClick}) => {
  return (
    <CategoryFilterCards background={background} onClick={onClick}>
      {title}
    </CategoryFilterCards>
  );
};

export default CatFilterCards;
