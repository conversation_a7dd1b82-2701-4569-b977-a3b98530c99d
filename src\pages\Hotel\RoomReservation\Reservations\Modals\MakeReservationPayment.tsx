/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {useEffect, useState} from 'react';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import * as S from './Modals.style';
import {Col, Row, Select} from 'antd';
import {Input} from '@app/components/common/inputs/Input/Input';
import {FormInstance} from 'antd/es/form/Form';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {setLoading} from '@app/store/slices/commonSlice';
import {notificationController} from '@app/controllers/notificationController';
import {CARD, MONEY, PAY_BY_LINK} from '@app/assets';
import {makeInvoicePayment, makePaymentByLink} from '@app/api/hotel/invoice.api';
import {isPositiveNumber} from '@app/utils/functions';
import EmailCustomizer from '@app/components/common/EmailCustomizer/EmailCustomizer';
import {log} from 'handlebars/runtime';
import CurrencyInput from '@app/components/common/inputs/CurrencyInput/CurrencyInput';

export interface IPaymentValues {
  amount: string;
}

interface FieldData {
  name: string | number;
  value?: any;
}

export interface MakePaymentProps {
  amount: number;
  invoiceId: number;
  paid: boolean;
  currencyId: number;
  paymentMethod?: string;
  account: string;
  paymentStatus: string;
  paymentType: string;
}

export interface IInvoiceData {
  id: number;
  dueAmount: number;
  currencyPrefix: string;
  currencyId: number;
  reservationId: number;
}

interface Props {
  form: FormInstance;
  reloadData: () => void;
  isModalVisible: boolean;
  onCancel: () => void;
  rowData: IInvoiceData;
  readOnly?: boolean;
  onChangeAmount?: any;
  isCheckIn: boolean;
  handleChangePaymentType: (type: 'CASH' | 'CREDITCARD' | 'ONLINE_PAYMENT') => void;
  onStart: () => void;
  onSuccess: (data: any) => void;
  onFailure: () => void;
  isFullyPaid: boolean;
  setIsFullyPaid: (value: boolean) => void;
}

const {Option} = Select;
export const MakeReservationPayment: React.FC<Props> = ({
  isModalVisible,
  onCancel,
  form,
  rowData,
  reloadData,
  readOnly = false,
  onChangeAmount,
  isCheckIn,
  handleChangePaymentType,
  onSuccess,
  onStart,
  onFailure,
  isFullyPaid,
  setIsFullyPaid,
}) => {
  const [fields, setFields] = React.useState<FieldData[]>([{name: 'amount', value: undefined}]);
  const [currencyPrefix, setCurrencyPrefix] = React.useState('');
  const [paymentType, setpaymentType] = useState<'CASH' | 'CREDITCARD' | 'ONLINE_PAYMENT'>('CASH');

  const dispatch = useAppDispatch();
  const {reservationDueAmount} = useAppSelector(state => state.reservationSlice);
  const ACCOUNT_TYPE = {
    DEBIT: 'DEBITS',
    CREDITS: 'CREDITS',
  };

  const onCloseModal = () => {
    reloadData();
    form.resetFields();
    onCancel();
  };

  const sendPaymentLink = async () => {
    onStart();
    try {
      const result = await makePaymentByLink(rowData.reservationId);
      if (result.statusCode === '20000') {
        notificationController.success({message: result.message});
        onCloseModal();
      } else {
        notificationController.error({message: result.message});
        onFailure();
        dispatch(setLoading(false));
      }
    } catch (error) {}
  };

  const create = async (payload: MakePaymentProps) => {
    const request: MakePaymentProps = {
      amount: Number(payload?.amount),
      paymentType: payload?.paymentStatus,
      invoiceId: rowData.id,
      paid: true,
      currencyId: rowData.currencyId,
      paymentMethod: paymentType,
      account: isPositiveNumber(rowData.dueAmount) ? ACCOUNT_TYPE.DEBIT : ACCOUNT_TYPE.CREDITS,
      paymentStatus: '',
    };
    onStart();
    try {
      const result = await makeInvoicePayment(request);
      if (result.statusCode === '20000') {
        notificationController.success({message: result.message});
        onSuccess(result);
        await onCloseModal();
      } else {
        notificationController.error({message: result.message});
        onFailure();
        dispatch(setLoading(false));
      }
    } catch (error) {}
  };

  const onFinish = () => {
    dispatch(setLoading(true));
    const formData = form.getFieldsValue();

    if (paymentType === 'ONLINE_PAYMENT') {
      sendPaymentLink();
    } else {
      create(formData);
    }
  };
  useEffect(() => {
    if (isModalVisible) {
      form.setFieldValue('amount', rowData?.dueAmount.toFixed(2)), setCurrencyPrefix(rowData?.currencyPrefix);
    }
  }, [rowData, isModalVisible, form, reservationDueAmount]);

  const NUMBER_REGEX = /^\d*\.?\d*$/;

  const onPaymentTypeChange = async (type: 'CASH' | 'CREDITCARD' | 'ONLINE_PAYMENT') => {
    setpaymentType(type);
    handleChangePaymentType(type);
    form.setFieldValue('amount', rowData?.dueAmount.toFixed(2));
    form.setFieldValue('paymentStatus', 'FULLYPAID');
    form.validateFields();
  };

  const onPaymentStatusChange = (value: string) => {
    form.setFields([
      {
        name: 'amount',
        errors: [],
        rules: [{required: false, message: ''}],
      },
    ]);
    setIsFullyPaid(value === 'FULLYPAID');
    if (value === 'FULLYPAID') {
      form.setFieldValue('amount', rowData?.dueAmount.toFixed(2));
    }
  };

  return (
    <S.FormContent>
      <S.PaymentOptionWrapper2>
        <S.PaymentOptionSection onClick={() => onPaymentTypeChange('CASH')}>
          <S.PaymentOptionOutline $selected={paymentType === 'CASH'}>
            <S.Image src={MONEY} />
          </S.PaymentOptionOutline>
          <S.PaymentType $selected={paymentType === 'CASH'}>Cash</S.PaymentType>
        </S.PaymentOptionSection>
        {rowData.dueAmount > 0 ? (
          <>
            <S.PaymentOptionSection onClick={() => onPaymentTypeChange('CREDITCARD')}>
              <S.PaymentOptionOutline $selected={paymentType === 'CREDITCARD'}>
                <S.Image src={CARD} />
              </S.PaymentOptionOutline>
              <S.PaymentType $selected={paymentType === 'CREDITCARD'}>Credit Card</S.PaymentType>
            </S.PaymentOptionSection>
            {/* <S.PaymentOptionSection onClick={() => onPaymentTypeChange('ONLINE_PAYMENT')}>
              <S.PaymentOptionOutline $selected={paymentType === 'ONLINE_PAYMENT'}>
                <S.Image style={{height: 100, width: 100}} src={PAY_BY_LINK} />
              </S.PaymentOptionOutline>
              <S.PaymentType $selected={paymentType === 'ONLINE_PAYMENT'}>Payment Link</S.PaymentType>
            </S.PaymentOptionSection> */}
          </>
        ) : null}
      </S.PaymentOptionWrapper2>
      <BaseForm
        size="middle"
        name="paymentForm"
        form={form}
        initialValues={{amount: rowData.dueAmount.toFixed(2), paymentStatus: 'FULLYPAID'}}
        fields={fields}
        onFinish={onFinish}
        onFieldsChange={(_, allFields) => {
          //   dispatch(setIsTouchAction(true));
        }}>
        {rowData?.dueAmount >= 0 && (
          <Row
            gutter={{xs: 10, md: 15, xl: 30}}
            style={{
              display: 'flex',
              justifyContent: 'center',
            }}>
            <Col xs={24} md={16}>
              <BaseForm.Item
                label="Payment Status"
                name="paymentStatus"
                rules={[{required: true, message: 'Please select the Payment Status.'}]}>
                <Select
                  placeholder="Select Payment Status"
                  style={{width: '100%'}}
                  onChange={onPaymentStatusChange}
                  disabled={paymentType === 'ONLINE_PAYMENT'}>
                  <Option value="FULLYPAID"> Paid</Option>
                  <Option value="OUTSTANDING">Balance Payment </Option>
                  <Option value="ADVANCE">Advance Payment</Option>
                </Select>
              </BaseForm.Item>
            </Col>
          </Row>
        )}
        <Row
          gutter={{xs: 10, md: 15, xl: 30}}
          style={{
            display: 'flex',
            justifyContent: 'center',
          }}>
          <Col xs={24} md={16}>
            <BaseForm.Item
              name="amount"
              label={`Amount (${currencyPrefix} ${new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }).format(rowData.dueAmount)})`}
              rules={[
                {
                  required: false,
                  message: 'Required field',
                },
                ({getFieldValue}) => ({
                  validator(_, value) {
                    const input = value.trim();
                    if (value === '') {
                      return Promise.reject('Please enter value');
                    }
                    if (Number(value) > Number(rowData.dueAmount.toFixed(2))) {
                      return Promise.reject(`Please enter below due amount ${rowData.dueAmount.toFixed(2)}`);
                    }
                    if (isCheckIn && !input.match(NUMBER_REGEX)) {
                      return Promise.reject('Please enter numbers only');
                    }

                    return Promise.resolve();
                  },
                }),
              ]}>
              <CurrencyInput
                onChange={e => onChangeAmount(e.target.value)}
                readOnly={paymentType === 'ONLINE_PAYMENT' || isFullyPaid}
                placeholder="Enter Amount"
                prefix={currencyPrefix}
              />
            </BaseForm.Item>
          </Col>
        </Row>
      </BaseForm>
    </S.FormContent>
  );
};
