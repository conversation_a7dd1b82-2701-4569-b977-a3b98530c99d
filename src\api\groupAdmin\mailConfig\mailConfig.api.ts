import authInstance from '@app/api/authInstance';
import instance, {HOTEL_SERVICE, LOGIN_SERVICE} from '@app/api/instance';

export interface IApiResponse {
  message: string;
  result: any;
  status: string;
  statusCode: string;
}

export interface IupdateMailConfigPayload {
  id: number;
  host: string;
  username: string;
  password: string;
  port: number;
  protocol: string;
  sendEmail: string;
}

export interface ICreateMailConfigPayload {
  host: string;
  username: string;
  password: string;
  port: number;
  protocol: string;
  sendEmail: string;
}

export const getMailconfig = (hotelId: number): Promise<IApiResponse> =>
  instance.get<IApiResponse>(HOTEL_SERVICE + `email-configuration`).then(({data}) => data);

export const updateMailconfig = (payload: IupdateMailConfigPayload): Promise<IApiResponse> => {
  return instance.put<IApiResponse>(HOTEL_SERVICE + 'email-configuration', payload).then(({data}) => data);
};

export const createMailconfig = (payload: ICreateMailConfigPayload): Promise<IApiResponse> => {
  return instance.post<IApiResponse>(HOTEL_SERVICE + 'email-configuration', payload).then(({data}) => data);
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const createCCmailConfig = (payload: any) => {
  return instance.post<IApiResponse>(HOTEL_SERVICE + 'send-email-config', payload).then(({data}) => data);
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const updateCCmailConfig = (payload: any) => {
  return instance.post<IApiResponse>(HOTEL_SERVICE + 'send-email-config', payload).then(({data}) => data);
};

export const getCCMailconfig = (hotelId: number): Promise<IApiResponse> =>
  instance.get<IApiResponse>(HOTEL_SERVICE + `send-email-config/hotel/${hotelId}`).then(({data}) => data);

export const deleteCCMailconfig = (id: number): Promise<IApiResponse> =>
  instance.delete<IApiResponse>(HOTEL_SERVICE + `send-email-config/${id}`).then(({data}) => data);
