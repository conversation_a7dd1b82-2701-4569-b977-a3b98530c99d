import instance, {HOTEL_SERVICE} from '@app/api/instance';

export interface IRoomRequest {
  id?: any;
  roomNumber: string;
  unitCode: number;
  phoneExtention: number;
  viewTypeId: number;
  roomTypeId: number;
  maxAdults: number;
  maxChildren: number;
  roomName: string;
  roomWidth: number;
  roomLength: number;
  hotelId: number;
  imageUrl?: File[];
  bedsId?: number;
  unitId?: string;
}

export const CreateRoom = (payload: CreateRoomProps): Promise<RoomResponse> => {
  const roomTypeFormData = new FormData();
  roomTypeFormData.append('room', JSON.stringify(payload.room));
  roomTypeFormData.append('image', payload.image);
  return instance.post<RoomResponse>(HOTEL_SERVICE + 'room', roomTypeFormData).then(({data}) => data);
};

export const getAllRooms = (
  hotelId: number,
  {phoneExtention, roomNumber, roomType, unitCode, viewType, roomName}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<RoomResponse> =>
  instance
    .get<RoomResponse>(
      HOTEL_SERVICE +
        `room/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&roomNumber=${
          roomNumber ? roomNumber : ''
        }&unitCode=${unitCode ? unitCode : ''}&viewType=${viewType ? viewType : ''}&roomType=${
          roomType ? roomType : ''
        }&phoneExtention=${phoneExtention ? phoneExtention : ''}&roomName=${
          roomName ? roomName : ''
        }&hotelId=${hotelId}`,
    )
    .then(({data}) => data);

export const getAllRoomsWithoutPagination = (hotelId: number): Promise<RoomResponse> =>
  instance.get<RoomResponse>(HOTEL_SERVICE + `rooms/search?hotelId=${hotelId}`).then(({data}) => data);

export const UpdateRoom = (payload: UpdateRoomProps): Promise<RoomResponse> => {
  const roomTypeFormData = new FormData();
  roomTypeFormData.append('room', JSON.stringify(payload.room));
  roomTypeFormData.append('image', payload.image);
  return instance.put<RoomResponse>(HOTEL_SERVICE + 'room', roomTypeFormData).then(({data}) => data);
};

export const DeleteRoom = (id: number): Promise<RoomResponse> =>
  instance.delete<RoomResponse>(HOTEL_SERVICE + `room/${id}`).then(({data}) => data);

export const CreateRoomWithImage = (roomPayload: IRoomRequest): Promise<RoomResponse> => {
  const formData = new FormData();
  const obj = JSON.stringify({
    roomNumber: roomPayload.roomNumber,
    unitCode: roomPayload.unitCode,
    phoneExtention: roomPayload.phoneExtention,
    viewTypeId: roomPayload.viewTypeId,
    roomTypeId: roomPayload.roomTypeId,
    maxAdults: roomPayload.maxAdults,
    maxChildren: roomPayload.maxChildren,
    roomName: roomPayload.roomName,
    roomWidth: roomPayload.roomWidth,
    roomLength: roomPayload.roomLength,
    hotelId: roomPayload.hotelId,
    bedsId: roomPayload?.bedsId,
    unitId: roomPayload?.unitId,
  });
  const image: any = roomPayload?.imageUrl;
  image.map((image: any) => {
    formData.append('image', image);
  });

  formData.append('room', obj);

  return instance.post<RoomResponse>(HOTEL_SERVICE + 'room', formData).then(({data}) => data);
};

export const UpdateRoomWithImage = (roomPayload: IRoomRequest): Promise<RoomResponse> => {
  const formData = new FormData();
  const obj = JSON.stringify({
    id: roomPayload.id,
    roomNumber: roomPayload.roomNumber,
    unitCode: roomPayload.unitCode,
    phoneExtention: roomPayload.phoneExtention,
    viewTypeId: roomPayload.viewTypeId,
    roomTypeId: roomPayload.roomTypeId,
    maxAdults: roomPayload.maxAdults,
    maxChildren: roomPayload.maxChildren,
    roomName: roomPayload.roomName,
    roomWidth: roomPayload.roomWidth,
    roomLength: roomPayload.roomLength,
    hotelId: roomPayload.hotelId,
    bedsId: roomPayload?.bedsId,
    unitId: roomPayload?.unitId,
  });
  const image: any = roomPayload?.imageUrl;
  image.map((image: any) => {
    formData.append('image', image);
  });

  formData.append('room', obj);

  return instance.put<RoomResponse>(HOTEL_SERVICE + 'room', formData).then(({data}) => data);
};

export const getAllRoomStatus = (
  hotelId: number,
  {phoneExtention, roomNumber, roomType, unitCode, viewType, roomName}: FilterProps,
  pageSize: number | undefined,
  current: number,
): Promise<RoomResponse> =>
  instance
    .get<RoomResponse>(
      HOTEL_SERVICE +
        `room/search?page=${current}&size=${pageSize}&sortField=id&direction=DESC&roomNumber=${
          roomNumber ? roomNumber : ''
        }&unitCode=${unitCode ? unitCode : ''}&viewType=${viewType ? viewType : ''}&roomType=${
          roomType ? roomType : ''
        }&phoneExtention=${phoneExtention ? phoneExtention : ''}&roomName=${
          roomName ? roomName : ''
        }&hotelId=${hotelId}&roomStatus=CHECKEDOUT`,
    )
    .then(({data}) => data);

export const UpdateRoomStatus = (payload: RoomStatus): Promise<any> => {
  return instance.put<any>(HOTEL_SERVICE + 'room/status', payload).then(({data}) => data);
};

export interface CreateRoomProps {
  room: {
    id?: any;
    roomNumber: string;
    unitCode: string;
    phoneExtention: number;
    viewTypeId: number;
    roomTypeId: number;
    maxAdults: number;
    maxChildren: number;
    roomName: string;
    roomWidth: number;
    roomLength: number;
    hotelId: number;
  };
  image: string;
}
export interface UpdateRoomProps {
  room: {
    id: number;
    roomNumber: string;
    unitCode: string;
    phoneExtention: number;
    roomStatus?: string;
    viewTypeId: number;
    roomTypeId: number;
    maxAdults: number;
    maxChildren: number;
    roomName: string;
    roomWidth: number;
    roomLength: number;
    hotelId: number;
  };
  image: File;
}
export interface RoomResponse {
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result: any;
  status: string;
  statusCode: string;
}
export interface FilterProps {
  roomNumber: string;
  unitCode: string;
  viewType: string;
  roomType: string;
  phoneExtention: string;
  roomName: string;
}

export interface RoomStatus {
  id: string;
  status: string;
  onDemandStatus: string;
  houseKeepingStatus: string;
}
