/* eslint-disable @typescript-eslint/no-explicit-any */
import {Modal} from '@app/components/common/Modal/Modal';
import {Popconfirm} from '@app/components/common/Popconfirm/Popconfirm';
import React, {useEffect, useState} from 'react';
import {ICancelModal} from '../../interface/interface';
import {Button} from '@app/components/common/buttons/Button/Button';
import * as S from './Modals.style';
import {Col, Descriptions, Row, Typography} from 'antd';
import dayjs from 'dayjs';
import {StayTypeTitle} from '@app/components/common/StayTypeTitle/StayTypeTitle';
import {FONT_SIZE} from '@app/styles/themes/constants';
import {TextArea} from '@app/components/common/inputs/Input/Input';
import {BaseForm} from '@app/components/common/forms/BaseForm/BaseForm';
import {FormInstance} from 'antd/es/form/Form';
import './style.css';
import {difference, every, filter, flattenDeep, isArray, isEmpty, uniq} from 'lodash';
import {cancelReservation} from '@app/api/hotel/reservation/reservation.api';
import {notificationController} from '@app/controllers/notificationController';
import {useAppDispatch, useAppSelector} from '@app/hooks/reduxHooks';
import {getCancelModalData} from '@app/store/slices/reservationSlice';
import {formatNumberToDecimal} from '@app/utils/utils';
import {Checkbox} from '@app/components/common/Checkbox/Checkbox';
import {ENV} from '@app/shared/environment';
import EmailCustomizer from '@app/components/common/EmailCustomizer/EmailCustomizer';
import {EMAIL_TYPES} from '@app/shared/constants';
import CurrencyInput from '@app/components/common/inputs/CurrencyInput/CurrencyInput';
import ManualCancellation from './typesOfCancellations/ManualCancellation';
import AutomaticCancellation from './typesOfCancellations/AutomaticCancellation';
import {HOTEL_SERVICE_MODULE_NAME, modulePermission} from '@app/utils/permissions';

interface TaxPolicy {
  taxName: string;
  amount: number;
}

export interface RoomData {
  reservedRoomName: string;
  roomNumber: string;
  noOfAdults: number;
  noOfChildren: number;
  meal: string;
  roomTypeName: string;
  cancellationPolicyResponseList: CancellationPolicyResponse[];
  reservationCurrencyPrefix: string;
}

interface CancellationPolicyResponse {
  serviceChargeTaxPrice: number;
  cancelDate: string;
  totalPrice: number;
  percentage: number;
  reservedRoomDayPriceTaxResponse: TaxPolicy[];
  totalPriceWithTax: number;
  reservationCurrencyPrefix: string;
  serviceCharge?: number;
}

type AutoCancellationPayload = {
  cancellationType: 'AUTO';
  reservedRoomCancellationRequests: {
    reservedRoomId: number;
    reason: string;
    reservedRoomDayCancellationRequestList: {cancelDate: string}[];
  }[];
};

type ManualAmountCancellationPayload = {
  cancellationType: 'MANUAL_AMOUNT';
  manualCancellationRefundAmount: number;
  reservedRoomCancellationRequests: {
    reservedRoomId: number;
    reason: string;
    reservedRoomDayCancellationRequestList: {cancelDate: string}[];
  }[];
};

type ManualRateCancellationPayload = {
  cancellationType: 'MANUAL_RATE';
  manualCancellationRefundPercentage: number;
  reservedRoomCancellationRequests: {
    reservedRoomId: number;
    reason: string;
    reservedRoomDayCancellationRequestList: {cancelDate: string}[];
  }[];
};

type CancellationPayload = AutoCancellationPayload | ManualAmountCancellationPayload | ManualRateCancellationPayload;

const CancelModal: React.FC<ICancelModal> = ({close, open, reloadTable, setSearchObj, setDefaultSearchPayload}) => {
  const [checkboxStates, setCheckboxStates]: any = useState({});
  const [selectedDates, setSelectedDates]: any = useState({});
  const [selectedRooms, setSelectedRooms]: any = useState([]);
  const [totalDeductionAmount, setTotalDeductionAmount]: any = useState(0);
  const {hotelId} = useAppSelector(state => state.hotelSlice.hotelConfig);
  const {cancelModalData} = useAppSelector(state => state.reservationSlice);
  const [formItemErrors, setFormItemErrors]: any = useState({});
  const [buttonDisabled, setButtonDisabled]: any = useState({});
  const [isLoading, setisLoading] = useState(false);
  const [visiblePendingMailModal, setvisiblePendingMailModal] = useState(false);
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [templateMessage, settemplateMessage] = useState('');
  const [selectedPendingMailId, setSelectedPendingMailId] = useState<number | null>(null);

  const [isToggled, setIsToggled] = useState(false);
  const [selectValue, setSelectValue] = useState<string | undefined>(undefined);
  const [inputValue, setInputValue] = useState('');
  const [refundRoomPrice, setRefundRoomPrice] = useState(0);
  const [totalPrice, setTotalPrice] = useState(0);
  const [validateAllDateSelection, setValidateAllDateSelection] = useState<boolean>(false);
  const [selectedRoomIndexes, setSelectedRoomIndexes] = useState<number[]>([]);

  const userPermission = useAppSelector(state => state.user.permissions);
  const permissions = modulePermission(userPermission, HOTEL_SERVICE_MODULE_NAME.MANUAL_CANCELLATION);

  const handleRefundRoomPriceUpdate = (refundPrice: number) => {
    setRefundRoomPrice(refundPrice);
  };

  const onTotalPrice = (totalPrice: number) => {
    setTotalPrice(totalPrice);
  };

  useEffect(() => {
    const validateAllDate = Object.values(buttonDisabled).some(value => value === true);
    setValidateAllDateSelection(validateAllDate);
  }, [buttonDisabled]);

  const roomForms: Array<FormInstance<any>> =
    // eslint-disable-next-line react-hooks/rules-of-hooks
    Array.from({length: 16}, (_, index) => index).map(() => BaseForm.useForm()[0]) || [];

  const dispatch = useAppDispatch();

  const resetForm = () => {
    setCheckboxStates({});
    setSelectedDates({});
    setSelectedRooms([]);
    setTotalDeductionAmount(0);
    setFormItemErrors({});
    setButtonDisabled({});
    setRefundRoomPrice(0);
  };

  const cancelRoom = async (payload: any) => {
    try {
      const response = await cancelReservation(cancelModalData.reservationListResponse.reservationId, payload);
      if (response.statusCode === '20000') {
        setSelectedPendingMailId(response.result.pendingEmailId);
        setvisiblePendingMailModal(true);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        notificationController.success({message: 'Reservation cancelled successfully'});
        await dispatch(
          getCancelModalData({
            reservationId: cancelModalData.reservationListResponse.reservationId,
            hotelId: hotelId,
          }),
        );
        resetForm();
        form.resetFields();
        setInputValue('');
        setSelectValue(undefined);
        setFormItemErrors({});
        reloadTable();
        roomForms.filter((form, index) => {
          form.resetFields([`reason-${index}`]);
        });
        setisLoading(false);
      } else {
        notificationController.error({message: response.message});
        setisLoading(false);
      }
    } catch (error) {
      setisLoading(false);
    }
  };

  useEffect(() => {
    const totalDeduction = selectedRooms.reduce((total: number, selectedRoomIndex: number) => {
      const selectedRoomDates = selectedDates[selectedRoomIndex] || [];
      const room = cancelModalData.reservedRoomDetailsResponseList[selectedRoomIndex];

      const selectedRoomPolicies = selectedRoomDates.map((dateIdx: number) => {
        return room.cancellationPolicyResponseList[dateIdx];
      });

      const roomDeductionAmount = selectedRoomPolicies.reduce((roomTotal: number, policy: any) => {
        return roomTotal + (policy.totalPrice * policy.percentage) / 100;
      }, 0);

      return total + roomDeductionAmount;
    }, 0);

    setTotalDeductionAmount(totalDeduction);
  }, [selectedDates]);

  const handleDateChange = (roomIndex: number, dateIndex: number, room: any) => {
    setSelectedRoomIndexes(prev => [...prev, roomIndex]);

    setSelectedDates((prevState: any) => {
      const roomDates = prevState[roomIndex] || [];

      const updatedDates = roomDates.includes(dateIndex)
        ? roomDates.filter((date: number) => date !== dateIndex)
        : [...roomDates, dateIndex];

      roomForms[roomIndex].setFieldsValue({
        [`selectedDate-${roomIndex}`]: updatedDates,
      });

      const selectedPolicies = updatedDates.map((date: number) => {
        return room.cancellationPolicyResponseList[date];
      });

      const deductionAmount = selectedPolicies.reduce((total: number, policy: any) => {
        return total + (policy.totalPrice * policy.percentage) / 100;
      }, 0);

      roomForms[roomIndex].setFieldsValue({
        [`deductionAmount-${roomIndex}`]: deductionAmount,
      });

      const isCheckedInRoom = room.reservedRoomStatus === 'CHECKEDIN';
      const validationResult = areDatesSequentialAndStartFromValidIndex(
        updatedDates,
        room.cancellationPolicyResponseList,
        isCheckedInRoom,
      );

      const isButtonDisabled = !validationResult.isValid;
      roomForms[roomIndex].setFieldsValue({
        [`isButtonDisabled-${roomIndex}`]: isButtonDisabled,
      });

      if (updatedDates.length === 0) {
        roomForms[roomIndex].setFieldsValue({
          [`selectedDate-${roomIndex}`]: updatedDates,
        });
        roomForms[roomIndex].setFields([
          {
            name: `selectedDate-${roomIndex}`,
            errors: [],
          },
        ]);
        setFormItemErrors((prevErrors: any) => {
          const updatedErrors: any = {...prevErrors};
          delete updatedErrors[roomIndex];
          return updatedErrors;
        });
        setButtonDisabled((prevState: any) => {
          const updatedState: any = {...prevState};
          delete updatedState[roomIndex];
          return updatedState;
        });
        return {
          ...prevState,
          [roomIndex]: updatedDates,
        };
      }

      if (!validationResult.isValid) {
        const errorMessage =
          validationResult.message || 'Selected dates must be sequential and start from valid indices.';
        roomForms[roomIndex].setFields([
          {
            name: `selectedDate-${roomIndex}`,
            errors: [errorMessage],
          },
        ]);
        setFormItemErrors((prevErrors: any) => ({
          ...prevErrors,
          [roomIndex]: errorMessage,
        }));
        setButtonDisabled((prevState: any) => ({
          ...prevState,
          [roomIndex]: validationResult.shouldButtonDisable,
        }));
      } else {
        roomForms[roomIndex].setFields([
          {
            name: `selectedDate-${roomIndex}`,
            errors: [],
          },
        ]);
        setFormItemErrors((prevErrors: any) => {
          const updatedErrors: any = {...prevErrors};
          delete updatedErrors[roomIndex];
          return updatedErrors;
        });
        setButtonDisabled((prevState: any) => {
          const updatedState: any = {...prevState};
          delete updatedState[roomIndex];
          return updatedState;
        });
      }

      return {
        ...prevState,
        [roomIndex]: updatedDates,
      };
    });
  };

  const areDatesSequentialAndStartFromValidIndex = (selectedDates: any[], policyList: any[], isCheckedIn: boolean) => {
    const cavernHotelId = Number(ENV.CAVERN_HOTEL_ID);
    const selectedDatesLength = selectedDates.length;
    const policyListLength = policyList.length;

    if (hotelId === cavernHotelId) {
      const remainingDatesCount = policyListLength - selectedDatesLength;

      if (remainingDatesCount < 2 && selectedDatesLength !== policyListLength) {
        return {
          isValid: true,
          shouldButtonDisable: false,
          message: 'A Minimum stay of two nights is required. You may cancel this reservation if prefer.',
        };
      }

      const sortedDates = [...selectedDates].sort((a, b) => a - b);
      if (!isCheckedIn) {
        const startIndex = sortedDates[0];
        const lastIndex = sortedDates[sortedDates.length - 1];
        if (selectedDates.length === 0) {
          return {isValid: false, shouldButtonDisable: true};
        }
        if (startIndex === 0 && lastIndex === sortedDates.length - 1) {
          return {isValid: true, shouldButtonDisable: false};
        }
        if (sortedDates.length === 1 && startIndex !== 0 && lastIndex !== policyList.length - 1) {
          return {isValid: false, shouldButtonDisable: true};
        }
        if (startIndex === policyList.length - sortedDates.length && lastIndex === policyList.length - 1) {
          return {isValid: true, shouldButtonDisable: false};
        }
        if ((startIndex !== 0 || lastIndex !== policyList.length - 1) && sortedDates.length > 1) {
          return {isValid: false, shouldButtonDisable: true};
        }
        const originArray = policyList.map((post: any, index: number) => index);
        const diff = difference(originArray, sortedDates);

        if (!isSequence(diff)) {
          return {isValid: false, shouldButtonDisable: true};
        }
      } else {
        const sortedDates2 = [...selectedDates].sort((a, b) => a - b);
        for (let i = 1; i < sortedDates2.length; i++) {
          if (sortedDates2[i] !== sortedDates2[i - 1] + 1) {
            return {isValid: false, shouldButtonDisable: true};
          }
        }
        const startIndex = sortedDates2[0];
        const lastIndex = sortedDates2[sortedDates2.length - 1];

        if (startIndex === 0 && lastIndex !== selectedDates.length - 1) {
          return {isValid: false, shouldButtonDisable: true};
        }
        if (startIndex !== 0 && lastIndex !== policyList.length - 1) {
          return {isValid: false, shouldButtonDisable: true};
        }
        if (startIndex === 0 && lastIndex !== policyList.length - 1) {
          return {isValid: false, shouldButtonDisable: true};
        }
        if (selectedDates.length === 0) {
          return {isValid: false, shouldButtonDisable: true};
        }
        if (startIndex === 0 && lastIndex !== sortedDates2.length - 1) {
          return {isValid: false, shouldButtonDisable: true};
        }
      }

      return {isValid: true, shouldButtonDisable: false};
    }

    const sortedDates = [...selectedDates].sort((a, b) => a - b);
    if (!isCheckedIn) {
      const startIndex = sortedDates[0];
      const lastIndex = sortedDates[sortedDates.length - 1];
      if (selectedDates.length === 0) {
        return {isValid: false, shouldButtonDisable: true};
      }
      if (startIndex === 0 && lastIndex === sortedDates.length - 1) {
        return {isValid: true, shouldButtonDisable: false};
      }
      if (sortedDates.length === 1 && startIndex !== 0 && lastIndex !== policyList.length - 1) {
        return {isValid: false, shouldButtonDisable: true};
      }
      if (startIndex === policyList.length - sortedDates.length && lastIndex === policyList.length - 1) {
        return {isValid: true, shouldButtonDisable: false};
      }
      if ((startIndex !== 0 || lastIndex !== policyList.length - 1) && sortedDates.length > 1) {
        return {isValid: false, shouldButtonDisable: true};
      }
      const originArray = policyList.map((post: any, index: number) => index);
      const diff = difference(originArray, sortedDates);

      if (!isSequence(diff)) {
        return {isValid: false, shouldButtonDisable: true};
      }
    } else {
      const sortedDates2 = [...selectedDates].sort((a, b) => a - b);
      for (let i = 1; i < sortedDates2.length; i++) {
        if (sortedDates2[i] !== sortedDates2[i - 1] + 1) {
          return {isValid: false, shouldButtonDisable: true};
        }
      }
      const startIndex = sortedDates2[0];
      const lastIndex = sortedDates2[sortedDates2.length - 1];

      if (startIndex === 0 && lastIndex !== selectedDates.length - 1) {
        return {isValid: false, shouldButtonDisable: true};
      }
      if (startIndex !== 0 && lastIndex !== policyList.length - 1) {
        return {isValid: false, shouldButtonDisable: true};
      }
      if (startIndex === 0 && lastIndex !== policyList.length - 1) {
        return {isValid: false, shouldButtonDisable: true};
      }
      if (selectedDates.length === 0) {
        return {isValid: false, shouldButtonDisable: true};
      }
      if (startIndex === 0 && lastIndex !== sortedDates2.length - 1) {
        return {isValid: false, shouldButtonDisable: true};
      }
    }
    return {isValid: true, shouldButtonDisable: false};
  };
  const isSequence = (array: number[]) => {
    return array.every((num, index) => index === 0 || num === array[index - 1] + 1);
  };

  const handleCancelModal = () => {
    reloadTable();
    resetForm();
    form.resetFields();
    close();
    setIsToggled(false);
    setSelectValue(undefined);
    setInputValue('');

    selectedRoomIndexes.forEach(roomIndex => {
      roomForms[roomIndex].setFields([
        {
          name: `selectedDate-${roomIndex}`,
          value: [],
          errors: [],
        },
      ]);
    });

    // Clear errors & button states
    setFormItemErrors((prevErrors: any) => {
      const updatedErrors = {...prevErrors};
      selectedRoomIndexes.forEach(index => delete updatedErrors[index]);
      return updatedErrors;
    });

    setButtonDisabled((prevState: any) => {
      const updatedState = {...prevState};
      selectedRoomIndexes.forEach(index => delete updatedState[index]);
      return updatedState;
    });

    // Clear selected room indexes
    setSelectedRoomIndexes([]);
  };

  const cancellationData = cancelModalData.reservedRoomDetailsResponseList.filter(
    x => x.reservedRoomStatus !== 'CANCELLED' && x.reservedRoomStatus !== 'CHECKEDOUT',
  );

  const onEmptyData = () => {
    if (cancellationData.length <= 0) {
      close();
      setDefaultSearchPayload({});
      setSearchObj({});
      window.history.replaceState({}, '');
      window.location.reload();
    }
  };

  const calculateMaxRefund = () => {
    let maxRefund = 0;

    for (const roomIndex in selectedDates) {
      if (selectedDates.hasOwnProperty(roomIndex)) {
        const room = cancelModalData.reservedRoomDetailsResponseList[parseInt(roomIndex)];
        const selectedRoomDates = selectedDates[parseInt(roomIndex)];

        maxRefund += selectedRoomDates.reduce((total: number, dateIndex: number) => {
          const policy = room?.cancellationPolicyResponseList[dateIndex];
          const totalRoomRefundAmount = (policy?.totalPrice * policy?.percentage) / 100;
          const totalTaxAmountRefundAmount = policy?.reservedRoomDayPriceTaxResponse?.reduce(
            (sum: number, taxItem: {amount: number}) => sum + taxItem.amount * (policy?.percentage / 100),
            0,
          );
          return total + (totalRoomRefundAmount + totalTaxAmountRefundAmount);
        }, 0);
      }
    }

    return maxRefund;
  };

  const handleBulkCancel = async () => {
    setisLoading(true);
    try {
      const cancelRequests: any[] = [];

      for (const roomIndex in selectedDates) {
        if (selectedDates.hasOwnProperty(roomIndex)) {
          const withoutCancelledRooms = cancelModalData.reservedRoomDetailsResponseList.filter(
            x => x.reservedRoomStatus !== 'CANCELLED' && x.reservedRoomStatus !== 'CHECKEDOUT',
          );
          const room = withoutCancelledRooms[parseInt(roomIndex)];
          const selectedRoomDates = selectedDates[parseInt(roomIndex)];

          if (selectedRoomDates.length > 0) {
            const cancellationDates = selectedRoomDates.map((dateIndex: number) => ({
              cancelDate: room.cancellationPolicyResponseList[dateIndex].cancelDate,
              percentage: room.cancellationPolicyResponseList[dateIndex].percentage,
            }));

            const cancelRequest = {
              reservedRoomId: room.reservedRoomId,
              reason: 'Test',
              reservedRoomDayCancellationRequestList: cancellationDates,
            };

            cancelRequests.push(cancelRequest);
          }
        }
      }

      if (isToggled && selectValue === 'Value') {
        if (!inputValue || Number(inputValue) <= 0) {
          notificationController.error({
            message: 'Refund amount cannot be empty or less than or equal to 0.',
          });
          setisLoading(false);
          return;
        }

        if (Number(inputValue) > totalPrice) {
          notificationController.error({
            message: `Refund amount cannot exceed ${totalPrice}.`,
          });
          setisLoading(false);
          return;
        }
      }

      const payload: any = {
        cancellationType: isToggled ? (selectValue === 'Percentage' ? 'MANUAL_RATE' : 'MANUAL_AMOUNT') : 'AUTO',
        reservedRoomCancellationRequests: cancelRequests,
      };

      if (isToggled) {
        if (selectValue === 'Percentage') {
          if (Number(inputValue) > 100) {
            notificationController.error({
              message: 'Percentage cannot be greater than 100%.',
            });
            setisLoading(false);
            return;
          }
          payload.manualCancellationRefundPercentage = Number(inputValue);
        } else if (selectValue === 'Value') {
          payload.manualCancellationRefundAmount = refundRoomPrice;
        }
      }

      await cancelRoom(payload);
    } catch (error) {
      setisLoading(false);
      console.error('Error occurred during bulk cancellation:', error);
    }
  };

  const handleCancel = async (roomIndex: number, selectedDates: any, room: any) => {
    const hasErrors = buttonDisabled[roomIndex];
    if (hasErrors) {
      return;
    }

    try {
      setisLoading(true);
      const roomForm = roomForms[roomIndex];

      await roomForm.validateFields();
      const values = roomForm.getFieldsValue();

      const cancellationDates = selectedDates.map((dateIndex: number) => {
        const policy = room.cancellationPolicyResponseList[dateIndex];
        return {
          cancelDate: policy.cancelDate,
          percentage: policy.percentage,
        };
      });

      let payload: CancellationPayload;

      if (isToggled) {
        if (selectValue === 'Percentage') {
          if (Number(inputValue) > 100) {
            notificationController.error({
              message: 'Percentage cannot be greater than 100%.',
            });
            setisLoading(false);
            return;
          }
          payload = {
            cancellationType: 'MANUAL_RATE',
            manualCancellationRefundPercentage: Number(inputValue),
            reservedRoomCancellationRequests: [
              {
                reservedRoomId: room.reservedRoomId,
                reason: values[`reason-${roomIndex}`],
                reservedRoomDayCancellationRequestList: cancellationDates,
              },
            ],
          };
        } else {
          if (!inputValue || Number(inputValue) <= 0) {
            notificationController.error({
              message: 'Refund amount cannot be empty or less than or equal to 0.',
            });
            setisLoading(false);
            return;
          }

          if (Number(inputValue) > totalPrice) {
            notificationController.error({
              message: `Refund amount cannot exceed ${totalPrice} .`,
            });
            setisLoading(false);
            return;
          }

          payload = {
            cancellationType: 'MANUAL_AMOUNT',
            manualCancellationRefundAmount: refundRoomPrice,
            reservedRoomCancellationRequests: [
              {
                reservedRoomId: room.reservedRoomId,
                reason: values[`reason-${roomIndex}`],
                reservedRoomDayCancellationRequestList: cancellationDates,
              },
            ],
          };
        }
      } else {
        payload = {
          cancellationType: 'AUTO',
          reservedRoomCancellationRequests: [
            {
              reservedRoomId: room.reservedRoomId,
              reason: values[`reason-${roomIndex}`],
              reservedRoomDayCancellationRequestList: cancellationDates,
            },
          ],
        };
      }

      await cancelRoom(payload);
    } catch (error) {
      setisLoading(false);
      console.error('Error occurred during cancellation:', error);
    }
  };

  const handleToggle = (checked: boolean) => {
    setIsToggled(checked);
    setInputValue('');
    setSelectValue(undefined);
    setRefundRoomPrice(0);
    form.setFieldsValue({
      value: '',
      percentage: '',
      type: undefined,
    });
  };

  const handleSelectChange = (value: string) => {
    setSelectValue(value);
    setInputValue('');
    setRefundRoomPrice(0);
    form.setFieldsValue({
      value: '',
      percentage: '',
    });
  };

  const useDateSelectionValidator = (form: FormInstance, selectedDates: Record<string, number[]>) => {
    useEffect(() => {
      form.validateFields(['value', 'percentage']);
    }, [selectedDates, form]);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
  };

  const handleSelectAll = (roomIndex: number) => {
    const withoutCancelledRooms = cancelModalData.reservedRoomDetailsResponseList.filter(
      x => x.reservedRoomStatus !== 'CANCELLED' && x.reservedRoomStatus !== 'CHECKEDOUT',
    );
    const allDatesSelected =
      selectedDates[roomIndex]?.length === withoutCancelledRooms[roomIndex].cancellationPolicyResponseList.length;
    const updatedSelectedDates = {...selectedDates};

    setSelectedRoomIndexes(prev => [...prev, roomIndex]);

    if (allDatesSelected) {
      updatedSelectedDates[roomIndex] = [];
    } else {
      setValidateAllDateSelection(false);
      updatedSelectedDates[roomIndex] = withoutCancelledRooms[roomIndex].cancellationPolicyResponseList.map(
        (_, index) => index,
      );
      roomForms[roomIndex].setFieldsValue({
        [`selectedDate-${roomIndex}`]: updatedSelectedDates,
      });

      roomForms[roomIndex].setFields([
        {
          name: `selectedDate-${roomIndex}`,
          value: [],
          errors: [],
        },
      ]);
      setFormItemErrors((prevErrors: any) => {
        const updatedErrors = {...prevErrors};
        delete updatedErrors[roomIndex];
        return updatedErrors;
      });
      setButtonDisabled((prevState: any) => {
        const updatedState = {...prevState};
        delete updatedState[roomIndex];
        return updatedState;
      });
    }
    setSelectedDates(updatedSelectedDates);
  };

  const roomList = flattenDeep(cancellationData.map(x => x.cancellationPolicyResponseList));

  const [form] = BaseForm.useForm();

  useDateSelectionValidator(form, selectedDates);

  return (
    <>
      <Modal footer={null} title="Room Cancellation" size="xl" open={open} onCancel={handleCancelModal}>
        <Row gutter={{xs: 10, md: 15, xl: 30}}>
          <Col xs={24} md={15}>
            <React.Fragment>
              {!isEmpty(roomList) && (
                <div>
                  {permissions.VIEW && (
                    <S.ToggleWrapper>
                      {/* <S.ToggleLabel>Manual Cancellation</S.ToggleLabel>
                      <S.ToggleSwitch checked={isToggled} onChange={handleToggle} /> */}
                    </S.ToggleWrapper>
                  )}
                  {isToggled && (
                    <BaseForm name="stepForm" form={form}>
                      <Row gutter={15}>
                        <Col lg={6} md={6}>
                          <BaseForm.Item name="type" label="Type">
                            <S.ToggleSelect
                              placeholder="Select Type"
                              value={selectValue}
                              onChange={handleSelectChange}
                              options={[
                                {value: 'Value', label: 'Value'},
                                {value: 'Percentage', label: 'Percentage'},
                              ]}
                            />
                          </BaseForm.Item>
                        </Col>
                        {selectValue === 'Percentage' ? (
                          <Col lg={9} md={9}>
                            <BaseForm.Item
                              label="Refund Percentage"
                              name="percentage"
                              rules={[
                                {
                                  required: true,
                                  message: 'Percentage is required',
                                },
                                {
                                  validator: async (_, value) => {
                                    const hasSelectedDates = Object.values(selectedDates).some(
                                      dates => Array.isArray(dates) && dates.length > 0,
                                    );

                                    if (!hasSelectedDates) {
                                      return Promise.reject(new Error('Please select cancelling dates first'));
                                    }

                                    if (value < 0 || value > 100) {
                                      return Promise.reject(new Error('Please enter a percentage between 0 and 100'));
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}>
                              <S.ToggleInput
                                type="number"
                                placeholder={'Enter a percentage (0-100)%'}
                                value={inputValue}
                                onChange={handleInputChange}
                                min={0}
                                max={100}
                                disabled={!selectValue}
                              />
                            </BaseForm.Item>
                          </Col>
                        ) : (
                          <Col lg={9} md={9}>
                            <BaseForm.Item
                              name="value"
                              label="Value"
                              rules={[
                                {
                                  required: true,
                                  message: 'Value is required',
                                },
                                {
                                  validator: async (_, value) => {
                                    const hasSelectedDates = Object.values(selectedDates).some(
                                      dates => Array.isArray(dates) && dates.length > 0,
                                    );

                                    if (!hasSelectedDates) {
                                      return Promise.reject(new Error('Please select cancelling dates first'));
                                    }
                                    if (Number(value) > totalPrice) {
                                      return Promise.reject(
                                        new Error(`Refund amount cannot exceed ${totalPrice} value`),
                                      );
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}>
                              <CurrencyInput
                                placeholder={'Enter a value'}
                                value={inputValue}
                                onChange={handleInputChange}
                                disabled={!selectValue}
                              />
                            </BaseForm.Item>
                          </Col>
                        )}
                      </Row>
                    </BaseForm>
                  )}
                </div>
              )}
              <S.BlurCardWrapper>
                <S.BlueCard>
                  <S.Padding>
                    <S.CardTitle>Reservation Information</S.CardTitle>
                    <Descriptions>
                      <Descriptions.Item label="Reservation No" span={1.3}>
                        {cancelModalData?.reservationListResponse?.refNumber}
                      </Descriptions.Item>
                      <Descriptions.Item label="Channel Name" span={1.5}>
                        {cancelModalData?.reservationListResponse?.channelName}
                      </Descriptions.Item>
                    </Descriptions>
                    <Descriptions>
                      <Descriptions.Item label="Channel Email">
                        {cancelModalData?.reservationListResponse?.channelEmail}
                      </Descriptions.Item>
                    </Descriptions>
                  </S.Padding>
                </S.BlueCard>
              </S.BlurCardWrapper>

              {cancellationData &&
                cancellationData.map((room, roomIndex) => {
                  // if (!room?.cancellationPolicyResponseList || room?.cancellationPolicyResponseList.length === 0) {
                  //   return ()
                  // }
                  const withoutCancelledRooms = cancelModalData.reservedRoomDetailsResponseList.filter(
                    x => x.reservedRoomStatus !== 'CANCELLED' && x.reservedRoomStatus !== 'CHECKEDOUT',
                  );
                  const allDatesSelected =
                    selectedDates[roomIndex]?.length ===
                    withoutCancelledRooms[roomIndex].cancellationPolicyResponseList.length;
                  return (
                    <BaseForm key={`reserved-room${roomIndex}`} form={roomForms[roomIndex]} size="middle">
                      <S.BlurCardWrapper key={roomIndex}>
                        <S.BlueCard>
                          <S.Padding>
                            <S.TitleWrapper>
                              <S.RoomName>
                                {`${room?.reservedRoomName} - ${room?.roomNumber}`} ({' '}
                                <StayTypeTitle
                                  adultCount={room?.noOfAdults ? room?.noOfAdults : 0}
                                  childCount={room?.noOfChildren ? room?.noOfChildren : 0}
                                  isBold={true}
                                  meal={room?.meal ? room?.meal : ''}
                                  name={room?.roomTypeName ? room?.roomTypeName : ''}
                                  size={FONT_SIZE.md}
                                />
                                )
                              </S.RoomName>
                              {!room?.cancellationPolicyResponseList ||
                              room?.cancellationPolicyResponseList.length === 0 ? null : (
                                <Checkbox checked={allDatesSelected} onChange={() => handleSelectAll(roomIndex)}>
                                  <S.SelectallText>Select all dates</S.SelectallText>
                                </Checkbox>
                              )}
                            </S.TitleWrapper>

                            {!room?.cancellationPolicyResponseList ||
                            room?.cancellationPolicyResponseList.length === 0 ? (
                              <S.NoDatesAvalialble>
                                You cannot do the cancellation for this Reservation
                              </S.NoDatesAvalialble>
                            ) : (
                              <BaseForm.Item
                                name={`selectedDate-${roomIndex}`}
                                label="Select dates for cancellation"
                                rules={[
                                  {
                                    required: true,
                                    message: 'Required field',
                                    validator: (_, value) => {
                                      if (!value && !formItemErrors[roomIndex]) {
                                        return Promise.reject('Please select at least one date.');
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                                validateStatus={formItemErrors[roomIndex] ? 'error' : ''}
                                help={formItemErrors[roomIndex]}>
                                <S.DateFlexWrapper>
                                  {room?.cancellationPolicyResponseList?.map((policies, idx) => {
                                    const parsedDate = dayjs(policies.cancelDate);
                                    const isSelected = selectedDates[roomIndex]?.includes(idx);
                                    return (
                                      <S.DateWrapper key={idx}>
                                        <S.DateOutline
                                          $isSelected={isSelected}
                                          onClick={() => handleDateChange(roomIndex, idx, room)}>
                                          <S.DateText>
                                            {parsedDate.format('DD')} {parsedDate.format('MMM')},{' '}
                                            {parsedDate.format('YYYY')}
                                          </S.DateText>
                                          <S.CancellationReduction>
                                            {isToggled ? '' : `${policies?.percentage}% Refund`}
                                          </S.CancellationReduction>
                                        </S.DateOutline>
                                      </S.DateWrapper>
                                    );
                                  })}
                                </S.DateFlexWrapper>
                              </BaseForm.Item>
                            )}

                            {!room?.cancellationPolicyResponseList ||
                            room?.cancellationPolicyResponseList.length === 0 ? null : (
                              <>
                                <BaseForm.Item
                                  style={{margin: 0}}
                                  name={`reason-${roomIndex}`}
                                  label="Reason"
                                  rules={[
                                    {
                                      required: false,
                                      message: 'Required field',
                                    },
                                  ]}>
                                  <TextArea rows={3} placeholder="Reason for cancellation" />
                                </BaseForm.Item>
                                <S.ButtonFlex>
                                  <Popconfirm
                                    placement="topLeft"
                                    title="Are you sure to cancel these days for this room?"
                                    onConfirm={() => handleCancel(roomIndex, selectedDates[roomIndex], room)}
                                    okText="Yes"
                                    cancelText="No">
                                    <Button
                                      disabled={
                                        buttonDisabled[roomIndex] ||
                                        isEmpty(selectedDates[roomIndex]) ||
                                        isLoading ||
                                        (isToggled && !inputValue)
                                      }
                                      type="primary"
                                      danger>
                                      Cancel
                                    </Button>
                                  </Popconfirm>
                                </S.ButtonFlex>
                              </>
                            )}
                          </S.Padding>
                        </S.BlueCard>
                      </S.BlurCardWrapper>
                    </BaseForm>
                  );
                })}

              {totalDeductionAmount > 0 && (
                <S.TotalAmountWrapper>
                  <S.TotalDeductionAmount>
                    Total Refund Amount for Selected Rooms: {totalDeductionAmount}
                  </S.TotalDeductionAmount>
                </S.TotalAmountWrapper>
              )}
            </React.Fragment>
          </Col>

          {isToggled ? (
            <ManualCancellation
              cancellationData={cancellationData}
              selectedDates={selectedDates}
              handleBulkCancel={handleBulkCancel}
              isLoading={isLoading}
              validateAllDateSelection={validateAllDateSelection}
              selectValue={selectValue}
              inputValue={inputValue}
              onRefundRoomPriceUpdate={handleRefundRoomPriceUpdate}
              onTotalPrice={onTotalPrice}
            />
          ) : (
            <AutomaticCancellation
              cancellationData={cancellationData}
              selectedDates={selectedDates}
              handleDateChange={handleDateChange}
              handleBulkCancel={handleBulkCancel}
              isLoading={isLoading}
              validateAllDateSelection={validateAllDateSelection}
            />
          )}
        </Row>
        <S.EmptyFooterSpace />
      </Modal>

      {visiblePendingMailModal && (
        <EmailCustomizer
          isOpen={visiblePendingMailModal}
          setIsOpen={setvisiblePendingMailModal}
          status={status}
          setStatus={setStatus}
          onSubmitEmailCustomizer={function (): void {
            setSelectedPendingMailId(null);
          }}
          isResender={false}
          emailType={EMAIL_TYPES.PAYMENT}
          pendingMailId={selectedPendingMailId}
          message={templateMessage}
          reservationId={cancelModalData?.reservationListResponse?.reservationId}
          onClose={() => {
            setSelectedPendingMailId(null);
          }}
          onEmpty={onEmptyData}
        />
      )}
    </>
  );
};

export default CancelModal;
