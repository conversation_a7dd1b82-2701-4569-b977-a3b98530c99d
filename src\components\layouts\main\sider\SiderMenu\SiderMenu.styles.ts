import styled from 'styled-components';
import {Menu as AntMenu} from 'antd';
import {FONT_SIZE} from '@app/styles/themes/constants';

export const Menu = styled(AntMenu)`
  background: transparent;
  border-right: 0;

  a {
    width: 100%;
    display: block;
  }

  .ant-menu-item-only-child {
    padding-left: 60px;
  }

  .ant-menu-item,
  .ant-menu-submenu {
    font-size: ${FONT_SIZE.xs};
    margin-right: -10px;
  }

  .ant-menu-item-icon {
    width: 1rem;
  }

  .ant-menu-submenu-expand-icon,
  .ant-menu-submenu-arrow,
  span[role='img'],
  a,
  .ant-menu-item,
  .ant-menu-submenu {
    color: var(--text-sider-secondary-color);
    fill: var(--text-sider-secondary-color);
  }

  .ant-menu-title-content {
    margin-left: 0.7rem;
    /* font-size: 13px; */
  }

  .ant-menu-item:hover,
  .ant-menu-submenu-title:hover {
    .ant-menu-submenu-expand-icon,
    .ant-menu-submenu-arrow,
    span[role='img'],
    a,
    .ant-menu-item-icon,
    .ant-menu-title-content {
      color: var(--text-sider-primary-color);
      fill: var(--text-sider-primary-color);
    }
  }

  .ant-menu-submenu-selected {
    .ant-menu-submenu-title {
      color: var(--text-sider-primary-color);

      .ant-menu-submenu-expand-icon,
      .ant-menu-submenu-arrow,
      span[role='img'] {
        color: var(--text-sider-primary-color);
        fill: var(--text-sider-primary-color);
      }
    }
  }

  /* -------------------------------------------------------------- */
  .ant-menu-root.ant-menu-inline {
    box-shadow: none;
    margin-top: 12px !important;
  }

  .ant-menu-item-selected {
    background-color: white !important;
    width: 100% !important;
    height: 40px !important;
    padding: 10px 12px;
    border-radius: 10px !important;
    margin-left: 12px !important;
    margin-right: 5px !important;
    color: #2ca062;

    .ant-menu-submenu-expand-icon,
    .ant-menu-submenu-arrow,
    span[role='img'],
    .ant-menu-item-icon,
    a {
      color: #2ca062;
      fill: #2ca062;
    }
  }

  /* .ant-menu-inline .ant-menu-item::after,
  .ant-menu-vertical .ant-menu-item::after,
  .ant-menu-vertical-left .ant-menu-item::after,
  .ant-menu-vertical-right .ant-menu-item::after {
    border-right: none !important;
  }

   */

  .ant-menu-inline .ant-menu-item-selected::after,
  .ant-menu-inline .ant-menu-selected::after {
    opacity: 0 !important;
  }

  /* ------------------------------------------------------------ */

  .ant-menu-item-active,
  .ant-menu-submenu-active .ant-menu-submenu-title {
    background-color: transparent !important;
  }

  /* Add styles for active link */
  .ant-menu-item-selected a {
    color: #2ca062;
  }
`;
